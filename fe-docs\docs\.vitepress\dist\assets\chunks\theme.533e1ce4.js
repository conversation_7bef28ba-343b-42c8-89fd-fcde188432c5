import{d as y,o as a,c as l,r as d,n as N,a as B,t as C,_ as m,u as bt,b as i,g as kt,e as $t,f as wt,h as Le,i as Pt,j as L,w as q,k as W,l as Me,m as Ze,p as k,q as ee,s as Vt,v as le,x as St,P as Lt,y as Ce,z as te,A as ce,B as Te,C as _,F as I,D as $,E as p,G as g,T as Ae,H as X,I as pe,J as h,K as et,L as Mt,M as Ct,N as Q,O as tt,Q as Tt,R as D,S as G,U as j,V as At,W as Ke,X as xe,Y as fe,Z as xt,$ as se,a0 as It,a1 as Nt,a2 as Bt,a3 as Ht,a4 as Et}from"./framework.3d729ebc.js";const Dt=y({__name:"VPBadge",props:{text:{},type:{}},setup(t){return(e,n)=>(a(),l("span",{class:N(["VPBadge",e.type??"tip"])},[d(e.$slots,"default",{},()=>[B(C(e.text),1)],!0)],2))}});const zt=m(Dt,[["__scopeId","data-v-350d3852"]]),M=bt;function Ie(t){return kt()?($t(t),!0):!1}function F(t){return typeof t=="function"?t():i(t)}const nt=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const Ot=Object.prototype.toString,Ft=t=>Ot.call(t)==="[object Object]",ae=()=>{},qe=Gt();function Gt(){var t,e;return nt&&((t=window==null?void 0:window.navigator)==null?void 0:t.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((e=window==null?void 0:window.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function jt(t,e){function n(...o){return new Promise((s,r)=>{Promise.resolve(t(()=>e.apply(this,o),{fn:e,thisArg:this,args:o})).then(s).catch(r)})}return n}const ot=t=>t();function Wt(t,e={}){let n,o,s=ae;const r=v=>{clearTimeout(v),s(),s=ae};return v=>{const u=F(t),f=F(e.maxWait);return n&&r(n),u<=0||f!==void 0&&f<=0?(o&&(r(o),o=null),Promise.resolve(v())):new Promise((b,V)=>{s=e.rejectOnCancel?V:b,f&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,b(v())},f)),n=setTimeout(()=>{o&&r(o),o=null,b(v())},u)})}}function Rt(t=ot){const e=L(!0);function n(){e.value=!1}function o(){e.value=!0}const s=(...r)=>{e.value&&t(...r)};return{isActive:Le(e),pause:n,resume:o,eventFilter:s}}function Ut(t){return t||Ze()}function Kt(...t){if(t.length!==1)return wt(...t);const e=t[0];return typeof e=="function"?Le(Pt(()=>({get:e,set:ae}))):L(e)}function st(t,e,n={}){const{eventFilter:o=ot,...s}=n;return q(t,jt(o,e),s)}function qt(t,e,n={}){const{eventFilter:o,...s}=n,{eventFilter:r,pause:c,resume:v,isActive:u}=Rt(o);return{stop:st(t,e,{...s,eventFilter:r}),pause:c,resume:v,isActive:u}}function Yt(t,e=!0,n){Ut()?W(t,n):e?t():Me(t)}function Pl(t,e,n={}){const{debounce:o=0,maxWait:s=void 0,...r}=n;return st(t,e,{...r,eventFilter:Wt(o,{maxWait:s})})}function Vl(t,e,n){let o;Vt(n)?o={evaluating:n}:o=n||{};const{lazy:s=!1,evaluating:r=void 0,shallow:c=!0,onError:v=ae}=o,u=L(!s),f=c?le(e):L(e);let b=0;return ee(async V=>{if(!u.value)return;b++;const w=b;let P=!1;r&&Promise.resolve().then(()=>{r.value=!0});try{const T=await t(A=>{V(()=>{r&&(r.value=!1),P||A()})});w===b&&(f.value=T)}catch(T){v(T)}finally{r&&w===b&&(r.value=!1),P=!0}}),s?k(()=>(u.value=!0,f.value)):f}function Jt(t){var e;const n=F(t);return(e=n==null?void 0:n.$el)!=null?e:n}const K=nt?window:void 0;function re(...t){let e,n,o,s;if(typeof t[0]=="string"||Array.isArray(t[0])?([n,o,s]=t,e=K):[e,n,o,s]=t,!e)return ae;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const r=[],c=()=>{r.forEach(b=>b()),r.length=0},v=(b,V,w,P)=>(b.addEventListener(V,w,P),()=>b.removeEventListener(V,w,P)),u=q(()=>[Jt(e),F(s)],([b,V])=>{if(c(),!b)return;const w=Ft(V)?{...V}:V;r.push(...n.flatMap(P=>o.map(T=>v(b,P,T,w))))},{immediate:!0,flush:"post"}),f=()=>{u(),c()};return Ie(f),f}function Xt(t){return typeof t=="function"?t:typeof t=="string"?e=>e.key===t:Array.isArray(t)?e=>t.includes(e.key):()=>!0}function Qt(...t){let e,n,o={};t.length===3?(e=t[0],n=t[1],o=t[2]):t.length===2?typeof t[1]=="object"?(e=!0,n=t[0],o=t[1]):(e=t[0],n=t[1]):(e=!0,n=t[0]);const{target:s=K,eventName:r="keydown",passive:c=!1,dedupe:v=!1}=o,u=Xt(e);return re(s,r,b=>{b.repeat&&F(v)||u(b)&&n(b)},c)}function Zt(){const t=L(!1),e=Ze();return e&&W(()=>{t.value=!0},e),t}function en(t){const e=Zt();return k(()=>(e.value,!!t()))}function Pe(t,e={}){const{window:n=K}=e,o=en(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let s;const r=L(!1),c=f=>{r.value=f.matches},v=()=>{s&&("removeEventListener"in s?s.removeEventListener("change",c):s.removeListener(c))},u=ee(()=>{o.value&&(v(),s=n.matchMedia(F(t)),"addEventListener"in s?s.addEventListener("change",c):s.addListener(c),r.value=s.matches)});return Ie(()=>{u(),v(),s=void 0}),r}const _e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ve="__vueuse_ssr_handlers__",tn=nn();function nn(){return ve in _e||(_e[ve]=_e[ve]||{}),_e[ve]}function on(t,e){return tn[t]||e}function sn(t){return t==null?"any":t instanceof Set?"set":t instanceof Map?"map":t instanceof Date?"date":typeof t=="boolean"?"boolean":typeof t=="string"?"string":typeof t=="object"?"object":Number.isNaN(t)?"any":"number"}const an={boolean:{read:t=>t==="true",write:t=>String(t)},object:{read:t=>JSON.parse(t),write:t=>JSON.stringify(t)},number:{read:t=>Number.parseFloat(t),write:t=>String(t)},any:{read:t=>t,write:t=>String(t)},string:{read:t=>t,write:t=>String(t)},map:{read:t=>new Map(JSON.parse(t)),write:t=>JSON.stringify(Array.from(t.entries()))},set:{read:t=>new Set(JSON.parse(t)),write:t=>JSON.stringify(Array.from(t))},date:{read:t=>new Date(t),write:t=>t.toISOString()}},Ye="vueuse-storage";function at(t,e,n,o={}){var s;const{flush:r="pre",deep:c=!0,listenToStorageChanges:v=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:b,window:V=K,eventFilter:w,onError:P=x=>{console.error(x)},initOnMounted:T}=o,A=(b?le:L)(typeof e=="function"?e():e);if(!n)try{n=on("getDefaultStorage",()=>{var x;return(x=K)==null?void 0:x.localStorage})()}catch(x){P(x)}if(!n)return A;const S=F(e),H=sn(S),z=(s=o.serializer)!=null?s:an[H],{pause:O,resume:Re}=qt(A,()=>mt(A.value),{flush:r,deep:c,eventFilter:w});V&&v&&Yt(()=>{re(V,"storage",de),re(V,Ye,yt),T&&de()}),T||de();function Ue(x,E){V&&V.dispatchEvent(new CustomEvent(Ye,{detail:{key:t,oldValue:x,newValue:E,storageArea:n}}))}function mt(x){try{const E=n.getItem(t);if(x==null)Ue(E,null),n.removeItem(t);else{const U=z.write(x);E!==U&&(n.setItem(t,U),Ue(E,U))}}catch(E){P(E)}}function gt(x){const E=x?x.newValue:n.getItem(t);if(E==null)return u&&S!=null&&n.setItem(t,z.write(S)),S;if(!x&&f){const U=z.read(E);return typeof f=="function"?f(U,S):H==="object"&&!Array.isArray(U)?{...S,...U}:U}else return typeof E!="string"?E:z.read(E)}function de(x){if(!(x&&x.storageArea!==n)){if(x&&x.key==null){A.value=S;return}if(!(x&&x.key!==t)){O();try{(x==null?void 0:x.newValue)!==z.write(A.value)&&(A.value=gt(x))}catch(E){P(E)}finally{x?Me(Re):Re()}}}}function yt(x){de(x.detail)}return A}function ke(t){return typeof Window<"u"&&t instanceof Window?t.document.documentElement:typeof Document<"u"&&t instanceof Document?t.documentElement:t}function Sl(t,e,n={}){const{window:o=K}=n;return at(t,e,o==null?void 0:o.localStorage,n)}function rt(t){const e=window.getComputedStyle(t);if(e.overflowX==="scroll"||e.overflowY==="scroll"||e.overflowX==="auto"&&t.clientWidth<t.scrollWidth||e.overflowY==="auto"&&t.clientHeight<t.scrollHeight)return!0;{const n=t.parentNode;return!n||n.tagName==="BODY"?!1:rt(n)}}function rn(t){const e=t||window.event,n=e.target;return rt(n)?!1:e.touches.length>1?!0:(e.preventDefault&&e.preventDefault(),!1)}const $e=new WeakMap;function Ll(t,e=!1){const n=L(e);let o=null,s="";q(Kt(t),v=>{const u=ke(F(v));if(u){const f=u;if($e.get(f)||$e.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(s=f.style.overflow),f.style.overflow==="hidden")return n.value=!0;if(n.value)return f.style.overflow="hidden"}},{immediate:!0});const r=()=>{const v=ke(F(t));!v||n.value||(qe&&(o=re(v,"touchmove",u=>{rn(u)},{passive:!1})),v.style.overflow="hidden",n.value=!0)},c=()=>{const v=ke(F(t));!v||!n.value||(qe&&(o==null||o()),v.style.overflow=s,$e.delete(v),n.value=!1)};return Ie(c),k({get(){return n.value},set(v){v?r():c()}})}function Ml(t,e,n={}){const{window:o=K}=n;return at(t,e,o==null?void 0:o.sessionStorage,n)}function ln(t={}){const{window:e=K,behavior:n="auto"}=t;if(!e)return{x:L(0),y:L(0)};const o=L(e.scrollX),s=L(e.scrollY),r=k({get(){return o.value},set(v){scrollTo({left:v,behavior:n})}}),c=k({get(){return s.value},set(v){scrollTo({top:v,behavior:n})}});return re(e,"scroll",()=>{o.value=e.scrollX,s.value=e.scrollY},{capture:!1,passive:!0}),{x:r,y:c}}function cn(t,e){let n,o=!1;return()=>{n&&clearTimeout(n),o?n=setTimeout(t,e):(t(),o=!0,setTimeout(()=>{o=!1},e))}}function Ve(t){return/^\//.test(t)?t:`/${t}`}function ie(t){if(St(t))return t.replace(Lt,"");const{site:e}=M(),{pathname:n,search:o,hash:s}=new URL(t,"http://example.com"),r=n.endsWith("/")||n.endsWith(".html")?t:t.replace(/(?:(^\.+)\/)?.*$/,`$1${n.replace(/(\.md)?$/,e.value.cleanUrls?"":".html")}${o}${s}`);return Ce(r)}function it(t,e){if(Array.isArray(t))return t;if(t==null)return[];e=Ve(e);const n=Object.keys(t).sort((o,s)=>s.split("/").length-o.split("/").length).find(o=>e.startsWith(Ve(o)));return n?t[n]:[]}function un(t){const e=[];let n=0;for(const o in t){const s=t[o];if(s.items){n=e.push(s);continue}e[n]||e.push({items:[]}),e[n].items.push(s)}return e}function dn(t){const e=[];function n(o){for(const s of o)s.text&&s.link&&e.push({text:s.text,link:s.link}),s.items&&n(s.items)}return n(t),e}function Se(t,e){return Array.isArray(e)?e.some(n=>Se(t,n)):te(t,e.link)?!0:e.items?Se(t,e.items):!1}function R(){const t=ce(),{theme:e,frontmatter:n}=M(),o=Pe("(min-width: 960px)"),s=L(!1),r=k(()=>{const T=e.value.sidebar,A=t.data.relativePath;return T?it(T,A):[]}),c=k(()=>n.value.sidebar!==!1&&r.value.length>0&&n.value.layout!=="home"),v=k(()=>u?n.value.aside==null?e.value.aside==="left":n.value.aside==="left":!1),u=k(()=>n.value.layout==="home"?!1:n.value.aside!=null?!!n.value.aside:e.value.aside!==!1),f=k(()=>c.value&&o.value),b=k(()=>c.value?un(r.value):[]);function V(){s.value=!0}function w(){s.value=!1}function P(){s.value?w():V()}return{isOpen:s,sidebar:r,sidebarGroups:b,hasSidebar:c,hasAside:u,leftAside:v,isSidebarEnabled:f,open:V,close:w,toggle:P}}function _n(t,e){let n;ee(()=>{n=t.value?document.activeElement:void 0}),W(()=>{window.addEventListener("keyup",o)}),Te(()=>{window.removeEventListener("keyup",o)});function o(s){s.key==="Escape"&&t.value&&(e(),n==null||n.focus())}}function vn(t){const{page:e}=M(),n=L(!1),o=k(()=>t.value.collapsed!=null),s=k(()=>!!t.value.link),r=k(()=>te(e.value.relativePath,t.value.link)),c=k(()=>r.value?!0:t.value.items?Se(e.value.relativePath,t.value.items):!1),v=k(()=>!!(t.value.items&&t.value.items.length));ee(()=>{n.value=!!(o.value&&t.value.collapsed)}),ee(()=>{(r.value||c.value)&&(n.value=!1)});function u(){o.value&&(n.value=!n.value)}return{collapsed:n,collapsible:o,isLink:s,isActiveLink:r,hasActiveLink:c,hasChildren:v,toggle:u}}const pn=y({__name:"VPSkipLink",setup(t){const e=ce(),n=L();q(()=>e.path,()=>n.value.focus());function o({target:s}){const r=document.querySelector(decodeURIComponent(s.hash));if(r){const c=()=>{r.removeAttribute("tabindex"),r.removeEventListener("blur",c)};r.setAttribute("tabindex","-1"),r.addEventListener("blur",c),r.focus(),window.scrollTo(0,0)}}return(s,r)=>(a(),l(I,null,[_("span",{ref_key:"backToTop",ref:n,tabindex:"-1"},null,512),_("a",{href:"#VPContent",class:"VPSkipLink visually-hidden",onClick:o}," Skip to content ")],64))}});const fn=m(pn,[["__scopeId","data-v-c8616af1"]]),hn={key:0,class:"VPBackdrop"},mn=y({__name:"VPBackdrop",props:{show:{type:Boolean}},setup(t){return(e,n)=>(a(),$(Ae,{name:"fade"},{default:p(()=>[e.show?(a(),l("div",hn)):g("",!0)]),_:1}))}});const gn=m(mn,[["__scopeId","data-v-c79a1216"]]);function yn(){const t=L(!1);function e(){t.value=!0,window.addEventListener("resize",s)}function n(){t.value=!1,window.removeEventListener("resize",s)}function o(){t.value?n():e()}function s(){window.outerWidth>=768&&n()}const r=ce();return q(()=>r.path,n),{isScreenOpen:t,openScreen:e,closeScreen:n,toggleScreen:o}}function ue({removeCurrent:t=!0,correspondingLink:e=!1}={}){const{site:n,localeIndex:o,page:s,theme:r}=M(),c=k(()=>{var u,f;return{label:(u=n.value.locales[o.value])==null?void 0:u.label,link:((f=n.value.locales[o.value])==null?void 0:f.link)||(o.value==="root"?"/":`/${o.value}/`)}});return{localeLinks:k(()=>Object.entries(n.value.locales).flatMap(([u,f])=>t&&c.value.label===f.label?[]:{text:f.label,link:bn(f.link||(u==="root"?"/":`/${u}/`),r.value.i18nRouting!==!1&&e,s.value.relativePath.slice(c.value.link.length-1),!n.value.cleanUrls)})),currentLang:c}}function bn(t,e,n,o){return e?t.replace(/\/$/,"")+Ve(n.replace(/(^|\/)?index.md$/,"$1").replace(/\.md$/,o?".html":"")):t}const kn=["src","alt"],$n={inheritAttrs:!1},wn=y({...$n,__name:"VPImage",props:{image:{},alt:{}},setup(t){return(e,n)=>{const o=X("VPImage",!0);return e.image?(a(),l(I,{key:0},[typeof e.image=="string"||"src"in e.image?(a(),l("img",pe({key:0,class:"VPImage"},typeof e.image=="string"?e.$attrs:{...e.image,...e.$attrs},{src:i(Ce)(typeof e.image=="string"?e.image:e.image.src),alt:e.alt??(typeof e.image=="string"?"":e.image.alt||"")}),null,16,kn)):(a(),l(I,{key:1},[h(o,pe({class:"dark",image:e.image.dark,alt:e.image.alt},e.$attrs),null,16,["image","alt"]),h(o,pe({class:"light",image:e.image.light,alt:e.image.alt},e.$attrs),null,16,["image","alt"])],64))],64)):g("",!0)}}});const Ne=m(wn,[["__scopeId","data-v-6db2186b"]]),Pn=["href"],Vn=y({__name:"VPNavBarTitle",setup(t){const{site:e,theme:n}=M(),{hasSidebar:o}=R(),{currentLang:s}=ue();return(r,c)=>(a(),l("div",{class:N(["VPNavBarTitle",{"has-sidebar":i(o)}])},[_("a",{class:"title",href:i(ie)(i(s).link)},[d(r.$slots,"nav-bar-title-before",{},void 0,!0),i(n).logo?(a(),$(Ne,{key:0,class:"logo",image:i(n).logo},null,8,["image"])):g("",!0),i(n).siteTitle?(a(),l(I,{key:1},[B(C(i(n).siteTitle),1)],64)):i(n).siteTitle===void 0?(a(),l(I,{key:2},[B(C(i(e).title),1)],64)):g("",!0),d(r.$slots,"nav-bar-title-after",{},void 0,!0)],8,Pn)],2))}});const Sn=m(Vn,[["__scopeId","data-v-f4ef19a3"]]);const Ln={type:"button",class:"DocSearch DocSearch-Button","aria-label":"Search"},Mn={class:"DocSearch-Button-Container"},Cn=_("svg",{class:"DocSearch-Search-Icon",width:"20",height:"20",viewBox:"0 0 20 20","aria-label":"search icon"},[_("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none","fill-rule":"evenodd","stroke-linecap":"round","stroke-linejoin":"round"})],-1),Tn={class:"DocSearch-Button-Placeholder"},An=_("span",{class:"DocSearch-Button-Keys"},[_("kbd",{class:"DocSearch-Button-Key"}),_("kbd",{class:"DocSearch-Button-Key"},"K")],-1),Je=y({__name:"VPNavBarSearchButton",props:{placeholder:{}},setup(t){return(e,n)=>(a(),l("button",Ln,[_("span",Mn,[Cn,_("span",Tn,C(e.placeholder),1)]),An]))}});const xn={id:"local-search"},In={key:1,id:"docsearch"},Nn=y({__name:"VPNavBarSearch",setup(t){const e=Mt(()=>Ct(()=>import("./VPLocalSearchBox.7e20dcd6.js"),["assets/chunks/VPLocalSearchBox.7e20dcd6.js","assets/chunks/framework.3d729ebc.js"])),n=()=>null,{theme:o,localeIndex:s}=M(),r=L(!1),c=k(()=>{var P,T,A,S,H,z,O;const w=((P=o.value.search)==null?void 0:P.options)??o.value.algolia;return((H=(S=(A=(T=w==null?void 0:w.locales)==null?void 0:T[s.value])==null?void 0:A.translations)==null?void 0:S.button)==null?void 0:H.buttonText)||((O=(z=w==null?void 0:w.translations)==null?void 0:z.button)==null?void 0:O.buttonText)||"Search"});W(()=>{});function v(){r.value||(r.value=!0,setTimeout(u,16))}function u(){const w=new Event("keydown");w.key="k",w.metaKey=!0,window.dispatchEvent(w),setTimeout(()=>{document.querySelector(".DocSearch-Modal")||u()},16)}const f=L(!1);Qt("k",w=>{(w.ctrlKey||w.metaKey)&&(w.preventDefault(),f.value=!0)});const b=L("'Meta'");W(()=>{b.value=/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?"'⌘'":"'Ctrl'"});const V="local";return(w,P)=>{var T;return a(),l("div",{class:"VPNavBarSearch",style:et({"--vp-meta-key":b.value})},[i(V)==="local"?(a(),l(I,{key:0},[f.value?(a(),$(i(e),{key:0,placeholder:c.value,onClose:P[0]||(P[0]=A=>f.value=!1)},null,8,["placeholder"])):g("",!0),_("div",xn,[h(Je,{placeholder:c.value,onClick:P[1]||(P[1]=A=>f.value=!0)},null,8,["placeholder"])])],64)):i(V)==="algolia"?(a(),l(I,{key:1},[r.value?(a(),$(i(n),{key:0,algolia:((T=i(o).search)==null?void 0:T.options)??i(o).algolia},null,8,["algolia"])):(a(),l("div",In,[h(Je,{placeholder:c.value,onClick:v},null,8,["placeholder"])]))],64)):g("",!0)],4)}}});const Bn={},Hn={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",height:"24px",viewBox:"0 0 24 24",width:"24px"},En=_("path",{d:"M0 0h24v24H0V0z",fill:"none"},null,-1),Dn=_("path",{d:"M9 5v2h6.59L4 18.59 5.41 20 17 8.41V15h2V5H9z"},null,-1),zn=[En,Dn];function On(t,e){return a(),l("svg",Hn,zn)}const Fn=m(Bn,[["render",On]]),Gn=y({__name:"VPLink",props:{tag:{},href:{},noIcon:{type:Boolean},target:{},rel:{}},setup(t){const e=t,n=k(()=>e.tag??e.href?"a":"span"),o=k(()=>e.href&&tt.test(e.href));return(s,r)=>(a(),$(Q(n.value),{class:N(["VPLink",{link:s.href}]),href:s.href?i(ie)(s.href):void 0,target:s.target||(o.value?"_blank":void 0),rel:s.rel||(o.value?"noreferrer":void 0)},{default:p(()=>[d(s.$slots,"default",{},void 0,!0),o.value&&!s.noIcon?(a(),$(Fn,{key:0,class:"icon"})):g("",!0)]),_:3},8,["class","href","target","rel"]))}});const Y=m(Gn,[["__scopeId","data-v-8f4dc553"]]),jn=y({__name:"VPNavBarMenuLink",props:{item:{}},setup(t){const{page:e}=M();return(n,o)=>(a(),$(Y,{class:N({VPNavBarMenuLink:!0,active:i(te)(i(e).relativePath,n.item.activeMatch||n.item.link,!!n.item.activeMatch)}),href:n.item.link,target:n.item.target,rel:n.item.rel,tabindex:"0"},{default:p(()=>[B(C(n.item.text),1)]),_:1},8,["class","href","target","rel"]))}});const Wn=m(jn,[["__scopeId","data-v-37adc828"]]),Be=L();let lt=!1,we=0;function Rn(t){const e=L(!1);if(Tt){!lt&&Un(),we++;const n=q(Be,o=>{var s,r,c;o===t.el.value||(s=t.el.value)!=null&&s.contains(o)?(e.value=!0,(r=t.onFocus)==null||r.call(t)):(e.value=!1,(c=t.onBlur)==null||c.call(t))});Te(()=>{n(),we--,we||Kn()})}return Le(e)}function Un(){document.addEventListener("focusin",ct),lt=!0,Be.value=document.activeElement}function Kn(){document.removeEventListener("focusin",ct)}function ct(){Be.value=document.activeElement}const qn={},Yn={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Jn=_("path",{d:"M12,16c-0.3,0-0.5-0.1-0.7-0.3l-6-6c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l5.3,5.3l5.3-5.3c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-6,6C12.5,15.9,12.3,16,12,16z"},null,-1),Xn=[Jn];function Qn(t,e){return a(),l("svg",Yn,Xn)}const ut=m(qn,[["render",Qn]]),Zn={},eo={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},to=_("circle",{cx:"12",cy:"12",r:"2"},null,-1),no=_("circle",{cx:"19",cy:"12",r:"2"},null,-1),oo=_("circle",{cx:"5",cy:"12",r:"2"},null,-1),so=[to,no,oo];function ao(t,e){return a(),l("svg",eo,so)}const ro=m(Zn,[["render",ao]]),io={class:"VPMenuLink"},lo=y({__name:"VPMenuLink",props:{item:{}},setup(t){const{page:e}=M();return(n,o)=>(a(),l("div",io,[h(Y,{class:N({active:i(te)(i(e).relativePath,n.item.activeMatch||n.item.link,!!n.item.activeMatch)}),href:n.item.link,target:n.item.target,rel:n.item.rel},{default:p(()=>[B(C(n.item.text),1)]),_:1},8,["class","href","target","rel"])]))}});const ye=m(lo,[["__scopeId","data-v-d2c93bab"]]),co={class:"VPMenuGroup"},uo={key:0,class:"title"},_o=y({__name:"VPMenuGroup",props:{text:{},items:{}},setup(t){return(e,n)=>(a(),l("div",co,[e.text?(a(),l("p",uo,C(e.text),1)):g("",!0),(a(!0),l(I,null,D(e.items,o=>(a(),l(I,null,["link"in o?(a(),$(ye,{key:0,item:o},null,8,["item"])):g("",!0)],64))),256))]))}});const vo=m(_o,[["__scopeId","data-v-69e747b5"]]),po={class:"VPMenu"},fo={key:0,class:"items"},ho=y({__name:"VPMenu",props:{items:{}},setup(t){return(e,n)=>(a(),l("div",po,[e.items?(a(),l("div",fo,[(a(!0),l(I,null,D(e.items,o=>(a(),l(I,{key:o.text},["link"in o?(a(),$(ye,{key:0,item:o},null,8,["item"])):(a(),$(vo,{key:1,text:o.text,items:o.items},null,8,["text","items"]))],64))),128))])):g("",!0),d(e.$slots,"default",{},void 0,!0)]))}});const mo=m(ho,[["__scopeId","data-v-e7ea1737"]]),go=["aria-expanded","aria-label"],yo={key:0,class:"text"},bo={class:"menu"},ko=y({__name:"VPFlyout",props:{icon:{},button:{},label:{},items:{}},setup(t){const e=L(!1),n=L();Rn({el:n,onBlur:o});function o(){e.value=!1}return(s,r)=>(a(),l("div",{class:"VPFlyout",ref_key:"el",ref:n,onMouseenter:r[1]||(r[1]=c=>e.value=!0),onMouseleave:r[2]||(r[2]=c=>e.value=!1)},[_("button",{type:"button",class:"button","aria-haspopup":"true","aria-expanded":e.value,"aria-label":s.label,onClick:r[0]||(r[0]=c=>e.value=!e.value)},[s.button||s.icon?(a(),l("span",yo,[s.icon?(a(),$(Q(s.icon),{key:0,class:"option-icon"})):g("",!0),B(" "+C(s.button)+" ",1),h(ut,{class:"text-icon"})])):(a(),$(ro,{key:1,class:"icon"}))],8,go),_("div",bo,[h(mo,{items:s.items},{default:p(()=>[d(s.$slots,"default",{},void 0,!0)]),_:3},8,["items"])])],544))}});const He=m(ko,[["__scopeId","data-v-764effdf"]]),$o=y({__name:"VPNavBarMenuGroup",props:{item:{}},setup(t){const{page:e}=M();return(n,o)=>(a(),$(He,{class:N({VPNavBarMenuGroup:!0,active:i(te)(i(e).relativePath,n.item.activeMatch,!!n.item.activeMatch)}),button:n.item.text,items:n.item.items},null,8,["class","button","items"]))}}),wo=t=>(G("data-v-7f418b0f"),t=t(),j(),t),Po={key:0,"aria-labelledby":"main-nav-aria-label",class:"VPNavBarMenu"},Vo=wo(()=>_("span",{id:"main-nav-aria-label",class:"visually-hidden"},"Main Navigation",-1)),So=y({__name:"VPNavBarMenu",setup(t){const{theme:e}=M();return(n,o)=>i(e).nav?(a(),l("nav",Po,[Vo,(a(!0),l(I,null,D(i(e).nav,s=>(a(),l(I,{key:s.text},["link"in s?(a(),$(Wn,{key:0,item:s},null,8,["item"])):(a(),$($o,{key:1,item:s},null,8,["item"]))],64))),128))])):g("",!0)}});const Lo=m(So,[["__scopeId","data-v-7f418b0f"]]),Mo={},Co={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},To=_("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1),Ao=_("path",{d:" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z ",class:"css-c4d79v"},null,-1),xo=[To,Ao];function Io(t,e){return a(),l("svg",Co,xo)}const dt=m(Mo,[["render",Io]]),No={class:"items"},Bo={class:"title"},Ho=y({__name:"VPNavBarTranslations",setup(t){const{theme:e}=M(),{localeLinks:n,currentLang:o}=ue({correspondingLink:!0});return(s,r)=>i(n).length&&i(o).label?(a(),$(He,{key:0,class:"VPNavBarTranslations",icon:dt,label:i(e).langMenuLabel||"Change language"},{default:p(()=>[_("div",No,[_("p",Bo,C(i(o).label),1),(a(!0),l(I,null,D(i(n),c=>(a(),$(ye,{key:c.link,item:c},null,8,["item"]))),128))])]),_:1},8,["label"])):g("",!0)}});const Eo=m(Ho,[["__scopeId","data-v-74abcbb9"]]);const Do={},zo={class:"VPSwitch",type:"button",role:"switch"},Oo={class:"check"},Fo={key:0,class:"icon"};function Go(t,e){return a(),l("button",zo,[_("span",Oo,[t.$slots.default?(a(),l("span",Fo,[d(t.$slots,"default",{},void 0,!0)])):g("",!0)])])}const jo=m(Do,[["render",Go],["__scopeId","data-v-f3c41672"]]),Wo={},Ro={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Uo=At('<path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path>',9),Ko=[Uo];function qo(t,e){return a(),l("svg",Ro,Ko)}const Yo=m(Wo,[["render",qo]]),Jo={},Xo={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Qo=_("path",{d:"M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"},null,-1),Zo=[Qo];function es(t,e){return a(),l("svg",Xo,Zo)}const ts=m(Jo,[["render",es]]),ns={title:"toggle dark mode"},os=y({__name:"VPSwitchAppearance",setup(t){const{site:e,isDark:n}=M(),o=L(!1),s=typeof localStorage<"u"?r():()=>{};W(()=>{o.value=document.documentElement.classList.contains("dark")});function r(){const c=window.matchMedia("(prefers-color-scheme: dark)"),v=document.documentElement.classList;let u=localStorage.getItem(Ke),f=e.value.appearance==="dark"&&u==null||(u==="auto"||u==null?c.matches:u==="dark");c.onchange=w=>{u==="auto"&&V(f=w.matches)};function b(){V(f=!f),u=f?c.matches?"auto":"dark":c.matches?"light":"auto",localStorage.setItem(Ke,u)}function V(w){const P=document.createElement("style");P.type="text/css",P.appendChild(document.createTextNode(`:not(.VPSwitchAppearance):not(.VPSwitchAppearance *) {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;
}`)),document.head.appendChild(P),o.value=w,v[w?"add":"remove"]("dark"),window.getComputedStyle(P).opacity,document.head.removeChild(P)}return b}return q(o,c=>{n.value=c}),(c,v)=>(a(),l("label",ns,[h(jo,{class:"VPSwitchAppearance","aria-checked":o.value,onClick:i(s)},{default:p(()=>[h(Yo,{class:"sun"}),h(ts,{class:"moon"})]),_:1},8,["aria-checked","onClick"])]))}});const Ee=m(os,[["__scopeId","data-v-a9c8afb8"]]),ss={key:0,class:"VPNavBarAppearance"},as=y({__name:"VPNavBarAppearance",setup(t){const{site:e}=M();return(n,o)=>i(e).appearance?(a(),l("div",ss,[h(Ee)])):g("",!0)}});const rs=m(as,[["__scopeId","data-v-f6a63727"]]),is={discord:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Discord</title><path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/></svg>',facebook:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Facebook</title><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>',github:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>GitHub</title><path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"/></svg>',instagram:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Instagram</title><path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/></svg>',linkedin:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>LinkedIn</title><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>',mastodon:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Mastodon</title><path d="M23.268 5.313c-.35-2.578-2.617-4.61-5.304-5.004C17.51.242 15.792 0 11.813 0h-.03c-3.98 0-4.835.242-5.288.309C3.882.692 1.496 2.518.917 5.127.64 6.412.61 7.837.661 9.143c.074 1.874.088 3.745.26 5.611.118 1.24.325 2.47.62 3.68.55 2.237 2.777 4.098 4.96 4.857 2.336.792 4.849.923 7.256.38.265-.061.527-.132.786-.213.585-.184 1.27-.39 1.774-.753a.057.057 0 0 0 .023-.043v-1.809a.052.052 0 0 0-.02-.041.053.053 0 0 0-.046-.01 20.282 20.282 0 0 1-4.709.545c-2.73 0-3.463-1.284-3.674-1.818a5.593 5.593 0 0 1-.319-1.433.053.053 0 0 1 .066-.054c1.517.363 3.072.546 4.632.546.376 0 .75 0 1.125-.01 1.57-.044 3.224-.124 4.768-.422.038-.008.077-.015.11-.024 2.435-.464 4.753-1.92 4.989-5.604.008-.145.03-1.52.03-1.67.002-.512.167-3.63-.024-5.545zm-3.748 9.195h-2.561V8.29c0-1.309-.55-1.976-1.67-1.976-1.23 0-1.846.79-1.846 2.35v3.403h-2.546V8.663c0-1.56-.617-2.35-1.848-2.35-1.112 0-1.668.668-1.67 1.977v6.218H4.822V8.102c0-1.31.337-2.35 1.011-3.12.696-.77 1.608-1.164 2.74-1.164 1.311 0 2.302.5 2.962 1.498l.638 1.06.638-1.06c.66-.999 1.65-1.498 2.96-1.498 1.13 0 2.043.395 2.74 1.164.675.77 1.012 1.81 1.012 3.12z"/></svg>',slack:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Slack</title><path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z"/></svg>',twitter:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Twitter</title><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>',youtube:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>YouTube</title><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>'},ls=["href","aria-label","innerHTML"],cs=y({__name:"VPSocialLink",props:{icon:{},link:{}},setup(t){const e=t,n=k(()=>typeof e.icon=="object"?e.icon.svg:is[e.icon]);return(o,s)=>(a(),l("a",{class:"VPSocialLink",href:o.link,"aria-label":typeof o.icon=="string"?o.icon:"",target:"_blank",rel:"noopener",innerHTML:n.value},null,8,ls))}});const us=m(cs,[["__scopeId","data-v-c530cc0a"]]),ds={class:"VPSocialLinks"},_s=y({__name:"VPSocialLinks",props:{links:{}},setup(t){return(e,n)=>(a(),l("div",ds,[(a(!0),l(I,null,D(e.links,({link:o,icon:s})=>(a(),$(us,{key:o,icon:s,link:o},null,8,["icon","link"]))),128))]))}});const De=m(_s,[["__scopeId","data-v-f6988cfb"]]),vs=y({__name:"VPNavBarSocialLinks",setup(t){const{theme:e}=M();return(n,o)=>i(e).socialLinks?(a(),$(De,{key:0,class:"VPNavBarSocialLinks",links:i(e).socialLinks},null,8,["links"])):g("",!0)}});const ps=m(vs,[["__scopeId","data-v-0394ad82"]]),fs={key:0,class:"group translations"},hs={class:"trans-title"},ms={key:1,class:"group"},gs={class:"item appearance"},ys={class:"label"},bs={class:"appearance-action"},ks={key:2,class:"group"},$s={class:"item social-links"},ws=y({__name:"VPNavBarExtra",setup(t){const{site:e,theme:n}=M(),{localeLinks:o,currentLang:s}=ue({correspondingLink:!0}),r=k(()=>o.value.length&&s.value.label||e.value.appearance||n.value.socialLinks);return(c,v)=>r.value?(a(),$(He,{key:0,class:"VPNavBarExtra",label:"extra navigation"},{default:p(()=>[i(o).length&&i(s).label?(a(),l("div",fs,[_("p",hs,C(i(s).label),1),(a(!0),l(I,null,D(i(o),u=>(a(),$(ye,{key:u.link,item:u},null,8,["item"]))),128))])):g("",!0),i(e).appearance?(a(),l("div",ms,[_("div",gs,[_("p",ys,C(i(n).darkModeSwitchLabel||"Appearance"),1),_("div",bs,[h(Ee)])])])):g("",!0),i(n).socialLinks?(a(),l("div",ks,[_("div",$s,[h(De,{class:"social-links-list",links:i(n).socialLinks},null,8,["links"])])])):g("",!0)]),_:1})):g("",!0)}});const Ps=m(ws,[["__scopeId","data-v-40855f84"]]),Vs=t=>(G("data-v-e5dd9c1c"),t=t(),j(),t),Ss=["aria-expanded"],Ls=Vs(()=>_("span",{class:"container"},[_("span",{class:"top"}),_("span",{class:"middle"}),_("span",{class:"bottom"})],-1)),Ms=[Ls],Cs=y({__name:"VPNavBarHamburger",props:{active:{type:Boolean}},emits:["click"],setup(t){return(e,n)=>(a(),l("button",{type:"button",class:N(["VPNavBarHamburger",{active:e.active}]),"aria-label":"mobile navigation","aria-expanded":e.active,"aria-controls":"VPNavScreen",onClick:n[0]||(n[0]=o=>e.$emit("click"))},Ms,10,Ss))}});const Ts=m(Cs,[["__scopeId","data-v-e5dd9c1c"]]),As=t=>(G("data-v-94c81dcc"),t=t(),j(),t),xs={class:"container"},Is={class:"title"},Ns={class:"content"},Bs=As(()=>_("div",{class:"curtain"},null,-1)),Hs={class:"content-body"},Es=y({__name:"VPNavBar",props:{isScreenOpen:{type:Boolean}},emits:["toggle-screen"],setup(t){const{y:e}=ln(),{hasSidebar:n}=R(),o=k(()=>({"has-sidebar":n.value,fill:e.value>0}));return(s,r)=>(a(),l("div",{class:N(["VPNavBar",o.value])},[_("div",xs,[_("div",Is,[h(Sn,null,{"nav-bar-title-before":p(()=>[d(s.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":p(()=>[d(s.$slots,"nav-bar-title-after",{},void 0,!0)]),_:3})]),_("div",Ns,[Bs,_("div",Hs,[d(s.$slots,"nav-bar-content-before",{},void 0,!0),h(Nn,{class:"search"}),h(Lo,{class:"menu"}),h(Eo,{class:"translations"}),h(rs,{class:"appearance"}),h(ps,{class:"social-links"}),h(Ps,{class:"extra"}),d(s.$slots,"nav-bar-content-after",{},void 0,!0),h(Ts,{class:"hamburger",active:s.isScreenOpen,onClick:r[0]||(r[0]=c=>s.$emit("toggle-screen"))},null,8,["active"])])])])],2))}});const Ds=m(Es,[["__scopeId","data-v-94c81dcc"]]);function zs(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}else return Array.from(t)}var ze=!1;if(typeof window<"u"){var Xe={get passive(){ze=!0}};window.addEventListener("testPassive",null,Xe),window.removeEventListener("testPassive",null,Xe)}var he=typeof window<"u"&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1),Z=[],me=!1,Oe=-1,ne=void 0,J=void 0,oe=void 0,_t=function(e){return Z.some(function(n){return!!(n.options.allowTouchMove&&n.options.allowTouchMove(e))})},ge=function(e){var n=e||window.event;return _t(n.target)||n.touches.length>1?!0:(n.preventDefault&&n.preventDefault(),!1)},Os=function(e){if(oe===void 0){var n=!!e&&e.reserveScrollBarGap===!0,o=window.innerWidth-document.documentElement.clientWidth;if(n&&o>0){var s=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right"),10);oe=document.body.style.paddingRight,document.body.style.paddingRight=s+o+"px"}}ne===void 0&&(ne=document.body.style.overflow,document.body.style.overflow="hidden")},Fs=function(){oe!==void 0&&(document.body.style.paddingRight=oe,oe=void 0),ne!==void 0&&(document.body.style.overflow=ne,ne=void 0)},Gs=function(){return window.requestAnimationFrame(function(){if(J===void 0){J={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left};var e=window,n=e.scrollY,o=e.scrollX,s=e.innerHeight;document.body.style.position="fixed",document.body.style.top=-n,document.body.style.left=-o,setTimeout(function(){return window.requestAnimationFrame(function(){var r=s-window.innerHeight;r&&n>=s&&(document.body.style.top=-(n+r))})},300)}})},js=function(){if(J!==void 0){var e=-parseInt(document.body.style.top,10),n=-parseInt(document.body.style.left,10);document.body.style.position=J.position,document.body.style.top=J.top,document.body.style.left=J.left,window.scrollTo(n,e),J=void 0}},Ws=function(e){return e?e.scrollHeight-e.scrollTop<=e.clientHeight:!1},Rs=function(e,n){var o=e.targetTouches[0].clientY-Oe;return _t(e.target)?!1:n&&n.scrollTop===0&&o>0||Ws(n)&&o<0?ge(e):(e.stopPropagation(),!0)},vt=function(e,n){if(!e){console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");return}if(!Z.some(function(s){return s.targetElement===e})){var o={targetElement:e,options:n||{}};Z=[].concat(zs(Z),[o]),he?Gs():Os(n),he&&(e.ontouchstart=function(s){s.targetTouches.length===1&&(Oe=s.targetTouches[0].clientY)},e.ontouchmove=function(s){s.targetTouches.length===1&&Rs(s,e)},me||(document.addEventListener("touchmove",ge,ze?{passive:!1}:void 0),me=!0))}},pt=function(){he&&(Z.forEach(function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null}),me&&(document.removeEventListener("touchmove",ge,ze?{passive:!1}:void 0),me=!1),Oe=-1),he?js():Fs(),Z=[]};const Us=y({__name:"VPNavScreenMenuLink",props:{text:{},link:{}},setup(t){const e=xe("close-screen");return(n,o)=>(a(),$(Y,{class:"VPNavScreenMenuLink",href:n.link,onClick:i(e)},{default:p(()=>[B(C(n.text),1)]),_:1},8,["href","onClick"]))}});const Ks=m(Us,[["__scopeId","data-v-c328f34f"]]),qs={},Ys={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Js=_("path",{d:"M18.9,10.9h-6v-6c0-0.6-0.4-1-1-1s-1,0.4-1,1v6h-6c-0.6,0-1,0.4-1,1s0.4,1,1,1h6v6c0,0.6,0.4,1,1,1s1-0.4,1-1v-6h6c0.6,0,1-0.4,1-1S19.5,10.9,18.9,10.9z"},null,-1),Xs=[Js];function Qs(t,e){return a(),l("svg",Ys,Xs)}const Zs=m(qs,[["render",Qs]]),ea=y({__name:"VPNavScreenMenuGroupLink",props:{text:{},link:{}},setup(t){const e=xe("close-screen");return(n,o)=>(a(),$(Y,{class:"VPNavScreenMenuGroupLink",href:n.link,onClick:i(e)},{default:p(()=>[B(C(n.text),1)]),_:1},8,["href","onClick"]))}});const ft=m(ea,[["__scopeId","data-v-3d20956d"]]),ta={class:"VPNavScreenMenuGroupSection"},na={key:0,class:"title"},oa=y({__name:"VPNavScreenMenuGroupSection",props:{text:{},items:{}},setup(t){return(e,n)=>(a(),l("div",ta,[e.text?(a(),l("p",na,C(e.text),1)):g("",!0),(a(!0),l(I,null,D(e.items,o=>(a(),$(ft,{key:o.text,text:o.text,link:o.link},null,8,["text","link"]))),128))]))}});const sa=m(oa,[["__scopeId","data-v-7478538b"]]),aa=["aria-controls","aria-expanded"],ra={class:"button-text"},ia=["id"],la={key:1,class:"group"},ca=y({__name:"VPNavScreenMenuGroup",props:{text:{},items:{}},setup(t){const e=t,n=L(!1),o=k(()=>`NavScreenGroup-${e.text.replace(" ","-").toLowerCase()}`);function s(){n.value=!n.value}return(r,c)=>(a(),l("div",{class:N(["VPNavScreenMenuGroup",{open:n.value}])},[_("button",{class:"button","aria-controls":o.value,"aria-expanded":n.value,onClick:s},[_("span",ra,C(r.text),1),h(Zs,{class:"button-icon"})],8,aa),_("div",{id:o.value,class:"items"},[(a(!0),l(I,null,D(r.items,v=>(a(),l(I,{key:v.text},["link"in v?(a(),l("div",{key:v.text,class:"item"},[h(ft,{text:v.text,link:v.link},null,8,["text","link"])])):(a(),l("div",la,[h(sa,{text:v.text,items:v.items},null,8,["text","items"])]))],64))),128))],8,ia)],2))}});const ua=m(ca,[["__scopeId","data-v-a9a19324"]]),da={key:0,class:"VPNavScreenMenu"},_a=y({__name:"VPNavScreenMenu",setup(t){const{theme:e}=M();return(n,o)=>i(e).nav?(a(),l("nav",da,[(a(!0),l(I,null,D(i(e).nav,s=>(a(),l(I,{key:s.text},["link"in s?(a(),$(Ks,{key:0,text:s.text,link:s.link},null,8,["text","link"])):(a(),$(ua,{key:1,text:s.text||"",items:s.items},null,8,["text","items"]))],64))),128))])):g("",!0)}}),va={key:0,class:"VPNavScreenAppearance"},pa={class:"text"},fa=y({__name:"VPNavScreenAppearance",setup(t){const{site:e,theme:n}=M();return(o,s)=>i(e).appearance?(a(),l("div",va,[_("p",pa,C(i(n).darkModeSwitchLabel||"Appearance"),1),h(Ee)])):g("",!0)}});const ha=m(fa,[["__scopeId","data-v-add8f686"]]),ma={class:"list"},ga=y({__name:"VPNavScreenTranslations",setup(t){const{localeLinks:e,currentLang:n}=ue({correspondingLink:!0}),o=L(!1);function s(){o.value=!o.value}return(r,c)=>i(e).length&&i(n).label?(a(),l("div",{key:0,class:N(["VPNavScreenTranslations",{open:o.value}])},[_("button",{class:"title",onClick:s},[h(dt,{class:"icon lang"}),B(" "+C(i(n).label)+" ",1),h(ut,{class:"icon chevron"})]),_("ul",ma,[(a(!0),l(I,null,D(i(e),v=>(a(),l("li",{key:v.link,class:"item"},[h(Y,{class:"link",href:v.link},{default:p(()=>[B(C(v.text),1)]),_:2},1032,["href"])]))),128))])],2)):g("",!0)}});const ya=m(ga,[["__scopeId","data-v-d72aa483"]]),ba=y({__name:"VPNavScreenSocialLinks",setup(t){const{theme:e}=M();return(n,o)=>i(e).socialLinks?(a(),$(De,{key:0,class:"VPNavScreenSocialLinks",links:i(e).socialLinks},null,8,["links"])):g("",!0)}}),ka={class:"container"},$a=y({__name:"VPNavScreen",props:{open:{type:Boolean}},setup(t){const e=L(null);function n(){vt(e.value,{reserveScrollBarGap:!0})}function o(){pt()}return(s,r)=>(a(),$(Ae,{name:"fade",onEnter:n,onAfterLeave:o},{default:p(()=>[s.open?(a(),l("div",{key:0,class:"VPNavScreen",ref_key:"screen",ref:e},[_("div",ka,[d(s.$slots,"nav-screen-content-before",{},void 0,!0),h(_a,{class:"menu"}),h(ya,{class:"translations"}),h(ha,{class:"appearance"}),h(ba,{class:"social-links"}),d(s.$slots,"nav-screen-content-after",{},void 0,!0)])],512)):g("",!0)]),_:3}))}});const wa=m($a,[["__scopeId","data-v-724636ae"]]),Pa={class:"VPNav"},Va=y({__name:"VPNav",setup(t){const{isScreenOpen:e,closeScreen:n,toggleScreen:o}=yn();return fe("close-screen",n),(s,r)=>(a(),l("header",Pa,[h(Ds,{"is-screen-open":i(e),onToggleScreen:i(o)},{"nav-bar-title-before":p(()=>[d(s.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":p(()=>[d(s.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":p(()=>[d(s.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":p(()=>[d(s.$slots,"nav-bar-content-after",{},void 0,!0)]),_:3},8,["is-screen-open","onToggleScreen"]),h(wa,{open:i(e)},{"nav-screen-content-before":p(()=>[d(s.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":p(()=>[d(s.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3},8,["open"])]))}});const Sa=m(Va,[["__scopeId","data-v-7e5bc4a5"]]),La={},Ma={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Ca=_("path",{d:"M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"},null,-1),Ta=_("path",{d:"M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"},null,-1),Aa=_("path",{d:"M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"},null,-1),xa=_("path",{d:"M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"},null,-1),Ia=[Ca,Ta,Aa,xa];function Na(t,e){return a(),l("svg",Ma,Ia)}const Ba=m(La,[["render",Na]]);function Ha(){const{hasSidebar:t}=R(),e=Pe("(min-width: 960px)"),n=Pe("(min-width: 1280px)");return{isAsideEnabled:k(()=>!n.value&&!e.value?!1:t.value?n.value:e.value)}}const Ea=71;function Fe(t){return typeof t.outline=="object"&&!Array.isArray(t.outline)&&t.outline.label||t.outlineTitle||"On this page"}function Ge(t){const e=[...document.querySelectorAll(".VPDoc h2,h3,h4,h5,h6")].filter(n=>n.id&&n.hasChildNodes()).map(n=>{const o=Number(n.tagName[1]);return{title:Da(n),link:"#"+n.id,level:o}});return za(e,t)}function Da(t){let e="";for(const n of t.childNodes)if(n.nodeType===1){if(n.classList.contains("VPBadge")||n.classList.contains("header-anchor"))continue;e+=n.textContent}else n.nodeType===3&&(e+=n.textContent);return e.trim()}function za(t,e){if(e===!1)return[];const n=(typeof e=="object"&&!Array.isArray(e)?e.level:e)||2,[o,s]=typeof n=="number"?[n,n]:n==="deep"?[2,6]:n;t=t.filter(c=>c.level>=o&&c.level<=s);const r=[];e:for(let c=0;c<t.length;c++){const v=t[c];if(c===0)r.push(v);else{for(let u=c-1;u>=0;u--){const f=t[u];if(f.level<v.level){(f.children||(f.children=[])).push(v);continue e}}r.push(v)}}return r}function Oa(t,e){const{isAsideEnabled:n}=Ha(),o=cn(r,100);let s=null;W(()=>{requestAnimationFrame(r),window.addEventListener("scroll",o)}),xt(()=>{c(location.hash)}),Te(()=>{window.removeEventListener("scroll",o)});function r(){if(!n.value)return;const v=[].slice.call(t.value.querySelectorAll(".outline-link")),u=[].slice.call(document.querySelectorAll(".content .header-anchor")).filter(P=>v.some(T=>T.hash===P.hash&&P.offsetParent!==null)),f=window.scrollY,b=window.innerHeight,V=document.body.offsetHeight,w=Math.abs(f+b-V)<1;if(u.length&&w){c(u[u.length-1].hash);return}for(let P=0;P<u.length;P++){const T=u[P],A=u[P+1],[S,H]=Fa(P,T,A);if(S){c(H);return}}}function c(v){s&&s.classList.remove("active"),v!==null&&(s=t.value.querySelector(`a[href="${decodeURIComponent(v)}"]`));const u=s;u?(u.classList.add("active"),e.value.style.top=u.offsetTop+33+"px",e.value.style.opacity="1"):(e.value.style.top="33px",e.value.style.opacity="0")}}function Qe(t){return t.parentElement.offsetTop-Ea}function Fa(t,e,n){const o=window.scrollY;return t===0&&o===0?[!0,null]:o<Qe(e)?[!1,null]:!n||o<Qe(n)?[!0,e.hash]:[!1,null]}const Ga=["href","title"],ja=y({__name:"VPDocOutlineItem",props:{headers:{},root:{type:Boolean}},setup(t){function e({target:n}){const o="#"+n.href.split("#")[1],s=document.querySelector(decodeURIComponent(o));s==null||s.focus()}return(n,o)=>{const s=X("VPDocOutlineItem",!0);return a(),l("ul",{class:N(n.root?"root":"nested")},[(a(!0),l(I,null,D(n.headers,({children:r,link:c,title:v})=>(a(),l("li",null,[_("a",{class:"outline-link",href:c,onClick:e,title:v},C(v),9,Ga),r!=null&&r.length?(a(),$(s,{key:0,headers:r},null,8,["headers"])):g("",!0)]))),256))],2)}}});const je=m(ja,[["__scopeId","data-v-9a431c33"]]),Wa={},Ra={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Ua=_("path",{d:"M9,19c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l5.3-5.3L8.3,6.7c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l6,6c0.4,0.4,0.4,1,0,1.4l-6,6C9.5,18.9,9.3,19,9,19z"},null,-1),Ka=[Ua];function qa(t,e){return a(),l("svg",Ra,Ka)}const We=m(Wa,[["render",qa]]),Ya=y({__name:"VPLocalNavOutlineDropdown",setup(t){const{frontmatter:e,theme:n}=M(),o=L(!1),s=L(0),r=L();se(()=>{o.value=!1});function c(){o.value=!o.value,s.value=window.innerHeight+Math.min(window.scrollY-64,0)}function v(b){b.target.classList.contains("outline-link")&&(r.value&&(r.value.style.transition="none"),Me(()=>{o.value=!1}))}function u(){o.value=!1,window.scrollTo({top:0,left:0,behavior:"smooth"})}const f=le([]);return se(()=>{f.value=Ge(e.value.outline??n.value.outline)}),(b,V)=>(a(),l("div",{class:"VPLocalNavOutlineDropdown",style:et({"--vp-vh":s.value+"px"})},[f.value.length>0?(a(),l("button",{key:0,onClick:c,class:N({open:o.value})},[B(C(i(Fe)(i(n)))+" ",1),h(We,{class:"icon"})],2)):(a(),l("button",{key:1,onClick:u},C(i(n).returnToTopLabel||"Return to top"),1)),h(Ae,{name:"flyout"},{default:p(()=>[o.value?(a(),l("div",{key:0,ref_key:"items",ref:r,class:"items",onClick:v},[_("a",{class:"top-link",href:"#",onClick:u},C(i(n).returnToTopLabel||"Return to top"),1),h(je,{headers:f.value},null,8,["headers"])],512)):g("",!0)]),_:1})],4))}});const Ja=m(Ya,[["__scopeId","data-v-079b16a8"]]),Xa={key:0,class:"VPLocalNav"},Qa=["aria-expanded"],Za={class:"menu-text"},er=y({__name:"VPLocalNav",props:{open:{type:Boolean}},emits:["open-menu"],setup(t){const{theme:e,frontmatter:n}=M(),{hasSidebar:o}=R();return(s,r)=>i(n).layout!=="home"?(a(),l("div",Xa,[i(o)?(a(),l("button",{key:0,class:"menu","aria-expanded":s.open,"aria-controls":"VPSidebarNav",onClick:r[0]||(r[0]=c=>s.$emit("open-menu"))},[h(Ba,{class:"menu-icon"}),_("span",Za,C(i(e).sidebarMenuLabel||"Menu"),1)],8,Qa)):g("",!0),h(Ja)])):g("",!0)}});const tr=m(er,[["__scopeId","data-v-392e1bf8"]]),nr=t=>(G("data-v-c4656e6d"),t=t(),j(),t),or=["role","tabindex"],sr=nr(()=>_("div",{class:"indicator"},null,-1)),ar=["onKeydown"],rr={key:1,class:"items"},ir=y({__name:"VPSidebarItem",props:{item:{},depth:{}},setup(t){const e=t,{collapsed:n,collapsible:o,isLink:s,isActiveLink:r,hasActiveLink:c,hasChildren:v,toggle:u}=vn(k(()=>e.item)),f=k(()=>v.value?"section":"div"),b=k(()=>s.value?"a":"div"),V=k(()=>v.value?e.depth+2===7?"p":`h${e.depth+2}`:"p"),w=k(()=>s.value?void 0:"button"),P=k(()=>[[`level-${e.depth}`],{collapsible:o.value},{collapsed:n.value},{"is-link":s.value},{"is-active":r.value},{"has-active":c.value}]);function T(S){"key"in S&&S.key!=="Enter"||!e.item.link&&u()}function A(){e.item.link&&u()}return(S,H)=>{const z=X("VPSidebarItem",!0);return a(),$(Q(f.value),{class:N(["VPSidebarItem",P.value])},{default:p(()=>[S.item.text?(a(),l("div",pe({key:0,class:"item",role:w.value},It(S.item.items?{click:T,keydown:T}:{},!0),{tabindex:S.item.items&&0}),[sr,S.item.link?(a(),$(Y,{key:0,tag:b.value,class:"link",href:S.item.link},{default:p(()=>[(a(),$(Q(V.value),{class:"text",innerHTML:S.item.text},null,8,["innerHTML"]))]),_:1},8,["tag","href"])):(a(),$(Q(V.value),{key:1,class:"text",innerHTML:S.item.text},null,8,["innerHTML"])),S.item.collapsed!=null?(a(),l("div",{key:2,class:"caret",role:"button","aria-label":"toggle section",onClick:A,onKeydown:Nt(A,["enter"]),tabindex:"0"},[h(We,{class:"caret-icon"})],40,ar)):g("",!0)],16,or)):g("",!0),S.item.items&&S.item.items.length?(a(),l("div",rr,[S.depth<5?(a(!0),l(I,{key:0},D(S.item.items,O=>(a(),$(z,{key:O.text,item:O,depth:S.depth+1},null,8,["item","depth"]))),128)):g("",!0)])):g("",!0)]),_:1},8,["class"])}}});const lr=m(ir,[["__scopeId","data-v-c4656e6d"]]),ht=t=>(G("data-v-af16598e"),t=t(),j(),t),cr=ht(()=>_("div",{class:"curtain"},null,-1)),ur={class:"nav",id:"VPSidebarNav","aria-labelledby":"sidebar-aria-label",tabindex:"-1"},dr=ht(()=>_("span",{class:"visually-hidden",id:"sidebar-aria-label"}," Sidebar Navigation ",-1)),_r=y({__name:"VPSidebar",props:{open:{type:Boolean}},setup(t){const e=t,{sidebarGroups:n,hasSidebar:o}=R();let s=L(null);function r(){vt(s.value,{reserveScrollBarGap:!0})}function c(){pt()}return Bt(async()=>{var v;e.open?(r(),(v=s.value)==null||v.focus()):c()}),(v,u)=>i(o)?(a(),l("aside",{key:0,class:N(["VPSidebar",{open:v.open}]),ref_key:"navEl",ref:s,onClick:u[0]||(u[0]=Ht(()=>{},["stop"]))},[cr,_("nav",ur,[dr,d(v.$slots,"sidebar-nav-before",{},void 0,!0),(a(!0),l(I,null,D(i(n),f=>(a(),l("div",{key:f.text,class:"group"},[h(lr,{item:f,depth:0},null,8,["item"])]))),128)),d(v.$slots,"sidebar-nav-after",{},void 0,!0)])],2)):g("",!0)}});const vr=m(_r,[["__scopeId","data-v-af16598e"]]),pr={},fr={class:"VPPage"};function hr(t,e){const n=X("Content");return a(),l("div",fr,[d(t.$slots,"page-top"),h(n),d(t.$slots,"page-bottom")])}const mr=m(pr,[["render",hr]]),gr=y({__name:"VPButton",props:{tag:{},size:{},theme:{},text:{},href:{}},setup(t){const e=t,n=k(()=>[e.size??"medium",e.theme??"brand"]),o=k(()=>e.href&&tt.test(e.href)),s=k(()=>e.tag?e.tag:e.href?"a":"button");return(r,c)=>(a(),$(Q(s.value),{class:N(["VPButton",n.value]),href:r.href?i(ie)(r.href):void 0,target:o.value?"_blank":void 0,rel:o.value?"noreferrer":void 0},{default:p(()=>[B(C(r.text),1)]),_:1},8,["class","href","target","rel"]))}});const yr=m(gr,[["__scopeId","data-v-567ba664"]]),br=t=>(G("data-v-fd2650d5"),t=t(),j(),t),kr={class:"container"},$r={class:"main"},wr={key:0,class:"name"},Pr={class:"clip"},Vr={key:1,class:"text"},Sr={key:2,class:"tagline"},Lr={key:0,class:"actions"},Mr={key:0,class:"image"},Cr={class:"image-container"},Tr=br(()=>_("div",{class:"image-bg"},null,-1)),Ar=y({__name:"VPHero",props:{name:{},text:{},tagline:{},image:{},actions:{}},setup(t){const e=xe("hero-image-slot-exists");return(n,o)=>(a(),l("div",{class:N(["VPHero",{"has-image":n.image||i(e)}])},[_("div",kr,[_("div",$r,[d(n.$slots,"home-hero-info",{},()=>[n.name?(a(),l("h1",wr,[_("span",Pr,C(n.name),1)])):g("",!0),n.text?(a(),l("p",Vr,C(n.text),1)):g("",!0),n.tagline?(a(),l("p",Sr,C(n.tagline),1)):g("",!0)],!0),n.actions?(a(),l("div",Lr,[(a(!0),l(I,null,D(n.actions,s=>(a(),l("div",{key:s.link,class:"action"},[h(yr,{tag:"a",size:"medium",theme:s.theme,text:s.text,href:s.link},null,8,["theme","text","href"])]))),128))])):g("",!0)]),n.image||i(e)?(a(),l("div",Mr,[_("div",Cr,[Tr,d(n.$slots,"home-hero-image",{},()=>[n.image?(a(),$(Ne,{key:0,class:"image-src",image:n.image},null,8,["image"])):g("",!0)],!0)])])):g("",!0)])],2))}});const xr=m(Ar,[["__scopeId","data-v-fd2650d5"]]),Ir=y({__name:"VPHomeHero",setup(t){const{frontmatter:e}=M();return(n,o)=>i(e).hero?(a(),$(xr,{key:0,class:"VPHomeHero",name:i(e).hero.name,text:i(e).hero.text,tagline:i(e).hero.tagline,image:i(e).hero.image,actions:i(e).hero.actions},{"home-hero-info":p(()=>[d(n.$slots,"home-hero-info")]),"home-hero-image":p(()=>[d(n.$slots,"home-hero-image")]),_:3},8,["name","text","tagline","image","actions"])):g("",!0)}}),Nr={},Br={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Hr=_("path",{d:"M19.9,12.4c0.1-0.2,0.1-0.5,0-0.8c-0.1-0.1-0.1-0.2-0.2-0.3l-7-7c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l5.3,5.3H5c-0.6,0-1,0.4-1,1s0.4,1,1,1h11.6l-5.3,5.3c-0.4,0.4-0.4,1,0,1.4c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3l7-7C19.8,12.6,19.9,12.5,19.9,12.4z"},null,-1),Er=[Hr];function Dr(t,e){return a(),l("svg",Br,Er)}const zr=m(Nr,[["render",Dr]]),Or={class:"box"},Fr=["innerHTML"],Gr=["innerHTML"],jr=["innerHTML"],Wr={key:3,class:"link-text"},Rr={class:"link-text-value"},Ur=y({__name:"VPFeature",props:{icon:{},title:{},details:{},link:{},linkText:{}},setup(t){return(e,n)=>(a(),$(Y,{class:"VPFeature",href:e.link,"no-icon":!0},{default:p(()=>[_("article",Or,[typeof e.icon=="object"?(a(),$(Ne,{key:0,image:e.icon,alt:e.icon.alt,height:e.icon.height,width:e.icon.width},null,8,["image","alt","height","width"])):e.icon?(a(),l("div",{key:1,class:"icon",innerHTML:e.icon},null,8,Fr)):g("",!0),_("h2",{class:"title",innerHTML:e.title},null,8,Gr),e.details?(a(),l("p",{key:2,class:"details",innerHTML:e.details},null,8,jr)):g("",!0),e.linkText?(a(),l("div",Wr,[_("p",Rr,[B(C(e.linkText)+" ",1),h(zr,{class:"link-text-icon"})])])):g("",!0)])]),_:1},8,["href"]))}});const Kr=m(Ur,[["__scopeId","data-v-837f6cca"]]),qr={key:0,class:"VPFeatures"},Yr={class:"container"},Jr={class:"items"},Xr=y({__name:"VPFeatures",props:{features:{}},setup(t){const e=t,n=k(()=>{const o=e.features.length;if(o){if(o===2)return"grid-2";if(o===3)return"grid-3";if(o%3===0)return"grid-6";if(o%2===0)return"grid-4"}else return});return(o,s)=>o.features?(a(),l("div",qr,[_("div",Yr,[_("div",Jr,[(a(!0),l(I,null,D(o.features,r=>(a(),l("div",{key:r.title,class:N(["item",[n.value]])},[h(Kr,{icon:r.icon,title:r.title,details:r.details,link:r.link,"link-text":r.linkText},null,8,["icon","title","details","link","link-text"])],2))),128))])])])):g("",!0)}});const Qr=m(Xr,[["__scopeId","data-v-6816157f"]]),Zr=y({__name:"VPHomeFeatures",setup(t){const{frontmatter:e}=M();return(n,o)=>i(e).features?(a(),$(Qr,{key:0,class:"VPHomeFeatures",features:i(e).features},null,8,["features"])):g("",!0)}}),ei={class:"VPHome"},ti=y({__name:"VPHome",setup(t){return(e,n)=>{const o=X("Content");return a(),l("div",ei,[d(e.$slots,"home-hero-before",{},void 0,!0),h(Ir,null,{"home-hero-info":p(()=>[d(e.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-image":p(()=>[d(e.$slots,"home-hero-image",{},void 0,!0)]),_:3}),d(e.$slots,"home-hero-after",{},void 0,!0),d(e.$slots,"home-features-before",{},void 0,!0),h(Zr),d(e.$slots,"home-features-after",{},void 0,!0),h(o)])}}});const ni=m(ti,[["__scopeId","data-v-d82743a8"]]),oi=t=>(G("data-v-ff0f39c8"),t=t(),j(),t),si={class:"content"},ai={class:"outline-title"},ri={"aria-labelledby":"doc-outline-aria-label"},ii=oi(()=>_("span",{class:"visually-hidden",id:"doc-outline-aria-label"}," Table of Contents for current page ",-1)),li=y({__name:"VPDocAsideOutline",setup(t){const{frontmatter:e,theme:n}=M(),o=le([]);se(()=>{o.value=Ge(e.value.outline??n.value.outline)});const s=L(),r=L();return Oa(s,r),(c,v)=>(a(),l("div",{class:N(["VPDocAsideOutline",{"has-outline":o.value.length>0}]),ref_key:"container",ref:s},[_("div",si,[_("div",{class:"outline-marker",ref_key:"marker",ref:r},null,512),_("div",ai,C(i(Fe)(i(n))),1),_("nav",ri,[ii,h(je,{headers:o.value,root:!0},null,8,["headers"])])])],2))}});const ci=m(li,[["__scopeId","data-v-ff0f39c8"]]),ui={class:"VPDocAsideCarbonAds"},di=y({__name:"VPDocAsideCarbonAds",props:{carbonAds:{}},setup(t){const e=()=>null;return(n,o)=>(a(),l("div",ui,[h(i(e),{"carbon-ads":n.carbonAds},null,8,["carbon-ads"])]))}}),_i=t=>(G("data-v-3f215769"),t=t(),j(),t),vi={class:"VPDocAside"},pi=_i(()=>_("div",{class:"spacer"},null,-1)),fi=y({__name:"VPDocAside",setup(t){const{theme:e}=M();return(n,o)=>(a(),l("div",vi,[d(n.$slots,"aside-top",{},void 0,!0),d(n.$slots,"aside-outline-before",{},void 0,!0),h(ci),d(n.$slots,"aside-outline-after",{},void 0,!0),pi,d(n.$slots,"aside-ads-before",{},void 0,!0),i(e).carbonAds?(a(),$(di,{key:0,"carbon-ads":i(e).carbonAds},null,8,["carbon-ads"])):g("",!0),d(n.$slots,"aside-ads-after",{},void 0,!0),d(n.$slots,"aside-bottom",{},void 0,!0)]))}});const hi=m(fi,[["__scopeId","data-v-3f215769"]]);function mi(){const{theme:t,page:e}=M();return k(()=>{const{text:n="Edit this page",pattern:o=""}=t.value.editLink||{};let s;return typeof o=="function"?s=o(e.value):s=o.replace(/:path/g,e.value.filePath),{url:s,text:n}})}function gi(){const{page:t,theme:e,frontmatter:n}=M();return k(()=>{var c,v,u,f;const o=it(e.value.sidebar,t.value.relativePath),s=dn(o),r=s.findIndex(b=>te(t.value.relativePath,b.link));return{prev:n.value.prev===!1?void 0:{text:(typeof n.value.prev=="string"?n.value.prev:typeof n.value.prev=="object"?n.value.prev.text:void 0)??((c=s[r-1])==null?void 0:c.text),link:(typeof n.value.prev=="object"?n.value.prev.link:void 0)??((v=s[r-1])==null?void 0:v.link)},next:n.value.next===!1?void 0:{text:(typeof n.value.next=="string"?n.value.next:typeof n.value.next=="object"?n.value.next.text:void 0)??((u=s[r+1])==null?void 0:u.text),link:(typeof n.value.next=="object"?n.value.next.link:void 0)??((f=s[r+1])==null?void 0:f.link)}}})}const yi={},bi={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},ki=_("path",{d:"M18,23H4c-1.7,0-3-1.3-3-3V6c0-1.7,1.3-3,3-3h7c0.6,0,1,0.4,1,1s-0.4,1-1,1H4C3.4,5,3,5.4,3,6v14c0,0.6,0.4,1,1,1h14c0.6,0,1-0.4,1-1v-7c0-0.6,0.4-1,1-1s1,0.4,1,1v7C21,21.7,19.7,23,18,23z"},null,-1),$i=_("path",{d:"M8,17c-0.3,0-0.5-0.1-0.7-0.3C7,16.5,6.9,16.1,7,15.8l1-4c0-0.2,0.1-0.3,0.3-0.5l9.5-9.5c1.2-1.2,3.2-1.2,4.4,0c1.2,1.2,1.2,3.2,0,4.4l-9.5,9.5c-0.1,0.1-0.3,0.2-0.5,0.3l-4,1C8.2,17,8.1,17,8,17zM9.9,12.5l-0.5,2.1l2.1-0.5l9.3-9.3c0.4-0.4,0.4-1.1,0-1.6c-0.4-0.4-1.2-0.4-1.6,0l0,0L9.9,12.5z M18.5,2.5L18.5,2.5L18.5,2.5z"},null,-1),wi=[ki,$i];function Pi(t,e){return a(),l("svg",bi,wi)}const Vi=m(yi,[["render",Pi]]),Si={class:"VPLastUpdated"},Li=["datetime"],Mi=y({__name:"VPDocFooterLastUpdated",setup(t){const{theme:e,page:n,lang:o}=M(),s=k(()=>new Date(n.value.lastUpdated)),r=k(()=>s.value.toISOString()),c=L("");return W(()=>{ee(()=>{c.value=s.value.toLocaleString(o.value)})}),(v,u)=>(a(),l("p",Si,[B(C(i(e).lastUpdatedText||"Last updated")+": ",1),_("time",{datetime:r.value},C(c.value),9,Li)]))}});const Ci=m(Mi,[["__scopeId","data-v-7b3ebfe1"]]),Ti={key:0,class:"VPDocFooter"},Ai={key:0,class:"edit-info"},xi={key:0,class:"edit-link"},Ii={key:1,class:"last-updated"},Ni={key:1,class:"prev-next"},Bi={class:"pager"},Hi=["href"],Ei=["innerHTML"],Di=["innerHTML"],zi=["href"],Oi=["innerHTML"],Fi=["innerHTML"],Gi=y({__name:"VPDocFooter",setup(t){const{theme:e,page:n,frontmatter:o}=M(),s=mi(),r=gi(),c=k(()=>e.value.editLink&&o.value.editLink!==!1),v=k(()=>n.value.lastUpdated&&o.value.lastUpdated!==!1),u=k(()=>c.value||v.value||r.value.prev||r.value.next);return(f,b)=>{var V,w,P,T,A,S,H;return u.value?(a(),l("footer",Ti,[d(f.$slots,"doc-footer-before",{},void 0,!0),c.value||v.value?(a(),l("div",Ai,[c.value?(a(),l("div",xi,[h(Y,{class:"edit-link-button",href:i(s).url,"no-icon":!0},{default:p(()=>[h(Vi,{class:"edit-link-icon","aria-label":"edit icon"}),B(" "+C(i(s).text),1)]),_:1},8,["href"])])):g("",!0),v.value?(a(),l("div",Ii,[h(Ci)])):g("",!0)])):g("",!0),(V=i(r).prev)!=null&&V.link||(w=i(r).next)!=null&&w.link?(a(),l("div",Ni,[_("div",Bi,[(P=i(r).prev)!=null&&P.link?(a(),l("a",{key:0,class:"pager-link prev",href:i(ie)(i(r).prev.link)},[_("span",{class:"desc",innerHTML:((T=i(e).docFooter)==null?void 0:T.prev)||"Previous page"},null,8,Ei),_("span",{class:"title",innerHTML:i(r).prev.text},null,8,Di)],8,Hi)):g("",!0)]),_("div",{class:N(["pager",{"has-prev":(A=i(r).prev)==null?void 0:A.link}])},[(S=i(r).next)!=null&&S.link?(a(),l("a",{key:0,class:"pager-link next",href:i(ie)(i(r).next.link)},[_("span",{class:"desc",innerHTML:((H=i(e).docFooter)==null?void 0:H.next)||"Next page"},null,8,Oi),_("span",{class:"title",innerHTML:i(r).next.text},null,8,Fi)],8,zi)):g("",!0)],2)])):g("",!0)])):g("",!0)}}});const ji=m(Gi,[["__scopeId","data-v-face870a"]]),Wi={key:0,class:"VPDocOutlineDropdown"},Ri={key:0,class:"items"},Ui=y({__name:"VPDocOutlineDropdown",setup(t){const{frontmatter:e,theme:n}=M(),o=L(!1);se(()=>{o.value=!1});const s=le([]);return se(()=>{s.value=Ge(e.value.outline??n.value.outline)}),(r,c)=>s.value.length>0?(a(),l("div",Wi,[_("button",{onClick:c[0]||(c[0]=v=>o.value=!o.value),class:N({open:o.value})},[B(C(i(Fe)(i(n)))+" ",1),h(We,{class:"icon"})],2),o.value?(a(),l("div",Ri,[h(je,{headers:s.value},null,8,["headers"])])):g("",!0)])):g("",!0)}});const Ki=m(Ui,[["__scopeId","data-v-2edece88"]]),qi=t=>(G("data-v-c4b0d3cf"),t=t(),j(),t),Yi={class:"container"},Ji=qi(()=>_("div",{class:"aside-curtain"},null,-1)),Xi={class:"aside-container"},Qi={class:"aside-content"},Zi={class:"content"},el={class:"content-container"},tl={class:"main"},nl=y({__name:"VPDoc",setup(t){const e=ce(),{hasSidebar:n,hasAside:o,leftAside:s}=R(),r=k(()=>e.path.replace(/[./]+/g,"_").replace(/_html$/,""));return(c,v)=>{const u=X("Content");return a(),l("div",{class:N(["VPDoc",{"has-sidebar":i(n),"has-aside":i(o)}])},[d(c.$slots,"doc-top",{},void 0,!0),_("div",Yi,[i(o)?(a(),l("div",{key:0,class:N(["aside",{"left-aside":i(s)}])},[Ji,_("div",Xi,[_("div",Qi,[h(hi,null,{"aside-top":p(()=>[d(c.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":p(()=>[d(c.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":p(()=>[d(c.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":p(()=>[d(c.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":p(()=>[d(c.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":p(()=>[d(c.$slots,"aside-ads-after",{},void 0,!0)]),_:3})])])],2)):g("",!0),_("div",Zi,[_("div",el,[d(c.$slots,"doc-before",{},void 0,!0),h(Ki),_("main",tl,[h(u,{class:N(["vp-doc",r.value])},null,8,["class"])]),h(ji,null,{"doc-footer-before":p(()=>[d(c.$slots,"doc-footer-before",{},void 0,!0)]),_:3}),d(c.$slots,"doc-after",{},void 0,!0)])])]),d(c.$slots,"doc-bottom",{},void 0,!0)],2)}}});const ol=m(nl,[["__scopeId","data-v-c4b0d3cf"]]),be=t=>(G("data-v-c70503b8"),t=t(),j(),t),sl={class:"NotFound"},al=be(()=>_("p",{class:"code"},"404",-1)),rl=be(()=>_("h1",{class:"title"},"PAGE NOT FOUND",-1)),il=be(()=>_("div",{class:"divider"},null,-1)),ll=be(()=>_("blockquote",{class:"quote"}," But if you don't change your direction, and if you keep looking, you may end up where you are heading. ",-1)),cl={class:"action"},ul=["href"],dl=y({__name:"NotFound",setup(t){const{site:e}=M(),{localeLinks:n}=ue({removeCurrent:!1}),o=L("/");return W(()=>{var r;const s=window.location.pathname.replace(e.value.base,"").replace(/(^.*?\/).*$/,"/$1");n.value.length&&(o.value=((r=n.value.find(({link:c})=>c.startsWith(s)))==null?void 0:r.link)||n.value[0].link)}),(s,r)=>(a(),l("div",sl,[al,rl,il,ll,_("div",cl,[_("a",{class:"link",href:i(Ce)(o.value),"aria-label":"go to home"}," Take me home ",8,ul)])]))}});const _l=m(dl,[["__scopeId","data-v-c70503b8"]]),vl=y({__name:"VPContent",setup(t){const{page:e,frontmatter:n}=M(),{hasSidebar:o}=R();return(s,r)=>(a(),l("div",{class:N(["VPContent",{"has-sidebar":i(o),"is-home":i(n).layout==="home"}]),id:"VPContent"},[i(e).isNotFound?d(s.$slots,"not-found",{key:0},()=>[h(_l)],!0):i(n).layout==="page"?(a(),$(mr,{key:1},{"page-top":p(()=>[d(s.$slots,"page-top",{},void 0,!0)]),"page-bottom":p(()=>[d(s.$slots,"page-bottom",{},void 0,!0)]),_:3})):i(n).layout==="home"?(a(),$(ni,{key:2},{"home-hero-before":p(()=>[d(s.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info":p(()=>[d(s.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-image":p(()=>[d(s.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":p(()=>[d(s.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":p(()=>[d(s.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":p(()=>[d(s.$slots,"home-features-after",{},void 0,!0)]),_:3})):(a(),$(ol,{key:3},{"doc-top":p(()=>[d(s.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":p(()=>[d(s.$slots,"doc-bottom",{},void 0,!0)]),"doc-footer-before":p(()=>[d(s.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":p(()=>[d(s.$slots,"doc-before",{},void 0,!0)]),"doc-after":p(()=>[d(s.$slots,"doc-after",{},void 0,!0)]),"aside-top":p(()=>[d(s.$slots,"aside-top",{},void 0,!0)]),"aside-outline-before":p(()=>[d(s.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":p(()=>[d(s.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":p(()=>[d(s.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":p(()=>[d(s.$slots,"aside-ads-after",{},void 0,!0)]),"aside-bottom":p(()=>[d(s.$slots,"aside-bottom",{},void 0,!0)]),_:3}))],2))}});const pl=m(vl,[["__scopeId","data-v-a494bd1d"]]),fl={class:"container"},hl=["innerHTML"],ml=["innerHTML"],gl=y({__name:"VPFooter",setup(t){const{theme:e}=M(),{hasSidebar:n}=R();return(o,s)=>i(e).footer?(a(),l("footer",{key:0,class:N(["VPFooter",{"has-sidebar":i(n)}])},[_("div",fl,[i(e).footer.message?(a(),l("p",{key:0,class:"message",innerHTML:i(e).footer.message},null,8,hl)):g("",!0),i(e).footer.copyright?(a(),l("p",{key:1,class:"copyright",innerHTML:i(e).footer.copyright},null,8,ml)):g("",!0)])],2)):g("",!0)}});const yl=m(gl,[["__scopeId","data-v-2f86ebd2"]]),bl={key:0,class:"Layout"},kl=y({__name:"Layout",setup(t){const{isOpen:e,open:n,close:o}=R(),s=ce();q(()=>s.path,o),_n(e,o),fe("close-sidebar",o),fe("is-sidebar-open",e);const{frontmatter:r}=M(),c=Et(),v=k(()=>!!c["home-hero-image"]);return fe("hero-image-slot-exists",v),(u,f)=>{const b=X("Content");return i(r).layout!==!1?(a(),l("div",bl,[d(u.$slots,"layout-top",{},void 0,!0),h(fn),h(gn,{class:"backdrop",show:i(e),onClick:i(o)},null,8,["show","onClick"]),h(Sa,null,{"nav-bar-title-before":p(()=>[d(u.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":p(()=>[d(u.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":p(()=>[d(u.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":p(()=>[d(u.$slots,"nav-bar-content-after",{},void 0,!0)]),"nav-screen-content-before":p(()=>[d(u.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":p(()=>[d(u.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3}),h(tr,{open:i(e),onOpenMenu:i(n)},null,8,["open","onOpenMenu"]),h(vr,{open:i(e)},{"sidebar-nav-before":p(()=>[d(u.$slots,"sidebar-nav-before",{},void 0,!0)]),"sidebar-nav-after":p(()=>[d(u.$slots,"sidebar-nav-after",{},void 0,!0)]),_:3},8,["open"]),h(pl,null,{"page-top":p(()=>[d(u.$slots,"page-top",{},void 0,!0)]),"page-bottom":p(()=>[d(u.$slots,"page-bottom",{},void 0,!0)]),"not-found":p(()=>[d(u.$slots,"not-found",{},void 0,!0)]),"home-hero-before":p(()=>[d(u.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info":p(()=>[d(u.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-image":p(()=>[d(u.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":p(()=>[d(u.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":p(()=>[d(u.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":p(()=>[d(u.$slots,"home-features-after",{},void 0,!0)]),"doc-footer-before":p(()=>[d(u.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":p(()=>[d(u.$slots,"doc-before",{},void 0,!0)]),"doc-after":p(()=>[d(u.$slots,"doc-after",{},void 0,!0)]),"doc-top":p(()=>[d(u.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":p(()=>[d(u.$slots,"doc-bottom",{},void 0,!0)]),"aside-top":p(()=>[d(u.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":p(()=>[d(u.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":p(()=>[d(u.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":p(()=>[d(u.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":p(()=>[d(u.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":p(()=>[d(u.$slots,"aside-ads-after",{},void 0,!0)]),_:3}),h(yl),d(u.$slots,"layout-bottom",{},void 0,!0)])):(a(),$(b,{key:1}))}}});const $l=m(kl,[["__scopeId","data-v-b2cf3e0b"]]);const Cl={Layout:$l,enhanceApp:({app:t})=>{t.component("Badge",zt)}};function Tl(t,e){const{localeIndex:n}=M();function o(s){var P,T;const r=s.split("."),c=t&&typeof t=="object",v=c&&((T=(P=t.locales)==null?void 0:P[n.value])==null?void 0:T.translations)||null,u=c&&t.translations||null;let f=v,b=u,V=e;const w=r.pop();for(const A of r){let S=null;const H=V==null?void 0:V[A];H&&(S=V=H);const z=b==null?void 0:b[A];z&&(S=b=z);const O=f==null?void 0:f[A];O&&(S=f=O),H||(V=S),z||(b=S),O||(f=S)}return(f==null?void 0:f[w])??(b==null?void 0:b[w])??(V==null?void 0:V[w])??""}return o}export{Ml as a,Sl as b,Vl as c,Tl as d,re as e,Ll as f,Qt as o,Cl as t,M as u,Pl as w};
