import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const d=JSON.parse('{"title":"项目结构","description":"","frontmatter":{},"headers":[],"relativePath":"guide/project-structure.md","filePath":"guide/project-structure.md"}'),p={name:"guide/project-structure.md"},o=l(`<h1 id="项目结构" tabindex="-1">项目结构 <a class="header-anchor" href="#项目结构" aria-label="Permalink to &quot;项目结构&quot;">​</a></h1><p>本页面介绍现代化水库管理矩阵前端项目的标准目录结构及各部分的作用。理解项目结构有助于您更快地了解代码组织方式，便于开发和维护。</p><h2 id="matrix-ui项目结构" tabindex="-1">Matrix-UI项目结构 <a class="header-anchor" href="#matrix-ui项目结构" aria-label="Permalink to &quot;Matrix-UI项目结构&quot;">​</a></h2><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">matrix-ui/</span></span>
<span class="line"><span style="color:#babed8;">├── public/                 # 静态资源目录（不会被构建工具处理）</span></span>
<span class="line"><span style="color:#babed8;">│   ├── favicon.ico         # 网站图标</span></span>
<span class="line"><span style="color:#babed8;">│   ├── index.html          # HTML模板</span></span>
<span class="line"><span style="color:#babed8;">│   ├── Cesium/            # Cesium静态资源</span></span>
<span class="line"><span style="color:#babed8;">│   ├── js/                # 第三方JS库</span></span>
<span class="line"><span style="color:#babed8;">│   ├── json/              # 静态JSON配置文件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── static/            # 静态配置文件</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── config.js      # 系统配置文件</span></span>
<span class="line"><span style="color:#babed8;">│   └── vtour/             # 全景图资源</span></span>
<span class="line"><span style="color:#babed8;">├── src/                    # 源代码目录</span></span>
<span class="line"><span style="color:#babed8;">│   ├── api/                # API请求封装</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # API模块聚合</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── login.js        # 登录相关API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── menu.js         # 菜单API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── map.js          # 地图API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── admin/          # 管理员API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── adminIndex/     # 管理首页API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── calculations/   # 计算模块API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── digitalTwin/    # 数字孪生API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── hydmodel/       # 水文模型API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ipm/            # IPM相关API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── map/            # 地图服务API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── matrix/         # 矩阵管理API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── monitor/        # 监控API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── projectInfo/    # 项目信息API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── screen/         # 大屏API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── system/         # 系统API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── tool/           # 工具API</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── typicalrrainfall/ # 典型降雨API</span></span>
<span class="line"><span style="color:#babed8;">│   ├── assets/             # 资源文件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── font/           # 字体文件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── icons/          # SVG图标</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── images/         # 图片资源</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── styles/         # 全局样式</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── index.scss  # 主样式文件</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── element-variables.scss # Element UI主题</span></span>
<span class="line"><span style="color:#babed8;">│   │       └── zhy.scss    # 智洋定制样式</span></span>
<span class="line"><span style="color:#babed8;">│   ├── components/         # 全局通用组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── Breadcrumb/     # 面包屑导航</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ConfigData/     # 配置数据组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── Crontab/        # 定时任务组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── CustomFileUpload/ # 自定义文件上传</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── CustomIframe/   # 自定义iframe</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── DictData/       # 字典数据组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── DictTag/        # 字典标签组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── DigitalTwin/    # 数字孪生组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── DigitalTwin5/   # 数字孪生5.0组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── Editor/         # 富文本编辑器</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ElYearPicker/   # 年份选择器</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── FilePreview/    # 文件预览组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── FileUpload/     # 文件上传组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── Hamburger/      # 汉堡菜单</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── HeaderSearch/   # 头部搜索</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── IconSelect/     # 图标选择器</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ImageUpload/    # 图片上传组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── InputNumber/    # 自定义数字输入框</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── InputWord/      # 自定义输入框</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── Pagination/     # 分页组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── PanThumb/       # 缩略图组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ParentView/     # 父级视图</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── RightPanel/     # 右侧面板</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── RightToolbar/   # 右侧工具栏</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── Screenfull/     # 全屏组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── SizeSelect/     # 尺寸选择器</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── SvgIcon/        # SVG图标组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ThemePicker/    # 主题选择器</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── TopNav/         # 顶部导航</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── commonDialogBox/ # 通用对话框</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── dictSelect/     # 字典选择器</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── iFrame/         # iframe组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── directive/          # 全局指令</span></span>
<span class="line"><span style="color:#babed8;">│   ├── layout/             # 布局组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── mixins/             # 混入</span></span>
<span class="line"><span style="color:#babed8;">│   ├── plugins/            # 插件配置</span></span>
<span class="line"><span style="color:#babed8;">│   ├── router/             # 路由配置</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── index.js        # 路由主文件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── store/              # Vuex状态管理</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # Store主文件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── getters.js      # 全局getters</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── modules/        # Store模块</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── app.js      # 应用状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── common.js   # 通用状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── deepseek.js # DeepSeek集成</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── dict.js     # 字典状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── jiankong.js # 监控状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── managePlatform.js # 管理平台状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── map.js      # 地图状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── pageDialog.js # 页面对话框状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── permission.js # 权限状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── screenParams.js # 大屏参数状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── settings.js # 设置状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── tagsView.js # 标签视图状态</span></span>
<span class="line"><span style="color:#babed8;">│   │       └── user.js     # 用户状态</span></span>
<span class="line"><span style="color:#babed8;">│   ├── utils/              # 工具函数</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # 通用工具函数</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── request.js      # HTTP请求封装</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── auth.js         # 认证相关工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── errorCode.js    # 错误码定义</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── formatTime.js   # 时间格式化</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── formValidate.js # 表单验证工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── jsencrypt.js    # 加密解密工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── math.js         # 数学计算工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── permission.js   # 权限工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── resize.js       # 窗口缩放工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── scroll-to.js    # 滚动工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── uav.js          # 无人机相关工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── validate.js     # 验证工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── zhy.js          # 智洋定制工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── dict/           # 字典工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── generator/      # 代码生成工具</span></span>
<span class="line"><span style="color:#babed8;">│   ├── views/              # 页面组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.vue       # 首页</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── login.vue       # 登录页</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── register.vue    # 注册页</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── redirect.vue    # 重定向页</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── sharing.vue     # 分享页</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── adminIndex/     # 管理首页</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── components/     # 页面级组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── dashboard/      # 仪表板</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── document/       # 文档管理</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── error/          # 错误页面</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── hydmodel/       # 水文模型</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── ipm/            # IPM模块</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── map/            # 地图相关页面</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── matrix/         # 矩阵管理页面</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── monitor/        # 监控页面</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── screenPage/     # 大屏页面</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── index.vue   # 大屏入口</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── homePage/   # 首页大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── siquan/     # 四全大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── qfg/    # 全覆盖大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── qth/    # 全天候大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── qys/    # 全要素大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   └── qzq/    # 全周期大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── sizhi/      # 四制大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── fazhi/  # 法制大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── jizhi/  # 机制大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── tizhi/  # 体制大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   └── zerenzhi/ # 责任制大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── siyuNew/    # 四预大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── yuan/   # 预案大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── yubao/  # 预报大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   ├── yujing/ # 预警大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   │   └── yuyan/  # 预演大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   └── siguan/     # 四管大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │       ├── sg-anquan/   # 安全管理大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │       ├── sg-chuxian/  # 除险大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │       ├── sg-tijian/   # 体检大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   │       └── sg-weihu/    # 维护大屏</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── system/         # 系统管理</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── tool/           # 工具页面</span></span>
<span class="line"><span style="color:#babed8;">│   ├── App.vue             # 根组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── main.js             # 入口文件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── mixin.js            # 全局混入</span></span>
<span class="line"><span style="color:#babed8;">│   ├── permission.js       # 权限控制</span></span>
<span class="line"><span style="color:#babed8;">│   └── settings.js         # 项目设置</span></span>
<span class="line"><span style="color:#babed8;">├── .editorconfig           # 编辑器配置</span></span>
<span class="line"><span style="color:#babed8;">├── .eslintignore           # ESLint忽略文件</span></span>
<span class="line"><span style="color:#babed8;">├── .eslintrc.js            # ESLint配置</span></span>
<span class="line"><span style="color:#babed8;">├── .gitignore              # Git忽略文件</span></span>
<span class="line"><span style="color:#babed8;">├── babel.config.js         # Babel配置</span></span>
<span class="line"><span style="color:#babed8;">├── package.json            # 项目依赖和脚本</span></span>
<span class="line"><span style="color:#babed8;">├── package-lock.json       # 依赖版本锁定</span></span>
<span class="line"><span style="color:#babed8;">├── README.md               # 项目说明（24行）</span></span>
<span class="line"><span style="color:#babed8;">└── vue.config.js           # Vue CLI配置（200行）</span></span></code></pre></div><h2 id="核心目录详解" tabindex="-1">核心目录详解 <a class="header-anchor" href="#核心目录详解" aria-label="Permalink to &quot;核心目录详解&quot;">​</a></h2><h3 id="_1-src-api-api接口层" tabindex="-1">1. <code>src/api</code> - API接口层 <a class="header-anchor" href="#_1-src-api-api接口层" aria-label="Permalink to &quot;1. \`src/api\` - API接口层&quot;">​</a></h3><p>按业务模块组织API接口，支持水库管理的各个功能领域：</p><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/api/monitor/waterLevel.js - 水位监测API</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> request </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/utils/request</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">getWaterLevelData</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">params</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/monitor/water-level/list</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">get</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">params</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">getRealtimeWaterLevel</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">stationId</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">/monitor/water-level/realtime/</span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">stationId</span><span style="color:#89DDFF;">}\`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">get</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/api/screen/dashboard.js - 大屏数据API</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> request </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/utils/request</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">getScreenData</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">screenType</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">/screen/data/</span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">screenType</span><span style="color:#89DDFF;">}\`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">get</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">getDigitalTwinData</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/digital-twin/scene-data</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">get</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_2-src-components-组件库" tabindex="-1">2. <code>src/components</code> - 组件库 <a class="header-anchor" href="#_2-src-components-组件库" aria-label="Permalink to &quot;2. \`src/components\` - 组件库&quot;">​</a></h3><h4 id="基础组件" tabindex="-1">基础组件 <a class="header-anchor" href="#基础组件" aria-label="Permalink to &quot;基础组件&quot;">​</a></h4><ul><li><code>Pagination</code> - 分页组件，支持大数据量展示</li><li><code>FileUpload/ImageUpload</code> - 文件上传组件</li><li><code>Editor</code> - 富文本编辑器</li><li><code>DictTag/DictData</code> - 字典数据展示组件</li></ul><h4 id="业务组件" tabindex="-1">业务组件 <a class="header-anchor" href="#业务组件" aria-label="Permalink to &quot;业务组件&quot;">​</a></h4><ul><li><code>DigitalTwin</code> - 数字孪生3D展示组件</li><li><code>ScreenCommonDialog</code> - 大屏通用弹窗组件</li><li><code>CustomFileUpload</code> - 自定义文件上传组件</li></ul><h4 id="布局组件" tabindex="-1">布局组件 <a class="header-anchor" href="#布局组件" aria-label="Permalink to &quot;布局组件&quot;">​</a></h4><ul><li><code>RightToolbar</code> - 右侧工具栏</li><li><code>Breadcrumb</code> - 面包屑导航</li><li><code>HeaderSearch</code> - 头部搜索</li></ul><h3 id="_3-src-views-页面视图层" tabindex="-1">3. <code>src/views</code> - 页面视图层 <a class="header-anchor" href="#_3-src-views-页面视图层" aria-label="Permalink to &quot;3. \`src/views\` - 页面视图层&quot;">​</a></h3><h4 id="大屏页面结构-screenpage" tabindex="-1">大屏页面结构 (<code>screenPage/</code>) <a class="header-anchor" href="#大屏页面结构-screenpage" aria-label="Permalink to &quot;大屏页面结构 (\`screenPage/\`)&quot;">​</a></h4><p>按照水库管理&quot;四全四制四预四管&quot;体系组织：</p><div class="language-vue"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">&lt;!-- src/views/screenPage/siquan/qfg/index.vue --&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">template</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">class</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">screen-container siquan-qfg</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">&lt;!-- 全覆盖大屏内容 --&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">class</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">screen-header</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">h1</span><span style="color:#89DDFF;">&gt;</span><span style="color:#BABED8;">全覆盖监测大屏</span><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">h1</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">class</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">screen-content</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">&lt;!-- 监测点位分布图 --&gt;</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">&lt;!-- 实时数据展示 --&gt;</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">&lt;!-- 预警信息 --&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">template</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">script</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SiquanQfgScreen</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">data</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      screenData</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{},</span></span>
<span class="line"><span style="color:#F07178;">      timer</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">null</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mounted</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">initScreen</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">startDataRefresh</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">beforeDestroy</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">stopDataRefresh</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">script</span><span style="color:#89DDFF;">&gt;</span></span></code></pre></div><h4 id="系统管理页面-system" tabindex="-1">系统管理页面 (<code>system/</code>) <a class="header-anchor" href="#系统管理页面-system" aria-label="Permalink to &quot;系统管理页面 (\`system/\`)&quot;">​</a></h4><ul><li>用户管理</li><li>角色权限</li><li>菜单管理</li><li>字典管理</li><li>系统配置</li></ul><h4 id="业务功能页面" tabindex="-1">业务功能页面 <a class="header-anchor" href="#业务功能页面" aria-label="Permalink to &quot;业务功能页面&quot;">​</a></h4><ul><li><code>hydmodel/</code> - 水文模型相关页面</li><li><code>monitor/</code> - 监控管理页面</li><li><code>matrix/</code> - 矩阵管理页面</li><li><code>map/</code> - 地图功能页面</li></ul><h3 id="_4-src-store-状态管理" tabindex="-1">4. <code>src/store</code> - 状态管理 <a class="header-anchor" href="#_4-src-store-状态管理" aria-label="Permalink to &quot;4. \`src/store\` - 状态管理&quot;">​</a></h3><p>使用Vuex模块化管理状态：</p><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/store/modules/map.js - 地图状态管理</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">namespaced</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">state</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">currentMap</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">null,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">layers</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> []</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">markers</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> []</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">viewerConfig</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F07178;">center</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">116.404</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.915</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F07178;">zoom</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mutations</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">SET_CURRENT_MAP</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">map</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">currentMap</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">map</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">ADD_LAYER</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">layer</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">layers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">layer</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">ADD_MARKER</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">marker</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">markers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">marker</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">actions</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">initMap</span><span style="color:#89DDFF;">({</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">commit</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">config</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 初始化地图</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">addMonitoringPoint</span><span style="color:#89DDFF;">({</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">commit</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">point</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 添加监测点</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/store/modules/screenParams.js - 大屏参数状态</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">namespaced</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">state</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">currentScreen</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">screenData</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{},</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">refreshInterval</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">30000</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">isFullscreen</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mutations</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">SET_CURRENT_SCREEN</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">screen</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">currentScreen</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">screen</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">UPDATE_SCREEN_DATA</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenData</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">...</span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenData</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">...</span><span style="color:#BABED8;">data</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_5-src-utils-工具函数库" tabindex="-1">5. <code>src/utils</code> - 工具函数库 <a class="header-anchor" href="#_5-src-utils-工具函数库" aria-label="Permalink to &quot;5. \`src/utils\` - 工具函数库&quot;">​</a></h3><h4 id="核心工具文件" tabindex="-1">核心工具文件 <a class="header-anchor" href="#核心工具文件" aria-label="Permalink to &quot;核心工具文件&quot;">​</a></h4><ul><li><code>request.js</code> - HTTP请求封装，包含拦截器配置</li><li><code>auth.js</code> - 认证相关工具（Token管理）</li><li><code>zhy.js</code> - 智洋定制工具函数（322行）</li><li><code>formatTime.js</code> - 时间格式化工具（317行）</li></ul><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/utils/request.js - 请求拦截器示例</span></span>
<span class="line"><span style="color:#BABED8;">service</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">interceptors</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">request</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">use</span><span style="color:#BABED8;">(</span><span style="color:#BABED8;font-style:italic;">config</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 添加认证Token</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#82AAFF;">getToken</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">&amp;&amp;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">headers</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">isToken</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">headers</span><span style="color:#F07178;">[</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Authorization</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">] </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Bearer </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">getToken</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 防重复提交</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">headers</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">repeatSubmit</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&amp;&amp;</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">method</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">===</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">post</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">||</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">method</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">===</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">put</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 防重复提交逻辑</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">config</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">error</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span></code></pre></div><h3 id="_6-public-static-config-js-系统配置" tabindex="-1">6. <code>public/static/config.js</code> - 系统配置 <a class="header-anchor" href="#_6-public-static-config-js-系统配置" aria-label="Permalink to &quot;6. \`public/static/config.js\` - 系统配置&quot;">​</a></h3><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 系统默认配置（1314行大型配置文件）</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> systemDefault </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 系统基础配置</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">systemName</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">现代化水库管理矩阵</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">version</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">3.6.3</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 地图配置</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mapConfig</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">defaultCenter</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">116.404</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.915</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">defaultZoom</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">cesiumToken</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your_cesium_token</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 监测站点配置</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">monitoringStations</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 大量监测站点配置</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 大屏配置</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">screenConfig</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">refreshInterval</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">30000</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">resolution</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">1920x1080</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="路由结构设计" tabindex="-1">路由结构设计 <a class="header-anchor" href="#路由结构设计" aria-label="Permalink to &quot;路由结构设计&quot;">​</a></h2><h3 id="路由层级规划" tabindex="-1">路由层级规划 <a class="header-anchor" href="#路由层级规划" aria-label="Permalink to &quot;路由层级规划&quot;">​</a></h3><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/router/index.js - 路由配置示例</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> constantRoutes </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 大屏路由（独立布局）</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">redirect</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">home</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/screenPage/index.vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">children</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">home</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/screenPage/homePage/index.vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">title</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">首页大屏</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 四全大屏路由</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">siquanQfg</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/screenPage/siquan/qfg/index</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">title</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">四全-全覆盖</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// ... 更多大屏路由</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 管理后台路由（Layout布局）</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/admin</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Layout</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">children</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 后台管理页面</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">]</span></span></code></pre></div><h3 id="路由元信息配置" tabindex="-1">路由元信息配置 <a class="header-anchor" href="#路由元信息配置" aria-label="Permalink to &quot;路由元信息配置&quot;">​</a></h3><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">title</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">title</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;">           </span><span style="color:#676E95;font-style:italic;">// 页面标题</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">icon</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">svg-name</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;">         </span><span style="color:#676E95;font-style:italic;">// 菜单图标</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">noCache</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;">            </span><span style="color:#676E95;font-style:italic;">// 是否缓存</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">requiresAuth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;">       </span><span style="color:#676E95;font-style:italic;">// 是否需要认证</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">roles</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">admin</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;">         </span><span style="color:#676E95;font-style:italic;">// 角色权限</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">permissions</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">system:user:view</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// 功能权限</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">activeMenu</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/system/user</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// 高亮菜单</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">breadcrumb</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#F07178;">         </span><span style="color:#676E95;font-style:italic;">// 面包屑显示</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="开发规范" tabindex="-1">开发规范 <a class="header-anchor" href="#开发规范" aria-label="Permalink to &quot;开发规范&quot;">​</a></h2><h3 id="命名规范" tabindex="-1">命名规范 <a class="header-anchor" href="#命名规范" aria-label="Permalink to &quot;命名规范&quot;">​</a></h3><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 文件命名：kebab-case</span></span>
<span class="line"><span style="color:#BABED8;">water</span><span style="color:#89DDFF;">-</span><span style="color:#BABED8;">level</span><span style="color:#89DDFF;">-</span><span style="color:#BABED8;">monitor</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">vue</span></span>
<span class="line"><span style="color:#BABED8;">digital</span><span style="color:#89DDFF;">-</span><span style="color:#BABED8;">twin</span><span style="color:#89DDFF;">-</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">js</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 组件名：PascalCase</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">WaterLevelMonitor</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 变量/方法：camelCase</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> waterLevelData </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> []</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> getWaterLevelData </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 常量：UPPER_SNAKE_CASE</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> MAX_RETRY_COUNT </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">3</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> API_BASE_URL </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/api</span><span style="color:#89DDFF;">&#39;</span></span></code></pre></div><h3 id="组件开发规范" tabindex="-1">组件开发规范 <a class="header-anchor" href="#组件开发规范" aria-label="Permalink to &quot;组件开发规范&quot;">​</a></h3><div class="language-vue"><button title="Copy Code" class="copy"></button><span class="lang">vue</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">&lt;!-- 组件模板规范 --&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">template</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">class</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">component-container</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">&lt;!-- 组件内容 --&gt;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">template</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">script</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">ComponentName</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">components</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">props</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// props定义</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">data</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 响应式数据</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">computed</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 计算属性</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">watch</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 监听器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">created</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 生命周期</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mounted</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// DOM挂载后</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">beforeDestroy</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 组件销毁前清理</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">methods</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 方法定义</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">script</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">style</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">lang</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">scss</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">scoped</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">component-container</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 样式定义</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;/</span><span style="color:#F07178;">style</span><span style="color:#89DDFF;">&gt;</span></span></code></pre></div><h3 id="样式开发规范" tabindex="-1">样式开发规范 <a class="header-anchor" href="#样式开发规范" aria-label="Permalink to &quot;样式开发规范&quot;">​</a></h3><div class="language-scss"><button title="Copy Code" class="copy"></button><span class="lang">scss</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 使用BEM命名法</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">screen-container</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&amp;</span><span style="color:#89DDFF;">__header</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 头部样式</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&amp;</span><span style="color:#89DDFF;">__content</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 内容样式</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&amp;</span><span style="color:#BABED8;">--fullscreen </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 全屏修饰符</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 响应式设计</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">@media</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#FFCB6B;">max-width</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1920px</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">screen-container</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#B2CCD6;">transform</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">scale</span><span style="color:#89DDFF;">(</span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">);</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="技术栈说明" tabindex="-1">技术栈说明 <a class="header-anchor" href="#技术栈说明" aria-label="Permalink to &quot;技术栈说明&quot;">​</a></h2><h3 id="核心依赖" tabindex="-1">核心依赖 <a class="header-anchor" href="#核心依赖" aria-label="Permalink to &quot;核心依赖&quot;">​</a></h3><ul><li><strong>Vue 2.6.12</strong> - 前端框架</li><li><strong>Element UI 2.15.14</strong> - UI组件库</li><li><strong>Vue Router 3.4.9</strong> - 路由管理</li><li><strong>Vuex 3.6.0</strong> - 状态管理</li><li><strong>Axios 0.24.0</strong> - HTTP请求库</li></ul><h3 id="业务相关依赖" tabindex="-1">业务相关依赖 <a class="header-anchor" href="#业务相关依赖" aria-label="Permalink to &quot;业务相关依赖&quot;">​</a></h3><ul><li><strong>Cesium 1.108.0</strong> - 3D地图引擎</li><li><strong>ECharts 5.4.0</strong> - 图表库</li><li><strong>ECharts-GL 2.0.9</strong> - 3D图表</li><li><strong>Leaflet 1.9.4</strong> - 2D地图库</li><li><strong>Three.js 0.178.0</strong> - 3D图形库</li></ul><h3 id="工具依赖" tabindex="-1">工具依赖 <a class="header-anchor" href="#工具依赖" aria-label="Permalink to &quot;工具依赖&quot;">​</a></h3><ul><li><strong>Day.js 1.11.13</strong> - 时间处理</li><li><strong>Lodash 4.17.21</strong> - 工具函数库</li><li><strong>File-saver 2.0.5</strong> - 文件下载</li><li><strong>JS-Cookie 3.0.1</strong> - Cookie管理</li></ul><p>这个项目结构体现了现代化水库管理系统的复杂性和专业性，通过模块化的组织方式确保了代码的可维护性和可扩展性。</p>`,54),e=[o];function t(c,r,y,D,F,i){return a(),n("div",null,e)}const E=s(p,[["render",t]]);export{d as __pageData,E as default};
