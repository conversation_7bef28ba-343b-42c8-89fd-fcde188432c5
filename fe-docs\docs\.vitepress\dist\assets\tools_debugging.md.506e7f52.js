import{_ as s,o as a,c as l,V as n}from"./chunks/framework.3d729ebc.js";const h=JSON.parse('{"title":"调试工具指南","description":"","frontmatter":{},"headers":[],"relativePath":"tools/debugging.md","filePath":"tools/debugging.md"}'),o={name:"tools/debugging.md"},p=n(`<h1 id="调试工具指南" tabindex="-1">调试工具指南 <a class="header-anchor" href="#调试工具指南" aria-label="Permalink to &quot;调试工具指南&quot;">​</a></h1><p>本文档介绍前端开发中常用的调试工具和技巧，帮助开发者快速定位和解决问题。</p><h2 id="浏览器开发者工具" tabindex="-1">浏览器开发者工具 <a class="header-anchor" href="#浏览器开发者工具" aria-label="Permalink to &quot;浏览器开发者工具&quot;">​</a></h2><h3 id="chrome-devtools" tabindex="-1">Chrome DevTools <a class="header-anchor" href="#chrome-devtools" aria-label="Permalink to &quot;Chrome DevTools&quot;">​</a></h3><p>Chrome DevTools 是最常用的前端调试工具，提供了丰富的调试功能。</p><h4 id="_1-elements-面板" tabindex="-1">1. Elements 面板 <a class="header-anchor" href="#_1-elements-面板" aria-label="Permalink to &quot;1. Elements 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：查看和编辑HTML/CSS</li><li><strong>快捷键</strong>：<code>F12</code> 或 <code>Ctrl + Shift + I</code></li><li><strong>使用技巧</strong>： <ul><li>右键元素选择&quot;检查&quot;快速定位</li><li>使用 <code>Ctrl + Shift + C</code> 选择元素</li><li>在 Styles 面板中实时修改CSS</li></ul></li></ul><h4 id="_2-console-面板" tabindex="-1">2. Console 面板 <a class="header-anchor" href="#_2-console-面板" aria-label="Permalink to &quot;2. Console 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：JavaScript 控制台，查看日志和错误</li><li><strong>常用方法</strong>：</li></ul><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 基础日志</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">普通日志</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">warn</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">警告信息</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">error</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">错误信息</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 分组日志</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">group</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">用户信息</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">姓名：张三</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">年龄：25</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">groupEnd</span><span style="color:#BABED8;">()</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 表格显示</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">table</span><span style="color:#BABED8;">([</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">张三</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">age</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">25</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">李四</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">age</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">30</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">])</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 性能测试</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">time</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">API请求</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// ... 执行代码</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">timeEnd</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">API请求</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span></code></pre></div><h4 id="_3-sources-面板" tabindex="-1">3. Sources 面板 <a class="header-anchor" href="#_3-sources-面板" aria-label="Permalink to &quot;3. Sources 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：JavaScript 调试</li><li><strong>断点调试</strong>： <ul><li>点击行号设置断点</li><li>条件断点：右键行号设置条件</li><li>使用 <code>debugger</code> 语句设置断点</li></ul></li></ul><h4 id="_4-network-面板" tabindex="-1">4. Network 面板 <a class="header-anchor" href="#_4-network-面板" aria-label="Permalink to &quot;4. Network 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：监控网络请求</li><li><strong>使用技巧</strong>： <ul><li>筛选请求类型（XHR、JS、CSS等）</li><li>查看请求详情和响应数据</li><li>模拟网络条件（慢速3G等）</li></ul></li></ul><h4 id="_5-performance-面板" tabindex="-1">5. Performance 面板 <a class="header-anchor" href="#_5-performance-面板" aria-label="Permalink to &quot;5. Performance 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：性能分析</li><li><strong>使用方法</strong>： <ol><li>点击录制按钮</li><li>执行需要分析的操作</li><li>停止录制查看性能报告</li></ol></li></ul><h3 id="firefox-developer-tools" tabindex="-1">Firefox Developer Tools <a class="header-anchor" href="#firefox-developer-tools" aria-label="Permalink to &quot;Firefox Developer Tools&quot;">​</a></h3><p>Firefox 开发者工具的独特功能：</p><h4 id="_1-css-grid-inspector" tabindex="-1">1. CSS Grid Inspector <a class="header-anchor" href="#_1-css-grid-inspector" aria-label="Permalink to &quot;1. CSS Grid Inspector&quot;">​</a></h4><ul><li><strong>功能</strong>：可视化CSS Grid布局</li><li><strong>使用</strong>：在Elements面板中点击Grid标识</li></ul><h4 id="_2-flexbox-inspector" tabindex="-1">2. Flexbox Inspector <a class="header-anchor" href="#_2-flexbox-inspector" aria-label="Permalink to &quot;2. Flexbox Inspector&quot;">​</a></h4><ul><li><strong>功能</strong>：可视化Flexbox布局</li><li><strong>使用</strong>：在Elements面板中点击Flex标识</li></ul><h2 id="vue-devtools" tabindex="-1">Vue DevTools <a class="header-anchor" href="#vue-devtools" aria-label="Permalink to &quot;Vue DevTools&quot;">​</a></h2><p>Vue DevTools 是Vue.js应用的专用调试工具。</p><h3 id="安装" tabindex="-1">安装 <a class="header-anchor" href="#安装" aria-label="Permalink to &quot;安装&quot;">​</a></h3><h4 id="浏览器扩展" tabindex="-1">浏览器扩展 <a class="header-anchor" href="#浏览器扩展" aria-label="Permalink to &quot;浏览器扩展&quot;">​</a></h4><ul><li><a href="https://chrome.google.com/webstore/detail/vuejs-devtools/nhdogjmejiglipccpnnnanhbledajbpd" target="_blank" rel="noreferrer">Chrome扩展</a></li><li><a href="https://addons.mozilla.org/en-US/firefox/addon/vue-js-devtools/" target="_blank" rel="noreferrer">Firefox扩展</a></li></ul><h4 id="独立应用" tabindex="-1">独立应用 <a class="header-anchor" href="#独立应用" aria-label="Permalink to &quot;独立应用&quot;">​</a></h4><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-g</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@vue/devtools</span></span>
<span class="line"><span style="color:#FFCB6B;">vue-devtools</span></span></code></pre></div><h3 id="主要功能" tabindex="-1">主要功能 <a class="header-anchor" href="#主要功能" aria-label="Permalink to &quot;主要功能&quot;">​</a></h3><h4 id="_1-components-面板" tabindex="-1">1. Components 面板 <a class="header-anchor" href="#_1-components-面板" aria-label="Permalink to &quot;1. Components 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：查看组件树和组件状态</li><li><strong>使用技巧</strong>： <ul><li>选择组件查看props、data、computed等</li><li>实时修改组件数据</li><li>查看组件层级关系</li></ul></li></ul><h4 id="_2-vuex-面板" tabindex="-1">2. Vuex 面板 <a class="header-anchor" href="#_2-vuex-面板" aria-label="Permalink to &quot;2. Vuex 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：Vuex状态管理调试</li><li><strong>功能特性</strong>： <ul><li>查看state、getters、mutations、actions</li><li>时间旅行调试</li><li>导入/导出状态</li></ul></li></ul><h4 id="_3-events-面板" tabindex="-1">3. Events 面板 <a class="header-anchor" href="#_3-events-面板" aria-label="Permalink to &quot;3. Events 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：监控Vue事件</li><li><strong>使用</strong>：查看组件间的事件通信</li></ul><h4 id="_4-performance-面板" tabindex="-1">4. Performance 面板 <a class="header-anchor" href="#_4-performance-面板" aria-label="Permalink to &quot;4. Performance 面板&quot;">​</a></h4><ul><li><strong>功能</strong>：Vue组件性能分析</li><li><strong>使用</strong>：分析组件渲染性能</li></ul><h3 id="配置示例" tabindex="-1">配置示例 <a class="header-anchor" href="#配置示例" aria-label="Permalink to &quot;配置示例&quot;">​</a></h3><p>在Vue应用中启用DevTools：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// main.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> Vue </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">vue</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 开发环境启用DevTools</span></span>
<span class="line"><span style="color:#BABED8;">Vue</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">devtools </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> process</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">env</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">NODE_ENV </span><span style="color:#89DDFF;">===</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">development</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 生产环境也启用（不推荐）</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// Vue.config.devtools = true</span></span></code></pre></div><h2 id="移动端调试" tabindex="-1">移动端调试 <a class="header-anchor" href="#移动端调试" aria-label="Permalink to &quot;移动端调试&quot;">​</a></h2><h3 id="_1-chrome-remote-debugging" tabindex="-1">1. Chrome Remote Debugging <a class="header-anchor" href="#_1-chrome-remote-debugging" aria-label="Permalink to &quot;1. Chrome Remote Debugging&quot;">​</a></h3><p>调试Android设备上的网页：</p><ol><li>在Android设备上启用USB调试</li><li>在Chrome中访问 <code>chrome://inspect</code></li><li>选择要调试的页面</li></ol><h3 id="_2-safari-web-inspector" tabindex="-1">2. Safari Web Inspector <a class="header-anchor" href="#_2-safari-web-inspector" aria-label="Permalink to &quot;2. Safari Web Inspector&quot;">​</a></h3><p>调试iOS设备上的网页：</p><ol><li>在iOS设备上启用Web检查器</li><li>在Mac上打开Safari的开发菜单</li><li>选择设备和页面进行调试</li></ol><h3 id="_3-模拟器调试" tabindex="-1">3. 模拟器调试 <a class="header-anchor" href="#_3-模拟器调试" aria-label="Permalink to &quot;3. 模拟器调试&quot;">​</a></h3><p>使用浏览器的设备模拟功能：</p><ul><li>Chrome：<code>F12</code> → 设备工具栏图标</li><li>Firefox：<code>F12</code> → 响应式设计模式</li></ul><h2 id="网络调试工具" tabindex="-1">网络调试工具 <a class="header-anchor" href="#网络调试工具" aria-label="Permalink to &quot;网络调试工具&quot;">​</a></h2><h3 id="_1-postman" tabindex="-1">1. Postman <a class="header-anchor" href="#_1-postman" aria-label="Permalink to &quot;1. Postman&quot;">​</a></h3><p>API接口测试工具：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 环境变量设置</span></span>
<span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">baseUrl</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">http://localhost:3000/api</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">token</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">your-auth-token</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 请求示例</span></span>
<span class="line"><span style="color:#BABED8;">GET </span><span style="color:#89DDFF;">{{</span><span style="color:#BABED8;">baseUrl</span><span style="color:#89DDFF;">}}/</span><span style="color:#BABED8;">users</span></span>
<span class="line"><span style="color:#FFCB6B;">Authorization</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Bearer </span><span style="color:#89DDFF;">{{</span><span style="color:#BABED8;">token</span><span style="color:#89DDFF;">}}</span></span></code></pre></div><h3 id="_2-charles-proxy" tabindex="-1">2. Charles Proxy <a class="header-anchor" href="#_2-charles-proxy" aria-label="Permalink to &quot;2. Charles Proxy&quot;">​</a></h3><p>网络代理调试工具：</p><ul><li><strong>功能</strong>：拦截和修改HTTP/HTTPS请求</li><li><strong>使用场景</strong>： <ul><li>模拟网络异常</li><li>修改API响应数据</li><li>分析网络性能</li></ul></li></ul><h3 id="_3-fiddler" tabindex="-1">3. Fiddler <a class="header-anchor" href="#_3-fiddler" aria-label="Permalink to &quot;3. Fiddler&quot;">​</a></h3><p>Windows平台的网络调试工具：</p><ul><li><strong>功能</strong>：HTTP/HTTPS流量监控</li><li><strong>特性</strong>： <ul><li>请求/响应修改</li><li>性能分析</li><li>安全测试</li></ul></li></ul><h2 id="调试技巧" tabindex="-1">调试技巧 <a class="header-anchor" href="#调试技巧" aria-label="Permalink to &quot;调试技巧&quot;">​</a></h2><h3 id="_1-断点调试" tabindex="-1">1. 断点调试 <a class="header-anchor" href="#_1-断点调试" aria-label="Permalink to &quot;1. 断点调试&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 条件断点</span></span>
<span class="line"><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">processData</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 只有当data.length &gt; 100时才触发断点</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">100</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">debugger</span><span style="color:#89DDFF;">;</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// 设置断点</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">map</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">item</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">value</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_2-日志调试" tabindex="-1">2. 日志调试 <a class="header-anchor" href="#_2-日志调试" aria-label="Permalink to &quot;2. 日志调试&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 结构化日志</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> debugLog </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">module</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">action</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">process</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">env</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">NODE_ENV</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">===</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">development</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">group</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">[</span><span style="color:#89DDFF;">\${</span><span style="color:#89DDFF;">module</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">] </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">action</span><span style="color:#89DDFF;">}\`</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Data:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Timestamp:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Date</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toISOString</span><span style="color:#F07178;">())</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">groupEnd</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#82AAFF;">debugLog</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">UserService</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">fetchUser</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">userId</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">123</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="_3-性能调试" tabindex="-1">3. 性能调试 <a class="header-anchor" href="#_3-性能调试" aria-label="Permalink to &quot;3. 性能调试&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 性能监控</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> performanceMonitor </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">start</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">label</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">time</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">label</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">mark</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">\`\${</span><span style="color:#BABED8;">label</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">-start</span><span style="color:#89DDFF;">\`</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">end</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">label</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">timeEnd</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">label</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">mark</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">\`\${</span><span style="color:#BABED8;">label</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">-end</span><span style="color:#89DDFF;">\`</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">measure</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">label</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">\`\${</span><span style="color:#BABED8;">label</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">-start</span><span style="color:#89DDFF;">\`</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">\`\${</span><span style="color:#BABED8;">label</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">-end</span><span style="color:#89DDFF;">\`</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#BABED8;">performanceMonitor</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">start</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">API请求</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">await</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">fetchUserData</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">performanceMonitor</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">end</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">API请求</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="_4-内存调试" tabindex="-1">4. 内存调试 <a class="header-anchor" href="#_4-内存调试" aria-label="Permalink to &quot;4. 内存调试&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 内存使用监控</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> memoryMonitor </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">memory</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      used</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">round</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">memory</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">usedJSHeapSize</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">/</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1048576</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">MB</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      total</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">round</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">memory</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">totalJSHeapSize</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">/</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1048576</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">MB</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      limit</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">round</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">performance</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">memory</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">jsHeapSizeLimit</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">/</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1048576</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">MB</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span></code></pre></div><h2 id="常见问题调试" tabindex="-1">常见问题调试 <a class="header-anchor" href="#常见问题调试" aria-label="Permalink to &quot;常见问题调试&quot;">​</a></h2><h3 id="_1-vue组件不更新" tabindex="-1">1. Vue组件不更新 <a class="header-anchor" href="#_1-vue组件不更新" aria-label="Permalink to &quot;1. Vue组件不更新&quot;">​</a></h3><p>检查清单：</p><ul><li>数据是否为响应式</li><li>是否直接修改了数组索引</li><li>是否使用了正确的Vue.set方法</li></ul><h3 id="_2-样式不生效" tabindex="-1">2. 样式不生效 <a class="header-anchor" href="#_2-样式不生效" aria-label="Permalink to &quot;2. 样式不生效&quot;">​</a></h3><p>检查清单：</p><ul><li>CSS选择器优先级</li><li>样式是否被覆盖</li><li>scoped样式作用域</li></ul><h3 id="_3-异步请求问题" tabindex="-1">3. 异步请求问题 <a class="header-anchor" href="#_3-异步请求问题" aria-label="Permalink to &quot;3. 异步请求问题&quot;">​</a></h3><p>检查清单：</p><ul><li>网络请求状态码</li><li>请求参数格式</li><li>跨域配置</li><li>错误处理逻辑</li></ul><h3 id="_4-性能问题" tabindex="-1">4. 性能问题 <a class="header-anchor" href="#_4-性能问题" aria-label="Permalink to &quot;4. 性能问题&quot;">​</a></h3><p>检查清单：</p><ul><li>组件重复渲染</li><li>大量DOM操作</li><li>内存泄漏</li><li>资源加载优化</li></ul><h2 id="调试最佳实践" tabindex="-1">调试最佳实践 <a class="header-anchor" href="#调试最佳实践" aria-label="Permalink to &quot;调试最佳实践&quot;">​</a></h2><ol><li><strong>渐进式调试</strong>：从简单到复杂，逐步缩小问题范围</li><li><strong>日志记录</strong>：在关键位置添加日志，便于问题追踪</li><li><strong>单元测试</strong>：编写测试用例，预防问题发生</li><li><strong>代码审查</strong>：通过代码审查发现潜在问题</li><li><strong>工具结合</strong>：结合多种调试工具，提高调试效率</li></ol><p>通过掌握这些调试工具和技巧，可以大大提高问题定位和解决的效率，提升开发体验。</p>`,86),e=[p];function t(r,c,i,D,y,F){return a(),l("div",null,e)}const d=s(o,[["render",t]]);export{h as __pageData,d as default};
