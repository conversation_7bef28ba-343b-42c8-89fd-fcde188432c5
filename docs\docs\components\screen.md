# 大屏开发指南

## 概述

大屏可视化是数据展示的重要方式，常用于监控数据、数据分析中心等场景

## 大屏自适应方案

大屏项目通常需要在不同尺寸的显示设备上保持良好的显示效果。提供了一套完善的自适应解决方案，支持两种缩放模式。

### 自适应原理

大屏自适应主要通过以下两种模式实现：

1. **FIT模式**：保持原有宽高比例，等比例缩放，确保内容完全可见（可能有留白）
2. **STRETCH模式**：拉伸适配，填满整个屏幕（可能导致变形）



### 使用方法
#### 1. 引入自适应混入

```js
import screenAdaptive from '@/utils/screenAdaptive.js'

export default {
  name: 'YourComponent',
  mixins: [screenAdaptive],
  // ...
}
```

#### 2. 设置基准尺寸

在开发大屏时，我们通常基于一个固定的设计尺寸进行开发（默认为1920×1080），然后通过自适应方案在不同尺寸的屏幕上进行缩放。

```js
// 在 screenAdaptive.js 中配置
const SCREEN_CONFIG = {
  // 设计稿基准尺寸
  DESIGN_WIDTH: 1920,
  DESIGN_HEIGHT: 1080,
  // ...
}
```

#### 3. 切换缩放模式

```vue
<template>
  <div>
    <button @click="setMode('fit')">FIT模式</button>
    <button @click="setMode('stretch')">STRETCH模式</button>
  </div>
</template>

<script>
export default {
  // ...
  methods: {
    setMode(mode) {
      // 切换缩放模式
      this.setScaleMode(mode)
    }
  }
}
</script>
```

#### 4. 获取缩放信息

```js
// 获取当前缩放信息
const scaleInfo = this.getScaleInfo()
console.log(`当前缩放比例: ${scaleInfo.scaleX} x ${scaleInfo.scaleY}`)
console.log(`屏幕尺寸: ${scaleInfo.screenWidth} x ${scaleInfo.screenHeight}`)
```

### 坐标转换

在某些交互场景下，需要进行屏幕坐标与设计稿坐标的转换：

```js
// 屏幕坐标转设计稿坐标
const designCoord = this.screenToDesignCoordinates(mouseX, mouseY)

// 设计稿坐标转屏幕坐标
const screenCoord = this.designToScreenCoordinates(designX, designY)
```

## 大屏布局最佳实践

### 基础布局结构

推荐的大屏布局结构如下：

```vue
<template>
  <div class="screen-container">
    <!-- 头部 -->
    <header class="screen-header">
      <h1>大屏标题</h1>
      <!-- 其他头部内容 -->
    </header>
    
    <!-- 主体内容 -->
    <main class="screen-content">
      <!-- 左侧区域 -->
      <div class="screen-left">
        <!-- 图表组件 -->
      </div>
      
      <!-- 中间区域 -->
      <div class="screen-center">
        <!-- 核心数据展示 -->
      </div>
      
      <!-- 右侧区域 -->
      <div class="screen-right">
        <!-- 图表组件 -->
      </div>
    </main>
    
    <!-- 底部 -->
    <footer class="screen-footer">
      <!-- 底部信息 -->
    </footer>
  </div>
</template>
```

### 完整示例代码

以下是一个完整的大屏自适应示例（App.vue），包含了自适应模式切换、布局结构和样式：

```vue
<template>
  <div id="app" class="screen-container">
    <!-- 头部标题 -->
    <header class="header">
      <h1>大屏自适应示例</h1>
      <div class="mode-controls">
        <button 
          @click="setMode('fit')" 
          :class="{ active: currentMode === 'fit' }"
        >
          FIT模式
        </button>
        <button 
          @click="setMode('stretch')" 
          :class="{ active: currentMode === 'stretch' }"
        >
          STRETCH模式
        </button>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="demo-section">
        <h3>左侧内容</h3>
        <div class="demo-chart">
          <div class="bar" style="height: 60%;"></div>
          <div class="bar" style="height: 80%;"></div>
          <div class="bar" style="height: 40%;"></div>
          <div class="bar" style="height: 90%;"></div>
        </div>
      </div>

      <div class="demo-section">
        <h3>中心内容</h3>
        <div class="center-content">
          <div class="metric">
            <span class="value">1,234</span>
            <span class="label">总数据</span>
          </div>
          <div class="metric">
            <span class="value">98.5%</span>
            <span class="label">成功率</span>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>右侧内容</h3>
        <div class="demo-grid">
          <div class="grid-item" v-for="i in 6" :key="i">{{ i }}</div>
        </div>
      </div>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
      <div class="scale-info">
        <span>当前模式: {{ currentMode }}</span>
        <span>缩放比例: {{ scaleInfo.scaleX.toFixed(2) }} x {{ scaleInfo.scaleY.toFixed(2) }}</span>
        <span>屏幕尺寸: {{ scaleInfo.screenWidth }} x {{ scaleInfo.screenHeight }}</span>
      </div>
    </footer>
  </div>
</template>

<script>
import screenAdaptive from './utils/screenAdaptive.js'

export default {
  name: 'App',
  mixins: [screenAdaptive],
  data() {
    return {
      currentMode: 'fit'
    }
  },
  computed: {
    scaleInfo() {
      return this.getScaleInfo()
    }
  },
  methods: {
    setMode(mode) {
      this.currentMode = mode
      this.setScaleMode(mode)
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  overflow: hidden;
  background: #0f172a;
  font-family: Arial, sans-serif;
}

#app {
  width: 1920px;
  height: 1080px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  height: 120px;
  background: rgba(30, 41, 59, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 60px;
  border-bottom: 2px solid #334155;
}

.header h1 {
  color: #60a5fa;
  font-size: 36px;
}

.mode-controls {
  display: flex;
  gap: 20px;
}

.mode-controls button {
  padding: 12px 24px;
  background: #334155;
  color: #e2e8f0;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.mode-controls button:hover {
  background: #475569;
}

.mode-controls button.active {
  background: #60a5fa;
  color: #ffffff;
}

/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  padding: 40px;
  gap: 40px;
}

.demo-section {
  flex: 1;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid #475569;
}

.demo-section h3 {
  color: #60a5fa;
  margin-bottom: 30px;
  font-size: 24px;
}

/* 图表样式 */
.demo-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 200px;
  padding: 20px 0;
}

.bar {
  width: 40px;
  background: linear-gradient(to top, #60a5fa, #34d399);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.bar:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* 中心内容 */
.center-content {
  text-align: center;
  padding: 40px 0;
}

.metric {
  margin: 30px 0;
}

.metric .value {
  display: block;
  font-size: 48px;
  font-weight: bold;
  color: #34d399;
  margin-bottom: 10px;
}

.metric .label {
  color: #94a3b8;
  font-size: 18px;
}

/* 网格样式 */
.demo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  height: 200px;
}

.grid-item {
  background: linear-gradient(135deg, #8b5cf6, #06b6d4);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  transition: transform 0.2s ease;
}

.grid-item:hover {
  transform: scale(1.05);
}

/* 底部样式 */
.footer {
  height: 80px;
  background: rgba(30, 41, 59, 0.8);
  display: flex;
  align-items: center;
  padding: 0 60px;
  border-top: 2px solid #334155;
}

.scale-info {
  display: flex;
  gap: 40px;
  color: #94a3b8;
  font-size: 16px;
}

.scale-info span {
  background: rgba(51, 65, 85, 0.5);
  padding: 8px 16px;
  border-radius: 6px;
}
</style>
```

### 效果预览

大屏自适应的两种模式效果对比：

#### FIT模式（等比例缩放）

在FIT模式下，大屏会保持原有宽高比例，整体等比例缩放，确保内容完全可见。当浏览器窗口比例与设计稿比例不一致时，会在两侧或上下方出现留白区域。

特点：
- 保持设计稿的原始比例
- 内容不会变形
- 可能会出现留白区域
- 适合对视觉效果要求较高的场景

#### STRETCH模式（拉伸适配）

在STRETCH模式下，大屏会拉伸适配整个浏览器窗口，填满所有空间。水平和垂直方向可能会有不同的缩放比例。

特点：
- 充分利用所有屏幕空间
- 不会出现留白区域
- 当浏览器窗口比例与设计稿差异较大时，内容可能会出现变形
- 适合对空间利用率要求较高的场景

### CSS样式建议

```css
/* 设置基准尺寸 */
.screen-container {
  width: 1920px;
  height: 1080px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.screen-header {
  height: 120px;
  background: rgba(30, 41, 59, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 60px;
  border-bottom: 2px solid #334155;
}

/* 主体内容 */
.screen-content {
  flex: 1;
  display: flex;
  padding: 40px;
  gap: 40px;
}

/* 左/中/右区域 */
.screen-left,
.screen-center,
.screen-right {
  flex: 1;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid #475569;
}

/* 底部样式 */
.screen-footer {
  height: 80px;
  background: rgba(30, 41, 59, 0.8);
  display: flex;
  align-items: center;
  padding: 0 60px;
  border-top: 2px solid #334155;
}
```

## 图表组件集成

大屏项目中，图表是核心展示元素。我们推荐使用ECharts作为图表库，并结合我们的自适应方案：
参考文档 图表组件 [图表组件](charts.md) 

```vue
<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from 'echarts'
import screenAdaptive from '@/utils/screenAdaptive.js'

export default {
  mixins: [screenAdaptive],
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
    // 监听自适应事件
    this.$on('screen-adapted', this.handleScreenAdapted)
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },
    updateChart() {
      // 图表配置
      const option = {
        // ...图表配置
      }
      this.chart.setOption(option)
    },
    handleScreenAdapted() {
      // 当屏幕自适应时，重新调整图表大小
      this.chart && this.chart.resize()
    }
  },
  beforeDestroy() {
    // 移除事件监听
    this.$off('screen-adapted', this.handleScreenAdapted)
    // 销毁图表实例
    this.chart && this.chart.dispose()
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 300px;
}
</style>
```



## 性能优化建议

大屏项目通常包含大量图表和实时数据，性能优化尤为重要：

1. **按需加载**：使用动态导入，仅加载当前需要的组件和数据
2. **数据处理**：大量数据在前端进行预处理和聚合，减少渲染压力
3. **防抖节流**：对实时数据更新和窗口调整事件进行防抖处理
4. **WebGL渲染**：对于大数据量图表，使用ECharts的WebGL渲染模式
5. **组件卸载**：确保在组件销毁时清理所有事件监听和定时器

## 最佳实践与注意事项

在使用大屏自适应方案时，请注意以下最佳实践和注意事项：

### 设计阶段

1. **统一设计基准**：确保设计师和开发人员使用相同的基准尺寸（通常为1920×1080）
2. **考虑不同比例**：在设计时考虑内容在不同屏幕比例下的展示效果
3. **关键内容居中**：将重要信息放置在中心区域，避免在某些比例下被裁剪
4. **设置安全区域**：类似电视设计，设置内容安全区域，避免重要内容靠近边缘
5. **响应式设计**：对于特别重要的信息，考虑使用响应式设计而非纯粹的缩放

### 开发阶段

1. **避免使用固定像素**：对于字体大小、边距等，可以使用相对单位（rem、em）或动态计算
2. **图表自适应**：确保图表组件监听自适应事件，及时调整大小
3. **性能监控**：大屏项目通常长时间运行，需要监控内存泄漏和性能问题
4. **优雅降级**：针对低配置设备，提供简化版的图表和动效
5. **预加载策略**：使用预加载和骨架屏，提高首屏加载速度

### 部署阶段

1. **全屏模式**：建议使用浏览器的全屏模式（F11）展示大屏
2. **硬件加速**：开启浏览器的硬件加速功能，提高渲染性能
3. **定时刷新**：对于需要7×24小时运行的大屏，可以设置定时自动刷新，避免长时间运行导致的内存问题
4. **环境测试**：在实际部署环境中进行充分测试，特别是不同尺寸和分辨率的显示设备
5. **降低亮度**：对于长时间展示的大屏，适当降低亮度可以延长设备寿命

### 常见陷阱

1. **过度动效**：过多的动画效果会导致性能问题，特别是在低配置设备上
2. **数据过载**：单个页面展示过多数据会导致理解困难和渲染性能下降
3. **忽略交互**：即使是展示型大屏，也需要考虑基本的交互体验，如数据筛选、图表联动等
4. **字体模糊**：缩放后的文字可能出现模糊，可以通过CSS的`text-rendering`和`font-smooth`属性优化
5. **忽略极端情况**：需要测试极端窗口尺寸下的展示效果，确保内容仍然可用

## 常见问题

### 1. 图表在缩放后变模糊

这通常是因为Canvas渲染的图表在缩放后产生了模糊。解决方案：

```js
// 在图表初始化时设置设备像素比
const chart = echarts.init(dom, null, {
  devicePixelRatio: window.devicePixelRatio
})
```

### 2. 自适应后图表未重新调整大小

确保在自适应事件触发后调用图表的resize方法：

```js
// 监听自适应事件
this.$on('screen-adapted', () => {
  this.chart && this.chart.resize()
})
```

### 3. 字体大小不适应缩放

对于需要随屏幕缩放的文字，可以使用rem或em单位，或在缩放时动态调整：

```js
// 根据缩放比例调整字体大小
const fontSize = 16 * this.scale
element.style.fontSize = `${fontSize}px`
```

## 参考资源

- [ECharts官方文档](https://echarts.apache.org/zh/index.html)

## 源码实现


下面是完整的自适应方案源代码（`screenAdaptive.js`），您可以直接在项目中使用：

<details>
<summary>展开查看代码</summary>

```js
/**
 * 大屏自适应 Mixin - 优化版
 * 采用整体缩放方案，支持多种缩放模式
 */

// 屏幕配置常量
const SCREEN_CONFIG = {
  // 设计稿基准尺寸
  DESIGN_WIDTH: 1920,
  DESIGN_HEIGHT: 1080,
  // 最小缩放比例
  MIN_SCALE: 0.1,
  // 最大缩放比例  
  MAX_SCALE: 5,
  // 缩放模式
  SCALE_MODE: {
    FIT: 'fit',           // 完全可见模式（可能有留白）
    STRETCH: 'stretch'    // 拉伸模式（可能变形）
  }
}

const screenAdaptive = {
  data() {
    return {
      // 整体缩放比例
      scale: 1,
      scaleX: 1,
      scaleY: 1,
      // 当前屏幕尺寸
      screenWidth: window.innerWidth,
      screenHeight: window.innerHeight,
      // 防抖定时器
      resizeTimer: null,
      // 容器偏移量
      offsetX: 0,
      offsetY: 0,
      // 缩放模式
      scaleMode: SCREEN_CONFIG.SCALE_MODE.FIT
    }
  },

  mounted() {
    this.initScreenAdaptive()
  },

  beforeDestroy() {
    this.destroyScreenAdaptive()
  },

  methods: {
    // 初始化大屏自适应
    initScreenAdaptive() {
      this.calculateScale()
      this.applyScreenAdaptive()
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    // 销毁监听器
    destroyScreenAdaptive() {
      window.removeEventListener('resize', this.handleResize)
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
      }
    },

    // 处理窗口大小变化（防抖）
    handleResize() {
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
      }
      
      this.resizeTimer = setTimeout(() => {
        this.updateScreenDimensions()
        this.calculateScale()
        this.applyScreenAdaptive()
      }, 100)
    },

    // 更新屏幕尺寸
    updateScreenDimensions() {
      this.screenWidth = window.innerWidth
      this.screenHeight = window.innerHeight
    },

    // 设置缩放模式
    setScaleMode(mode) {
      if (Object.values(SCREEN_CONFIG.SCALE_MODE).includes(mode)) {
        this.scaleMode = mode
        this.calculateScale()
        this.applyScreenAdaptive()
      }
    },

    // 计算缩放比例
    calculateScale() {
      const { DESIGN_WIDTH, DESIGN_HEIGHT, MIN_SCALE, MAX_SCALE } = SCREEN_CONFIG
      
      // 计算基于宽度和高度的缩放比例
      const scaleX = this.screenWidth / DESIGN_WIDTH
      const scaleY = this.screenHeight / DESIGN_HEIGHT
      
      let scale = 1
      
      // 根据缩放模式计算
      switch (this.scaleMode) {
        case SCREEN_CONFIG.SCALE_MODE.FIT:
          // 完全可见模式：使用较小的缩放比例
          scale = Math.min(scaleX, scaleY)
          this.scaleX = scale
          this.scaleY = scale
          break
          
        case SCREEN_CONFIG.SCALE_MODE.STRETCH:
          // 拉伸模式：分别使用 X 和 Y 的缩放比例
          this.scaleX = scaleX
          this.scaleY = scaleY
          scale = Math.min(scaleX, scaleY) // 用于偏移计算
          break
      }
      
      // 限制缩放范围（仅在非拉伸模式下）
      if (this.scaleMode !== SCREEN_CONFIG.SCALE_MODE.STRETCH) {
        scale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale))
        this.scaleX = scale
        this.scaleY = scale
      }
      
      this.scale = scale
      
      // 计算居中偏移量
      if (this.scaleMode === SCREEN_CONFIG.SCALE_MODE.STRETCH) {
        // 拉伸模式下不需要偏移
        this.offsetX = 0
        this.offsetY = 0
      } else {
        const scaledWidth = DESIGN_WIDTH * scale
        const scaledHeight = DESIGN_HEIGHT * scale
        
        this.offsetX = (this.screenWidth - scaledWidth) / 2
        this.offsetY = (this.screenHeight - scaledHeight) / 2
      }
      
      console.log(`屏幕自适应信息:`)
      console.log(`- 屏幕尺寸: ${this.screenWidth} x ${this.screenHeight}`)
      console.log(`- 设计尺寸: ${DESIGN_WIDTH} x ${DESIGN_HEIGHT}`)
      console.log(`- 缩放模式: ${this.scaleMode}`)
      console.log(`- 缩放比例: X=${this.scaleX.toFixed(3)}, Y=${this.scaleY.toFixed(3)}`)
      console.log(`- 偏移量: X=${this.offsetX.toFixed(1)}, Y=${this.offsetY.toFixed(1)}`)
    },

    // 应用屏幕自适应
    applyScreenAdaptive() {
      // 获取根容器
      const container = this.$el
      if (!container) {
        console.warn('未找到根容器元素')
        return
      }

      // 应用变换
      let transform = ''
      
      if (this.scaleMode === SCREEN_CONFIG.SCALE_MODE.STRETCH) {
        // 拉伸模式：分别应用 X 和 Y 缩放
        transform = `scale(${this.scaleX}, ${this.scaleY})`
      } else {
        // 其他模式：整体缩放加偏移
        transform = `translate(${this.offsetX}px, ${this.offsetY}px) scale(${this.scale})`
      }
      
      container.style.transform = transform
      container.style.transformOrigin = '0 0'
      container.style.transition = 'transform 0.3s ease-out'
      
      // 设置容器尺寸
      if (this.scaleMode === SCREEN_CONFIG.SCALE_MODE.STRETCH) {
        // 拉伸模式：容器尺寸设为屏幕尺寸
        container.style.width = `${this.screenWidth / this.scaleX}px`
        container.style.height = `${this.screenHeight / this.scaleY}px`
      } else {
        // 其他模式：使用设计稿尺寸
        container.style.width = `${SCREEN_CONFIG.DESIGN_WIDTH}px`
        container.style.height = `${SCREEN_CONFIG.DESIGN_HEIGHT}px`
      }

      // 触发自定义事件
      this.$emit('screen-adapted', {
        scale: this.scale,
        scaleX: this.scaleX,
        scaleY: this.scaleY,
        offsetX: this.offsetX,
        offsetY: this.offsetY,
        screenWidth: this.screenWidth,
        screenHeight: this.screenHeight,
        designWidth: SCREEN_CONFIG.DESIGN_WIDTH,
        designHeight: SCREEN_CONFIG.DESIGN_HEIGHT,
        scaleMode: this.scaleMode
      })
    },

    // 手动触发缩放
    triggerResize() {
      this.handleResize()
    },

    // 获取当前缩放信息
    getScaleInfo() {
      return {
        scale: this.scale,
        scaleX: this.scaleX,
        scaleY: this.scaleY,
        offsetX: this.offsetX,
        offsetY: this.offsetY,
        screenWidth: this.screenWidth,
        screenHeight: this.screenHeight,
        designWidth: SCREEN_CONFIG.DESIGN_WIDTH,
        designHeight: SCREEN_CONFIG.DESIGN_HEIGHT,
        scaleMode: this.scaleMode
      }
    },

    // 将屏幕坐标转换为设计稿坐标
    screenToDesignCoordinates(screenX, screenY) {
      if (this.scaleMode === SCREEN_CONFIG.SCALE_MODE.STRETCH) {
        return {
          x: screenX / this.scaleX,
          y: screenY / this.scaleY
        }
      } else {
        return {
          x: (screenX - this.offsetX) / this.scale,
          y: (screenY - this.offsetY) / this.scale
        }
      }
    },

    // 将设计稿坐标转换为屏幕坐标
    designToScreenCoordinates(designX, designY) {
      if (this.scaleMode === SCREEN_CONFIG.SCALE_MODE.STRETCH) {
        return {
          x: designX * this.scaleX,
          y: designY * this.scaleY
        }
      } else {
        return {
          x: designX * this.scale + this.offsetX,
          y: designY * this.scale + this.offsetY
        }
      }
    }
  }
}

export default screenAdaptive
```
</details>
