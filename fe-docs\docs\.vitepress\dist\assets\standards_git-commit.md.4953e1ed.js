import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const g=JSON.parse('{"title":"Git提交规范","description":"","frontmatter":{},"headers":[],"relativePath":"standards/git-commit.md","filePath":"standards/git-commit.md"}'),o={name:"standards/git-commit.md"},e=l(`<h1 id="git提交规范" tabindex="-1">Git提交规范 <a class="header-anchor" href="#git提交规范" aria-label="Permalink to &quot;Git提交规范&quot;">​</a></h1><p>本文档定义了项目中Git提交的规范，包括提交信息格式、分支管理和工作流程，旨在保持版本历史清晰，便于代码审查和版本追踪。</p><h2 id="提交信息规范" tabindex="-1">提交信息规范 <a class="header-anchor" href="#提交信息规范" aria-label="Permalink to &quot;提交信息规范&quot;">​</a></h2><p>我们采用 <a href="https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit" target="_blank" rel="noreferrer">Angular提交规范</a> 作为标准格式，每个提交信息应包含<strong>头部</strong>、<strong>正文</strong>和<strong>页脚</strong>三个部分：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">&lt;type&gt;(&lt;scope&gt;): &lt;subject&gt;</span></span>
<span class="line"><span style="color:#babed8;">&lt;BLANK LINE&gt;</span></span>
<span class="line"><span style="color:#babed8;">&lt;body&gt;</span></span>
<span class="line"><span style="color:#babed8;">&lt;BLANK LINE&gt;</span></span>
<span class="line"><span style="color:#babed8;">&lt;footer&gt;</span></span></code></pre></div><h3 id="头部-必需" tabindex="-1">头部（必需） <a class="header-anchor" href="#头部-必需" aria-label="Permalink to &quot;头部（必需）&quot;">​</a></h3><p>头部是必需的，由类型（type）、作用域（scope，可选）和主题（subject）组成：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">&lt;type&gt;(&lt;scope&gt;): &lt;subject&gt;</span></span></code></pre></div><h4 id="类型-type" tabindex="-1">类型（Type） <a class="header-anchor" href="#类型-type" aria-label="Permalink to &quot;类型（Type）&quot;">​</a></h4><p>必须是以下类型之一：</p><ul><li><strong>feat</strong>: 新功能（feature）</li><li><strong>fix</strong>: 修复bug</li><li><strong>docs</strong>: 文档更新</li><li><strong>style</strong>: 代码风格调整（不影响代码运行的变动）</li><li><strong>refactor</strong>: 重构（既不是新增功能，也不是修复bug的代码变动）</li><li><strong>perf</strong>: 性能优化</li><li><strong>chore</strong>: 构建过程或辅助工具的变动</li><li><strong>revert</strong>: 回滚之前的提交</li></ul><h4 id="作用域-scope" tabindex="-1">作用域（Scope） <a class="header-anchor" href="#作用域-scope" aria-label="Permalink to &quot;作用域（Scope）&quot;">​</a></h4><p>作用域指定提交更改的部分，例如：</p><ul><li><strong>user</strong>: 用户相关功能</li><li><strong>auth</strong>: 认证相关功能</li><li><strong>api</strong>: API相关功能</li><li><strong>ui</strong>: UI组件相关</li><li><strong>config</strong>: 配置相关</li></ul><p>作用域可以根据项目实际情况灵活定义。</p><h4 id="主题-subject" tabindex="-1">主题（Subject） <a class="header-anchor" href="#主题-subject" aria-label="Permalink to &quot;主题（Subject）&quot;">​</a></h4><p>主题是对变更的简短描述：</p><ul><li>使用现在时态的动词开头：&quot;change&quot;而非&quot;changed&quot;或&quot;changes&quot;</li><li>第一个字母不要大写</li><li>结尾不加句号（.）</li><li>不超过50个字符</li></ul><h3 id="正文-可选" tabindex="-1">正文（可选） <a class="header-anchor" href="#正文-可选" aria-label="Permalink to &quot;正文（可选）&quot;">​</a></h3><p>正文是对变更的详细描述，解释为什么要做这个变更以及它与之前行为的对比：</p><ul><li>使用现在时态的动词</li><li>包含变更的动机，并与之前的行为进行对比</li><li>可以包含多个段落</li></ul><h3 id="页脚-可选" tabindex="-1">页脚（可选） <a class="header-anchor" href="#页脚-可选" aria-label="Permalink to &quot;页脚（可选）&quot;">​</a></h3><p>页脚应包含关闭的Issue引用和Breaking Changes的信息：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">Closes #123, #456</span></span>
<span class="line"><span style="color:#babed8;">BREAKING CHANGE: 描述API变更的内容</span></span></code></pre></div><h3 id="提交信息示例" tabindex="-1">提交信息示例 <a class="header-anchor" href="#提交信息示例" aria-label="Permalink to &quot;提交信息示例&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">feat(user): 添加用户头像上传功能</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">实现用户头像上传功能，支持裁剪和预览。</span></span>
<span class="line"><span style="color:#babed8;">添加了文件大小和类型的验证。</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">Closes #123</span></span></code></pre></div><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">fix(auth): 修复登录失败不显示错误信息的问题</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">当API返回401错误时，现在会在登录表单下方显示错误提示。</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">Closes #456</span></span></code></pre></div><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">docs(readme): 更新项目文档</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">更新了安装步骤和开发环境配置说明。</span></span></code></pre></div><h2 id="分支管理规范" tabindex="-1">分支管理规范 <a class="header-anchor" href="#分支管理规范" aria-label="Permalink to &quot;分支管理规范&quot;">​</a></h2><h3 id="分支命名" tabindex="-1">分支命名 <a class="header-anchor" href="#分支命名" aria-label="Permalink to &quot;分支命名&quot;">​</a></h3><ul><li><strong>master/main</strong>: 主分支，稳定的生产环境代码</li><li><strong>develop</strong>: 开发分支，最新的开发代码</li><li><strong>feature/xxx</strong>: 功能分支，用于开发新功能，如 <code>feature/user-avatar</code></li><li><strong>bugfix/xxx</strong>: 修复分支，用于修复非紧急bug，如 <code>bugfix/login-error</code></li><li><strong>hotfix/xxx</strong>: 热修复分支，用于修复紧急生产环境bug，如 <code>hotfix/critical-auth-issue</code></li><li><strong>release/xxx</strong>: 发布分支，用于版本发布前的准备工作，如 <code>release/v1.2.0</code></li></ul><h3 id="分支工作流" tabindex="-1">分支工作流 <a class="header-anchor" href="#分支工作流" aria-label="Permalink to &quot;分支工作流&quot;">​</a></h3><p>我们采用 <a href="https://nvie.com/posts/a-successful-git-branching-model/" target="_blank" rel="noreferrer">Git Flow</a> 工作流：</p><ol><li>从 <code>develop</code> 分支创建 <code>feature/xxx</code> 分支进行功能开发</li><li>功能开发完成后，提交 Pull Request 到 <code>develop</code> 分支</li><li>代码审查通过后，合并到 <code>develop</code> 分支</li><li>当准备发布时，从 <code>develop</code> 分支创建 <code>release/xxx</code> 分支</li><li>在 <code>release/xxx</code> 分支上进行最终修复</li><li>完成后，将 <code>release/xxx</code> 分支同时合并到 <code>master</code> 和 <code>develop</code> 分支</li><li>在 <code>master</code> 分支上打标签（tag）标记版本</li><li>如果生产环境出现紧急bug，从 <code>master</code> 分支创建 <code>hotfix/xxx</code> 分支</li><li>修复完成后，将 <code>hotfix/xxx</code> 分支同时合并到 <code>master</code> 和 <code>develop</code> 分支</li></ol><h2 id="提交前检查" tabindex="-1">提交前检查 <a class="header-anchor" href="#提交前检查" aria-label="Permalink to &quot;提交前检查&quot;">​</a></h2><p>在提交代码前，请确保：</p><ol><li>代码符合项目的编码规范（通过lint检查）</li><li>提交信息符合上述规范</li><li>不包含调试代码或注释</li><li>不包含敏感信息（密码、密钥等）</li></ol><h2 id="自动化工具" tabindex="-1">自动化工具 <a class="header-anchor" href="#自动化工具" aria-label="Permalink to &quot;自动化工具&quot;">​</a></h2><p>为确保提交规范的执行，我们使用以下工具：</p><h3 id="commitlint" tabindex="-1">Commitlint <a class="header-anchor" href="#commitlint" aria-label="Permalink to &quot;Commitlint&quot;">​</a></h3><p>用于检查提交信息是否符合规范：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@commitlint/cli</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@commitlint/config-conventional</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 配置文件 commitlint.config.js</span></span>
<span class="line"><span style="color:#FFCB6B;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">=</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">extends:</span><span style="color:#BABED8;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@commitlint/config-conventional</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span></span>
<span class="line"><span style="color:#BABED8;">};</span></span></code></pre></div><h3 id="commitizen" tabindex="-1">Commitizen <a class="header-anchor" href="#commitizen" aria-label="Permalink to &quot;Commitizen&quot;">​</a></h3><p>用于生成符合规范的提交信息：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commitizen</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cz-conventional-changelog</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 配置</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># package.json</span></span>
<span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&quot;scripts&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#FFCB6B;">&quot;commit&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">cz</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&quot;config&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#FFCB6B;">&quot;commitizen&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#FFCB6B;">&quot;path&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">cz-conventional-changelog</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    }</span></span>
<span class="line"><span style="color:#BABED8;">  }</span></span>
<span class="line"><span style="color:#BABED8;">}</span></span></code></pre></div><h3 id="husky" tabindex="-1">Husky <a class="header-anchor" href="#husky" aria-label="Permalink to &quot;Husky&quot;">​</a></h3><p>用于在提交前运行检查：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 配置</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># package.json</span></span>
<span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&quot;husky&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#FFCB6B;">&quot;hooks&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#FFCB6B;">&quot;commit-msg&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">commitlint -E HUSKY_GIT_PARAMS</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#FFCB6B;">&quot;pre-commit&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">lint-staged</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  }</span></span>
<span class="line"><span style="color:#BABED8;">}</span></span></code></pre></div><h2 id="总结" tabindex="-1">总结 <a class="header-anchor" href="#总结" aria-label="Permalink to &quot;总结&quot;">​</a></h2><p>遵循一致的Git提交规范有助于：</p><ul><li>自动生成更新日志（CHANGELOG）</li><li>简化版本管理</li><li>方便团队协作和代码审查</li><li>提高项目可维护性</li></ul><p>每个开发人员都应该熟悉并遵循这些规范。如有任何问题或建议，请在团队会议中提出讨论。</p>`,52),t=[e];function p(c,i,r,d,y,h){return a(),n("div",null,t)}const b=s(o,[["render",p]]);export{g as __pageData,b as default};
