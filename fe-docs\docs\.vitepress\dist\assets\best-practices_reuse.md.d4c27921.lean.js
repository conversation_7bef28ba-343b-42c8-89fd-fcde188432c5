import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"Vue.js 代码复用最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/reuse.md","filePath":"best-practices/reuse.md"}'),p={name:"best-practices/reuse.md"},o=l("",77),e=[o];function t(c,r,D,F,y,i){return n(),a("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
