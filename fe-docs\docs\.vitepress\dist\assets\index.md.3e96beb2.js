import{_ as e,o as t,c as a,V as i}from"./chunks/framework.3d729ebc.js";const u=JSON.parse('{"title":"前端技术开发文档","description":"","frontmatter":{"layout":"home","title":"前端技术开发文档","hero":{"name":"前端技术开发文档","image":{"src":"/logo.jpeg","alt":"前端技术文档"},"actions":[{"theme":"brand","text":"快速开始","link":"/guide/"},{"theme":"alt","text":"组件库","link":"/components/"},{"theme":"alt","text":"规范标准","link":"/standards/"}]},"features":[{"icon":"📋","title":"全面的开发指南","details":"包含项目初始化、目录结构、开发流程等全面指南"},{"icon":"🧩","title":"丰富的组件库","details":"提供基础组件、业务组件及自定义组件的详细说明与使用示例"},{"icon":"🚀","title":"最佳实践","details":"前端性能优化、代码复用、状态管理等最佳实践指南"},{"icon":"📐","title":"规范标准","details":"编码规范、Git工作流、代码审查、文档规范等标准化指南"},{"icon":"🔧","title":"工具配置","details":"VS Code配置、调试工具、包管理、代码质量工具等开发环境配置"},{"icon":"🌏","title":"Cesium 3D地图引擎","details":"强大的3D地球可视化引擎，支持地理空间数据展示与交互"}]},"headers":[],"relativePath":"index.md","filePath":"index.md"}'),l={name:"index.md"},s=i('<div class="main-container"><div class="main-content"><h1 id="前端技术文档" tabindex="-1">前端技术文档 <a class="header-anchor" href="#前端技术文档" aria-label="Permalink to &quot;前端技术文档&quot;">​</a></h1><p>欢迎使用公司前端技术文档！本文档旨在为前端开发团队提供统一的开发规范、技术指南和最佳实践，帮助团队提高开发效率，保持代码质量和一致性。</p><h2 id="快速导航" tabindex="-1">快速导航 <a class="header-anchor" href="#快速导航" aria-label="Permalink to &quot;快速导航&quot;">​</a></h2><ul><li><a href="/guide/">开发指南</a> - 快速入门、项目结构和开发流程</li><li><a href="/components/">组件库</a> - 基础组件、业务组件和自定义组件</li><li><a href="/best-practices/">最佳实践</a> - 性能优化、代码复用和状态管理</li><li><a href="/standards/">规范标准</a> - 编码规范、Git工作流和代码审查</li><li><a href="/tools/">工具配置</a> - VS Code配置、调试工具、代码质量工具</li><li><a href="/cesium/">Cesium教程</a> - Cesium介绍、常用操作和实战案例</li></ul><div class="tip custom-block"><p class="custom-block-title">提示</p><p>本文档持续更新中，如有问题或建议，请联系前端开发团队。</p></div></div></div>',1),o=[s];function n(c,d,r,h,m,_){return t(),a("div",null,o)}const f=e(l,[["render",n]]);export{u as __pageData,f as default};
