export default {
    title: '前端技术开发文档',
    // description: '内部前端开发规范与技术文档',
    head: [
        ['link', { rel: 'icon', type: 'image/svg+xml', href: '/favicon.svg' }],
        ['link', { rel: 'apple-touch-icon', href: '/logo.png' }],
        ['meta', { name: 'theme-color', content: '#0ea5e9' }],
    ],
    vite: {
        server: {
            port: 5566,
            open: true,
        },
    },
    themeConfig: {
        logo: '/logo.jpeg',
        nav: [
            { text: '首页', link: '/' },
            { text: '指南', link: '/guide/' },
            { text: '组件', link: '/components/' },
            { text: '最佳实践', link: '/best-practices/' },
            { text: '规范标准', link: '/standards/' },
            { text: '工具配置', link: '/tools/' },
            { text: 'Cesium', link: '/cesium/' },
        ],
        search: {
            provider: 'local',
            options: {
                translations: {
                    button: {
                        buttonText: '搜索文档',
                        buttonAriaLabel: '搜索文档',
                    },
                    modal: {
                        noResultsText: '无法找到相关结果',
                        resetButtonTitle: '清除查询条件',
                        footer: {
                            selectText: '选择',
                            navigateText: '切换',
                            closeText: '关闭',
                        },
                    },
                },
            },
        },
        sidebar: {
            '/guide/': [
                {
                    text: '开发指南',
                    items: [
                        { text: '快速开始', link: '/guide/' },
                        { text: '项目结构', link: '/guide/project-structure' },
                        { text: '开发流程', link: '/guide/development-process' },
                        { text: '后台管理开发', link: '/guide/admin-development' },
                    ],
                },
            ],
            '/components/': [
                {
                    text: '组件库',
                    items: [
                        { text: '组件概览', link: '/components/' },
                        {
                            text: '业务组件',
                            collapsed: false,
                            items: [
                                { text: '业务组件总览', link: '/components/business' },
                                { text: '字典标签组件', link: '/components/business/dict-tag' },
                                { text: '字典选择器', link: '/components/business/dict-select' },
                                {
                                    text: '自定义文件上传',
                                    link: '/components/business/custom-file-upload',
                                },
                                { text: '图片上传组件', link: '/components/business/image-upload' },
                                {
                                    text: '自定义数字输入框',
                                    link: '/components/business/input-number',
                                },
                                {
                                    text: '自定义文本输入框',
                                    link: '/components/business/input-word',
                                },
                                { text: '文件预览组件', link: '/components/business/file-preview' },
                                {
                                    text: '地图可视化组件',
                                    link: '/components/business/map-visualization',
                                },
                            ],
                        },
                        { text: '表单组件', link: '/components/form' },
                        { text: '表格组件', link: '/components/table' },
                        { text: '图表组件', link: '/components/charts' },
                        {
                            text: '全局指令',
                            collapsed: false,
                            items: [
                                { text: '指令概览', link: '/components/directives/index' },
                                { text: '表格高度', link: '/components/directives/table-height' },
                                { text: '权限控制', link: '/components/directives/permission' },
                                { text: '弹窗拖拽', link: '/components/directives/drag-dialog' },
                                { text: '防抖处理', link: '/components/directives/debounce' },
                                { text: '节流处理', link: '/components/directives/throttle' },
                                { text: '一键复制', link: '/components/directives/copy' },
                            ],
                        },
                        { text: '大屏开发', link: '/components/screen' },
                    ],
                },
            ],
            '/best-practices/': [
                {
                    text: '最佳实践',
                    items: [
                        { text: '概述', link: '/best-practices/' },
                        { text: '性能优化', link: '/best-practices/performance' },
                        { text: '代码复用', link: '/best-practices/reuse' },
                        { text: '路由管理', link: '/best-practices/routing' },
                        { text: '组件通信', link: '/best-practices/component-communication' },
                        { text: '异步数据处理', link: '/best-practices/async-data' },
                        { text: '模块化开发', link: '/best-practices/modular-development' },
                        { text: '错误处理', link: '/best-practices/error-handling' },
                        { text: '国际化实现', link: '/best-practices/i18n' },
                        { text: 'Vue组件设计', link: '/best-practices/component-design' },
                        { text: 'Vuex最佳实践', link: '/best-practices/vuex-best-practices' },
                        { text: 'Vue项目结构', link: '/best-practices/project-structure' },
                        { text: 'API请求封装', link: '/best-practices/api-request' },
                        { text: '工具函数', link: '/best-practices/utils' },
                    ],
                },
            ],
            '/standards/': [
                {
                    text: '规范标准',
                    items: [
                        { text: '规范概述', link: '/standards/' },
                        {
                            text: '编码规范',
                            collapsed: false,
                            items: [
                                { text: 'JavaScript规范', link: '/standards/js-standard' },
                                { text: 'CSS规范', link: '/standards/css-standard' },
                                { text: 'HTML规范', link: '/standards/html-standard' },
                                { text: 'Vue开发规范', link: '/standards/vue-standard' },
                            ],
                        },
                        { text: 'Git提交规范', link: '/standards/git-commit' },
                        { text: 'Git工作流', link: '/standards/git-workflow' },
                        { text: '代码审查', link: '/standards/code-review' },
                        { text: '文档规范', link: '/standards/documentation' },
                    ],
                },
            ],
            '/tools/': [
                {
                    text: '开发工具配置',
                    items: [
                        { text: '工具概览', link: '/tools/' },
                        { text: 'VS Code配置', link: '/tools/vscode' },
                        { text: '调试工具', link: '/tools/debugging' },
                        { text: '包管理工具', link: '/tools/package-manager' },
                    ],
                },
                {
                    text: '代码质量工具',
                    collapsed: false,
                    items: [
                        { text: 'ESLint配置', link: '/tools/eslint' },
                        { text: 'Prettier配置', link: '/tools/prettier' },
                        { text: 'Husky配置', link: '/tools/husky' },
                    ],
                },
            ],
            '/cesium/': [
                {
                    text: 'Cesium 3D地图引擎',
                    items: [
                        { text: '简介', link: '/cesium/' },
                        { text: '基础配置', link: '/cesium/basics' },
                        { text: '核心概念', link: '/cesium/concepts' },
                        { text: '常用操作', link: '/cesium/operations' },
                    ],
                },
                {
                    text: '实战案例',
                    collapsed: false,
                    items: [{ text: '智慧水务监控', link: '/cesium/examples/water-monitor' }],
                },
            ],
        },
        footer: {
            copyright: 'Copyright © 2025 智洋上水',
        },
    },
}
