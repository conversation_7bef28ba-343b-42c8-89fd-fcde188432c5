import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"包管理工具指南","description":"","frontmatter":{},"headers":[],"relativePath":"tools/package-manager.md","filePath":"tools/package-manager.md"}'),p={name:"tools/package-manager.md"},o=l(`<h1 id="包管理工具指南" tabindex="-1">包管理工具指南 <a class="header-anchor" href="#包管理工具指南" aria-label="Permalink to &quot;包管理工具指南&quot;">​</a></h1><p>本文档介绍前端项目中包管理工具的使用规范，包括npm、yarn的配置和最佳实践。</p><h2 id="包管理工具对比" tabindex="-1">包管理工具对比 <a class="header-anchor" href="#包管理工具对比" aria-label="Permalink to &quot;包管理工具对比&quot;">​</a></h2><h3 id="npm-vs-yarn-vs-pnpm" tabindex="-1">npm vs yarn vs pnpm <a class="header-anchor" href="#npm-vs-yarn-vs-pnpm" aria-label="Permalink to &quot;npm vs yarn vs pnpm&quot;">​</a></h3><table><thead><tr><th>特性</th><th>npm</th><th>yarn</th><th>pnpm</th></tr></thead><tbody><tr><td>安装速度</td><td>中等</td><td>快</td><td>最快</td></tr><tr><td>磁盘空间</td><td>大</td><td>大</td><td>小</td></tr><tr><td>离线安装</td><td>支持</td><td>支持</td><td>支持</td></tr><tr><td>工作区支持</td><td>支持</td><td>支持</td><td>支持</td></tr><tr><td>锁文件</td><td>package-lock.json</td><td>yarn.lock</td><td>pnpm-lock.yaml</td></tr></tbody></table><h2 id="npm-使用指南" tabindex="-1">npm 使用指南 <a class="header-anchor" href="#npm-使用指南" aria-label="Permalink to &quot;npm 使用指南&quot;">​</a></h2><h3 id="基础配置" tabindex="-1">基础配置 <a class="header-anchor" href="#基础配置" aria-label="Permalink to &quot;基础配置&quot;">​</a></h3><h4 id="_1-设置镜像源" tabindex="-1">1. 设置镜像源 <a class="header-anchor" href="#_1-设置镜像源" aria-label="Permalink to &quot;1. 设置镜像源&quot;">​</a></h4><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 查看当前源</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">get</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">registry</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 设置淘宝镜像</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">https://registry.npmmirror.com</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 设置公司内部源</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">http://172.20.2.11:8081/repository/npm/</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 恢复官方源</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">https://registry.npmjs.org</span></span></code></pre></div><h4 id="_2-配置文件-npmrc" tabindex="-1">2. 配置文件 .npmrc <a class="header-anchor" href="#_2-配置文件-npmrc" aria-label="Permalink to &quot;2. 配置文件 .npmrc&quot;">​</a></h4><p>项目根目录创建 <code>.npmrc</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 镜像源配置</span></span>
<span class="line"><span style="color:#BABED8;">registry</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">http://172.20.2.11:8081/repository/npm/</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 安装配置</span></span>
<span class="line"><span style="color:#BABED8;">save-exact</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">true</span></span>
<span class="line"><span style="color:#BABED8;">package-lock</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">true</span></span>
<span class="line"><span style="color:#BABED8;">shrinkwrap</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">false</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 缓存配置</span></span>
<span class="line"><span style="color:#BABED8;">cache</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">/path/to/npm-cache</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 代理配置（如需要）</span></span>
<span class="line"><span style="color:#BABED8;">proxy</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">http://proxy.company.com:8080</span></span>
<span class="line"><span style="color:#BABED8;">https-proxy</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">http://proxy.company.com:8080</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 私有包配置</span></span>
<span class="line"><span style="color:#FFCB6B;">@company:registry</span><span style="color:#BABED8;">=http://npm.company.internal</span></span>
<span class="line"><span style="color:#FFCB6B;">//npm.company.internal/:_authToken</span><span style="color:#BABED8;">=your-auth-token</span></span></code></pre></div><h3 id="常用命令" tabindex="-1">常用命令 <a class="header-anchor" href="#常用命令" aria-label="Permalink to &quot;常用命令&quot;">​</a></h3><h4 id="_1-包安装" tabindex="-1">1. 包安装 <a class="header-anchor" href="#_1-包安装" aria-label="Permalink to &quot;1. 包安装&quot;">​</a></h4><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装所有依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 安装生产依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name@version</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 安装开发依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-D</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 全局安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-g</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 安装指定版本</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name@1.2.3</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 从特定源安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">https://registry.npmmirror.com</span></span></code></pre></div><h4 id="_2-包管理" tabindex="-1">2. 包管理 <a class="header-anchor" href="#_2-包管理" aria-label="Permalink to &quot;2. 包管理&quot;">​</a></h4><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 查看已安装包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--depth=0</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;"># 只显示顶级包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-g</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--depth=0</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;"># 全局包</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看包信息</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">info</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">view</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">versions</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--json</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 更新包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">update</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">update</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 卸载包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">uninstall</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">uninstall</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span></span></code></pre></div><h4 id="_3-脚本执行" tabindex="-1">3. 脚本执行 <a class="header-anchor" href="#_3-脚本执行" aria-label="Permalink to &quot;3. 脚本执行&quot;">​</a></h4><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 运行脚本</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">script-name</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">dev</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">build</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看可用脚本</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 传递参数</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">build</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--mode</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">production</span></span></code></pre></div><h3 id="package-json-配置" tabindex="-1">package.json 配置 <a class="header-anchor" href="#package-json-配置" aria-label="Permalink to &quot;package.json 配置&quot;">​</a></h3><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">name</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">project-name</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">version</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">1.0.0</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">description</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">项目描述</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">main</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">index.js</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">scripts</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">dev</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">vue-cli-service serve</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">build</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">vue-cli-service build</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">lint</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">vue-cli-service lint</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">test</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">jest</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">precommit</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">lint-staged</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">dependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vue</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^2.6.14</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vue-router</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^3.5.1</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vuex</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^3.6.2</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">devDependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">@vue/cli-service</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^4.5.0</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">eslint</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^7.32.0</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">prettier</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^2.3.2</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">engines</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">node</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">&gt;=14.0.0</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">npm</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">&gt;=6.0.0</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">browserslist</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">&gt; 1%</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">last 2 versions</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">not dead</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">]</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="yarn-使用指南" tabindex="-1">yarn 使用指南 <a class="header-anchor" href="#yarn-使用指南" aria-label="Permalink to &quot;yarn 使用指南&quot;">​</a></h2><h3 id="安装和配置" tabindex="-1">安装和配置 <a class="header-anchor" href="#安装和配置" aria-label="Permalink to &quot;安装和配置&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装yarn</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-g</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">yarn</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看版本</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--version</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 设置镜像源</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">https://registry.npmmirror.com</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看配置</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span></span></code></pre></div><h3 id="常用命令-1" tabindex="-1">常用命令 <a class="header-anchor" href="#常用命令-1" aria-label="Permalink to &quot;常用命令&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;"># 简写</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 添加依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name@version</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--dev</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 升级依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">upgrade</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">upgrade</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 移除依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">remove</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行脚本</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">script-name</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">dev</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;"># 可省略run</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看依赖树</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--depth=0</span></span></code></pre></div><h3 id="yarn-lock-文件" tabindex="-1">yarn.lock 文件 <a class="header-anchor" href="#yarn-lock-文件" aria-label="Permalink to &quot;yarn.lock 文件&quot;">​</a></h3><p>yarn.lock 文件锁定依赖版本，确保团队成员安装相同版本的依赖：</p><div class="language-yaml"><button title="Copy Code" class="copy"></button><span class="lang">yaml</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># yarn.lock 示例</span></span>
<span class="line"><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">@babel/core@^7.12.3</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C3E88D;">version &quot;7.15.5&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C3E88D;">resolved &quot;https://registry.yarnpkg.com/@babel/core/-/core-7.15.5.tgz&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C3E88D;">integrity sha512-pYgXxiwAgQpgM1bNkZsDEq85f0ggXMA5L7c+o3tskGMh2BunCI9QUwB9Z4jpvXUOuMdyGKiGKQiRe11VS6Jzvg==</span></span></code></pre></div><h2 id="私有包管理" tabindex="-1">私有包管理 <a class="header-anchor" href="#私有包管理" aria-label="Permalink to &quot;私有包管理&quot;">​</a></h2><h3 id="_1-发布私有包" tabindex="-1">1. 发布私有包 <a class="header-anchor" href="#_1-发布私有包" aria-label="Permalink to &quot;1. 发布私有包&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 登录私有仓库</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">login</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--registry=http://npm.company.internal</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 发布包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">publish</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--registry=http://npm.company.internal</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 发布带标签的版本</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">publish</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--tag</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">beta</span></span></code></pre></div><h3 id="_2-使用私有包" tabindex="-1">2. 使用私有包 <a class="header-anchor" href="#_2-使用私有包" aria-label="Permalink to &quot;2. 使用私有包&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装私有包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@company/package-name</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--registry=http://npm.company.internal</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 在package.json中配置</span></span>
<span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&quot;dependencies&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#FFCB6B;">&quot;@company/ui-components&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^1.0.0</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">}</span></span></code></pre></div><h3 id="_3-npmrc-配置私有源" tabindex="-1">3. .npmrc 配置私有源 <a class="header-anchor" href="#_3-npmrc-配置私有源" aria-label="Permalink to &quot;3. .npmrc 配置私有源&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 全局配置</span></span>
<span class="line"><span style="color:#FFCB6B;">@company:registry</span><span style="color:#BABED8;">=http://npm.company.internal</span></span>
<span class="line"><span style="color:#FFCB6B;">//npm.company.internal/:_authToken</span><span style="color:#BABED8;">=</span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">NPM_TOKEN</span><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 项目配置</span></span>
<span class="line"><span style="color:#BABED8;">registry</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">https://registry.npmjs.org</span></span>
<span class="line"><span style="color:#FFCB6B;">@company:registry</span><span style="color:#BABED8;">=http://npm.company.internal</span></span></code></pre></div><h2 id="依赖管理最佳实践" tabindex="-1">依赖管理最佳实践 <a class="header-anchor" href="#依赖管理最佳实践" aria-label="Permalink to &quot;依赖管理最佳实践&quot;">​</a></h2><h3 id="_1-版本管理" tabindex="-1">1. 版本管理 <a class="header-anchor" href="#_1-版本管理" aria-label="Permalink to &quot;1. 版本管理&quot;">​</a></h3><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">dependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vue</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">2.6.14</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 精确版本</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">axios</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^0.21.1</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 兼容版本</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">lodash</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">~4.17.21</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 补丁版本</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><p>版本号说明：</p><ul><li><code>1.2.3</code>：精确版本</li><li><code>^1.2.3</code>：兼容版本（1.x.x）</li><li><code>~1.2.3</code>：补丁版本（1.2.x）</li><li><code>&gt;=1.2.3</code>：大于等于版本</li></ul><h3 id="_2-依赖分类" tabindex="-1">2. 依赖分类 <a class="header-anchor" href="#_2-依赖分类" aria-label="Permalink to &quot;2. 依赖分类&quot;">​</a></h3><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">dependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 生产环境依赖</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vue</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^2.6.14</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vue-router</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^3.5.1</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">devDependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 开发环境依赖</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">webpack</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^5.0.0</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">eslint</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^7.32.0</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">peerDependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 同伴依赖</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">vue</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^2.6.0</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">optionalDependencies</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 可选依赖</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">fsevents</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">^2.3.2</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-安全检查" tabindex="-1">3. 安全检查 <a class="header-anchor" href="#_3-安全检查" aria-label="Permalink to &quot;3. 安全检查&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 检查安全漏洞</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">audit</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 自动修复</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">audit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">fix</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 强制修复</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">audit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">fix</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--force</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看详细信息</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">audit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--json</span></span></code></pre></div><h3 id="_4-清理和优化" tabindex="-1">4. 清理和优化 <a class="header-anchor" href="#_4-清理和优化" aria-label="Permalink to &quot;4. 清理和优化&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 清理缓存</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cache</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">clean</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--force</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 清理node_modules</span></span>
<span class="line"><span style="color:#FFCB6B;">rm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-rf</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">node_modules</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-lock.json</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查过期包</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">outdated</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看包大小</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">ls</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--depth=0</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--json</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">|</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">jq</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">.dependencies | to_entries | map({name: .key, version: .value.version})</span><span style="color:#89DDFF;">&#39;</span></span></code></pre></div><h2 id="工作区管理" tabindex="-1">工作区管理 <a class="header-anchor" href="#工作区管理" aria-label="Permalink to &quot;工作区管理&quot;">​</a></h2><h3 id="_1-npm-workspaces" tabindex="-1">1. npm workspaces <a class="header-anchor" href="#_1-npm-workspaces" aria-label="Permalink to &quot;1. npm workspaces&quot;">​</a></h3><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">name</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">monorepo</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">workspaces</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">packages/*</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">apps/*</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">]</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装所有工作区依赖</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 在特定工作区运行命令</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">build</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--workspace=package-a</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 在所有工作区运行命令</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--workspaces</span></span></code></pre></div><h3 id="_2-yarn-workspaces" tabindex="-1">2. yarn workspaces <a class="header-anchor" href="#_2-yarn-workspaces" aria-label="Permalink to &quot;2. yarn workspaces&quot;">​</a></h3><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">name</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">monorepo</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">private</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">true,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">workspaces</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">packages</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">packages/*</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 添加依赖到特定工作区</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">workspace</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-a</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lodash</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行工作区命令</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">workspace</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-a</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">build</span></span></code></pre></div><h2 id="常见问题解决" tabindex="-1">常见问题解决 <a class="header-anchor" href="#常见问题解决" aria-label="Permalink to &quot;常见问题解决&quot;">​</a></h2><h3 id="_1-依赖冲突" tabindex="-1">1. 依赖冲突 <a class="header-anchor" href="#_1-依赖冲突" aria-label="Permalink to &quot;1. 依赖冲突&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 查看依赖树</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">ls</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">package-name</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 强制解析</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--legacy-peer-deps</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 使用resolutions（yarn）</span></span>
<span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">&quot;resolutions&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#FFCB6B;">&quot;package-name&quot;</span><span style="color:#82AAFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">1.2.3</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">}</span></span></code></pre></div><h3 id="_2-网络问题" tabindex="-1">2. 网络问题 <a class="header-anchor" href="#_2-网络问题" aria-label="Permalink to &quot;2. 网络问题&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 增加超时时间</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">timeout</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">60000</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 使用代理</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">proxy</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">http://proxy.company.com:8080</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">https-proxy</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">http://proxy.company.com:8080</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 跳过SSL验证（不推荐）</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">strict-ssl</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">false</span></span></code></pre></div><h3 id="_3-权限问题" tabindex="-1">3. 权限问题 <a class="header-anchor" href="#_3-权限问题" aria-label="Permalink to &quot;3. 权限问题&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 修改npm全局目录</span></span>
<span class="line"><span style="color:#FFCB6B;">mkdir</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">~/.npm-global</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">config</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">prefix</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">~/.npm-global</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 添加到PATH</span></span>
<span class="line"><span style="color:#C792EA;">export</span><span style="color:#BABED8;"> PATH</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">~/.npm-global/bin:</span><span style="color:#BABED8;">$PATH</span></span></code></pre></div><h2 id="团队协作规范" tabindex="-1">团队协作规范 <a class="header-anchor" href="#团队协作规范" aria-label="Permalink to &quot;团队协作规范&quot;">​</a></h2><h3 id="_1-锁文件管理" tabindex="-1">1. 锁文件管理 <a class="header-anchor" href="#_1-锁文件管理" aria-label="Permalink to &quot;1. 锁文件管理&quot;">​</a></h3><ul><li>始终提交锁文件（package-lock.json、yarn.lock）</li><li>不要手动修改锁文件</li><li>团队使用相同的包管理工具</li></ul><h3 id="_2-依赖更新策略" tabindex="-1">2. 依赖更新策略 <a class="header-anchor" href="#_2-依赖更新策略" aria-label="Permalink to &quot;2. 依赖更新策略&quot;">​</a></h3><ul><li>定期更新依赖包</li><li>重大版本更新需要团队讨论</li><li>使用自动化工具检查过期依赖</li></ul><h3 id="_3-私有包发布流程" tabindex="-1">3. 私有包发布流程 <a class="header-anchor" href="#_3-私有包发布流程" aria-label="Permalink to &quot;3. 私有包发布流程&quot;">​</a></h3><ol><li>版本号遵循语义化版本规范</li><li>编写详细的CHANGELOG</li><li>进行充分的测试</li><li>使用CI/CD自动发布</li></ol><p>通过规范的包管理，可以确保项目依赖的稳定性和团队开发的一致性。</p>`,69),e=[o];function t(c,r,y,D,i,B){return a(),n("div",null,e)}const C=s(p,[["render",t]]);export{E as __pageData,C as default};
