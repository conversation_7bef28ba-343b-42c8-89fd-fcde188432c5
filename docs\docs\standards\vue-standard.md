# Vue开发规范

本文档定义了项目中Vue.js代码的编写规范，旨在保持代码风格一致，提高代码质量和可维护性。本规范主要针对Vue 2.x版本。

## 组件命名

### 组件文件命名

- 单文件组件的文件名应使用大驼峰命名法（PascalCase）
- 基础组件使用 `Base` 前缀
- 单例组件使用 `The` 前缀
- 紧密耦合的组件使用父组件名作为前缀

```
# 基础组件
BaseButton.vue
BaseIcon.vue
BaseInput.vue

# 单例组件
TheHeader.vue
TheSidebar.vue
TheFooter.vue

# 紧密耦合的组件
UserProfileCard.vue
UserProfileAvatar.vue
UserProfileStats.vue
```

### 组件名称

- 组件名应该是多个单词的，根组件 `App` 除外
- 在模板中使用组件时，使用kebab-case（短横线分隔命名）
- 在JS/TS中引用组件时，使用PascalCase（大驼峰命名法）

```vue
<!-- 在模板中使用 -->
<template>
  <div>
    <user-profile></user-profile>
    <base-button></base-button>
  </div>
</template>

<script>
// 在JS中引用
import UserProfile from './UserProfile.vue';
import BaseButton from './BaseButton.vue';

export default {
  components: {
    UserProfile,
    BaseButton
  }
}
</script>
```

## 组件结构

### 单文件组件结构

单文件组件应当按照以下顺序组织代码：

1. `<template>`
2. `<script>`
3. `<style>`

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
  // 脚本内容
</script>

<style>
  /* 样式内容 */
</style>
```

### 组件/实例选项顺序

组件/实例的选项应当有统一的顺序：

```js
export default {
  // 1. 组件名称
  name: 'UserProfile',
  
  // 2. 组件混入
  mixins: [],
  
  // 3. 组件继承
  extends: {},
  
  // 4. 组件注册
  components: {},
  
  // 5. 组件属性
  props: {},
  
  // 6. 数据
  data() {
    return {}
  },
  
  // 7. 计算属性
  computed: {},
  
  // 8. 监听属性
  watch: {},
  
  // 9. 生命周期钩子（按照它们被调用的顺序）
  beforeCreate() {},
  created() {},
  beforeMount() {},
  mounted() {},
  beforeUpdate() {},
  updated() {},
  activated() {},
  deactivated() {},
  beforeDestroy() {},
  destroyed() {},
  
  // 10. 方法
  methods: {},
  
  // 11. 渲染函数
  render(h) {}
}
```

## Props定义

### Props声明

- 必须使用详细的对象定义，包括类型、默认值和验证
- 必须为所有的 props 提供默认值，除非是必填项
- 使用 camelCase 命名 props，在模板中使用 kebab-case

```js
props: {
  // 基础类型检查
  propA: Number,
  
  // 多种可能的类型
  propB: [String, Number],
  
  // 必填的字符串
  propC: {
    type: String,
    required: true
  },
  
  // 带有默认值的数字
  propD: {
    type: Number,
    default: 100
  },
  
  // 带有默认值的对象
  propE: {
    type: Object,
    default() {
      return { message: 'hello' }
    }
  },
  
  // 自定义验证函数
  propF: {
    validator(value) {
      return ['success', 'warning', 'danger'].includes(value)
    }
  }
}
```

### Props传递

- 在模板中使用 kebab-case 传递 props
- 布尔值的 props，当值为 `true` 时可以省略值
- 尽量使用静态 props，避免过度使用动态 props

```vue
<template>
  <user-profile
    :user-id="userId"
    :user-name="userName"
    :is-admin
    required
  ></user-profile>
</template>
```

## 数据管理

### Data定义

- 组件的 `data` 必须是一个函数
- 初始化所有需要的数据属性，即使值为空
- 使用有意义的名称，避免使用单字母或缩写

```js
data() {
  return {
    user: null,
    userList: [],
    isLoading: false,
    searchQuery: '',
    pagination: {
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  }
}
```

### 计算属性

- 使用计算属性进行数据转换，而不是在模板中使用复杂表达式
- 计算属性应当是纯函数，不要有副作用
- 为计算属性提供有意义的名称

```js
computed: {
  // 好的做法
  fullName() {
    return `${this.firstName} ${this.lastName}`
  },
  
  filteredItems() {
    return this.items.filter(item => item.isActive)
  },
  
  // 不好的做法 - 有副作用
  badComputed() {
    this.sideEffect = 'changed' // 副作用
    return this.value
  }
}
```

### 监听器

- 使用监听器响应数据变化
- 避免在监听器中执行过于复杂的操作
- 需要深度监听时使用 `deep: true` 选项

```js
watch: {
  // 简单监听
  searchQuery(newValue, oldValue) {
    this.fetchData()
  },
  
  // 对象深度监听
  userProfile: {
    handler(newValue, oldValue) {
      this.updateProfile()
    },
    deep: true
  },
  
  // 立即执行的监听器
  userId: {
    handler: 'fetchUserData',
    immediate: true
  }
}
```

## 模板规范

### 指令使用

- 指令缩写：用 `:` 表示 `v-bind:`，用 `@` 表示 `v-on:`
- `v-for` 必须搭配 `key` 使用，且避免使用 `index` 作为 key
- `v-if` 和 `v-for` 不要一起使用在同一元素上
- 使用 `v-show` 进行频繁切换的元素

```vue
<template>
  <!-- 好的做法 -->
  <div>
    <button :disabled="isDisabled" @click="handleClick">点击</button>
    
    <ul>
      <li v-for="item in items" :key="item.id">{{ item.name }}</li>
    </ul>
    
    <div v-if="shouldShow">内容</div>
    <div v-show="toggleFrequently">频繁切换的内容</div>
  </div>
  
  <!-- 不好的做法 -->
  <div>
    <button v-bind:disabled="isDisabled" v-on:click="handleClick">点击</button>
    
    <ul>
      <li v-for="(item, index) in items" :key="index">{{ item.name }}</li>
    </ul>
    
    <div v-if="shouldShow" v-for="item in items">{{ item.name }}</div>
  </div>
</template>
```

### 模板表达式

- 模板表达式应当简单
- 复杂的表达式应当使用计算属性或方法
- 避免在模板中使用复杂的JavaScript表达式

```vue
<template>
  <!-- 好的做法 -->
  <div>
    <p>{{ formattedDate }}</p>
    <p>{{ item.name }}</p>
    <p>{{ isActive ? 'Active' : 'Inactive' }}</p>
  </div>
  
  <!-- 不好的做法 -->
  <div>
    <p>{{ new Date(item.date).toLocaleDateString() }}</p>
    <p>{{ item.firstName + ' ' + item.lastName }}</p>
    <p>{{ items.filter(item => item.isActive).map(item => item.name).join(', ') }}</p>
  </div>
</template>

<script>
export default {
  computed: {
    formattedDate() {
      return new Date(this.item.date).toLocaleDateString()
    }
  }
}
</script>
```

## 样式规范

### 样式作用域

- 优先使用 `scoped` 特性或 CSS Modules 实现样式隔离
- 避免使用全局样式污染
- 使用 BEM 命名约定增强样式的可维护性

```vue
<style lang="scss" scoped>
.user-card {
  padding: 20px;
  
  &__header {
    margin-bottom: 16px;
  }
  
  &__title {
    font-size: 18px;
    
    &--highlighted {
      color: #3498db;
    }
  }
}
</style>
```

### 样式变量

- 使用CSS变量或SCSS变量定义主题颜色、字体等
- 保持样式的一致性
- 避免硬编码颜色值和尺寸

```vue
<style lang="scss">
/* 在一个集中的文件中定义变量 */
$color-primary: #3498db;
$color-secondary: #2ecc71;
$font-size-base: 16px;
$spacing-unit: 8px;

.component {
  color: $color-primary;
  font-size: $font-size-base;
  margin-bottom: $spacing-unit * 2;
}
</style>
```

## 方法规范

### 方法命名

- 使用动词或动词短语命名方法
- 事件处理函数使用 `handle` 前缀
- API调用方法使用 `fetch`、`get`、`update` 等前缀

```js
methods: {
  // 事件处理函数
  handleClick() {
    // ...
  },
  
  handleSubmit() {
    // ...
  },
  
  // API调用
  async fetchUserData() {
    // ...
  },
  
  async updateProfile() {
    // ...
  },
  
  // 工具方法
  formatDate(date) {
    // ...
  },
  
  validateForm() {
    // ...
  }
}
```

### 方法实现

- 方法应当简短且专注于单一职责
- 避免副作用，除非方法的目的就是产生副作用
- 处理异步操作时使用 async/await

```js
methods: {
  // 好的做法
  async fetchUserData() {
    this.isLoading = true;
    try {
      const response = await this.$api.users.getById(this.userId);
      this.user = response.data;
    } catch (error) {
      this.$message.error('获取用户数据失败');
      console.error(error);
    } finally {
      this.isLoading = false;
    }
  },
  
  // 不好的做法 - 职责不单一
  handleEverything() {
    this.validateForm();
    this.updateUserProfile();
    this.navigateToNextPage();
  }
}
```

## 生命周期钩子

### 钩子使用

- 在 `created` 中进行数据初始化和API调用
- 在 `mounted` 中进行DOM操作和第三方库初始化
- 在 `beforeDestroy` 中清理定时器、事件监听器等

```js
export default {
  created() {
    // 数据初始化
    this.fetchInitialData();
  },
  
  mounted() {
    // DOM操作
    this.initChart();
    
    // 添加事件监听
    window.addEventListener('resize', this.handleResize);
  },
  
  beforeDestroy() {
    // 清理工作
    window.removeEventListener('resize', this.handleResize);
    
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
}
```

## 路由规范

### 路由配置

- 路由路径使用kebab-case（短横线分隔命名）
- 路由名称使用camelCase（小驼峰命名法）
- 使用路由元信息（meta）存储与路由相关的数据

```js
const routes = [
  {
    path: '/user-profile',
    name: 'userProfile',
    component: UserProfile,
    meta: {
      requiresAuth: true,
      title: '用户资料'
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: Settings,
    meta: {
      requiresAuth: true,
      title: '设置'
    }
  }
]
```

### 路由导航

- 优先使用命名路由进行导航
- 使用路由参数传递简单数据
- 使用查询参数传递可选数据

```js
// 好的做法
this.$router.push({ name: 'userProfile', params: { id: 123 } });
this.$router.push({ name: 'search', query: { keyword: 'vue' } });

// 不好的做法
this.$router.push('/user-profile/123');
```

## Vuex使用规范

### Store结构

- 按模块组织Vuex store
- 使用命名空间隔离模块
- 遵循Vuex的操作流程：state -> getters -> mutations -> actions

```js
// store/modules/user.js
export default {
  namespaced: true,
  
  state: {
    user: null,
    isLoading: false,
    error: null
  },
  
  getters: {
    isAdmin: state => state.user && state.user.role === 'admin'
  },
  
  mutations: {
    SET_USER(state, user) {
      state.user = user;
    },
    SET_LOADING(state, isLoading) {
      state.isLoading = isLoading;
    },
    SET_ERROR(state, error) {
      state.error = error;
    }
  },
  
  actions: {
    async fetchUser({ commit }, userId) {
      commit('SET_LOADING', true);
      commit('SET_ERROR', null);
      
      try {
        const response = await api.users.getById(userId);
        commit('SET_USER', response.data);
      } catch (error) {
        commit('SET_ERROR', error.message);
        console.error(error);
      } finally {
        commit('SET_LOADING', false);
      }
    }
  }
};
```

### Store使用

- 使用 `mapState`, `mapGetters`, `mapMutations`, `mapActions` 辅助函数
- 在组件中明确指定命名空间
- 避免在组件中直接修改 state

```vue
<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  computed: {
    // 从state映射
    ...mapState('user', ['user', 'isLoading']),
    
    // 从getters映射
    ...mapGetters('user', ['isAdmin'])
  },
  
  methods: {
    // 从actions映射
    ...mapActions('user', ['fetchUser']),
    
    // 组件方法
    loadUserData() {
      this.fetchUser(this.userId);
    }
  },
  
  created() {
    this.loadUserData();
  }
}
</script>
```

## 性能优化

### 组件优化

- 使用 `v-show` 代替 `v-if` 进行频繁切换的元素
- 为 `v-for` 设置适当的 `key`
- 避免同时使用 `v-for` 和 `v-if`
- 使用 `keep-alive` 缓存组件
- 使用计算属性进行数据过滤和转换

```vue
<template>
  <div>
    <!-- 缓存组件 -->
    <keep-alive>
      <component :is="currentComponent"></component>
    </keep-alive>
    
    <!-- 使用计算属性过滤 -->
    <ul>
      <li v-for="item in filteredItems" :key="item.id">
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  computed: {
    filteredItems() {
      return this.items.filter(item => item.isActive);
    }
  }
}
</script>
```

### 懒加载

- 路由组件使用懒加载
- 大型组件使用异步组件

```js
// 路由懒加载
const routes = [
  {
    path: '/user',
    name: 'user',
    component: () => import('./views/User.vue')
  }
];

// 异步组件
const AsyncComponent = () => ({
  component: import('./components/HeavyComponent.vue'),
  loading: LoadingComponent,
  error: ErrorComponent,
  delay: 200,
  timeout: 3000
});
```

## 错误处理

- 使用 try/catch 处理异步操作
- 为API调用提供错误处理
- 使用全局错误处理器捕获未处理的错误

```js
// 组件中的错误处理
async fetchData() {
  this.isLoading = true;
  this.error = null;
  
  try {
    const response = await this.$api.getData();
    this.data = response.data;
  } catch (error) {
    this.error = '获取数据失败，请稍后再试';
    console.error('Data fetching error:', error);
  } finally {
    this.isLoading = false;
  }
}

// 全局错误处理
Vue.config.errorHandler = function(err, vm, info) {
  console.error('Vue error:', err);
  console.error('Component:', vm);
  console.error('Info:', info);
  
  // 上报错误到监控服务
  errorTrackingService.report(err);
};
```

## 总结

遵循一致的Vue开发规范有助于提高代码质量、可读性和可维护性。每个开发人员都应该熟悉并遵循这些规范。如有任何问题或建议，请在团队会议中提出讨论。 