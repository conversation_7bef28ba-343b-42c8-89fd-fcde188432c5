# 🛠️ 工具函数库

本页面介绍业务开发中常用的工具函数，提供完整的函数说明、使用方法和源代码实现。

## 📋 函数列表

### 🔢 数字处理函数
- [数字格式化](#数字格式化)
- [金额格式化](#金额格式化)
- [数字范围限制](#数字范围限制)

### 📅 日期时间函数
- [日期格式化](#日期格式化)
- [相对时间格式化](#相对时间格式化)
- [日期计算](#日期计算)
- [周数计算](#周数计算)

### 📝 字符串处理函数
- [字符串截取](#字符串截取)
- [HTML标签过滤](#html标签过滤)
- [驼峰转换](#驼峰转换)
- [字符串脱敏](#字符串脱敏)

### 📊 数组对象函数
- [数组去重](#数组去重)
- [对象深拷贝](#对象深拷贝)
- [数组分组](#数组分组)
- [数据排序](#数据排序)

### 🔐 加密安全函数
- [MD5加密](#md5加密)
- [SHA256加密](#sha256加密)
- [Base64编码](#base64编码)
- [盐值生成](#盐值生成)

### 🌐 网络请求函数
- [请求重试](#请求重试)
- [请求防抖](#请求防抖)
- [请求节流](#请求节流)

### 🛠️ 其他实用函数
- [UUID生成](#uuid生成)
- [JSON深拷贝](#json深拷贝)
- [空值检查](#空值检查)
- [对象路径操作](#对象路径操作)

---

## 🔢 数字处理函数

### 数字格式化

**功能描述：** 将数字格式化为千分位分隔符的字符串，支持小数位数控制。

**使用方法：**
```js
import { formatNumber } from '@/utils'

// 基础用法
formatNumber(1234567.89)        // "1,234,567.89"
formatNumber(1234567.89, 0)     // "1,234,568"
formatNumber(1234567.89, 3)     // "1,234,567.890"

// 业务场景
const price = formatNumber(2999.99, 2)  // "2,999.99"
const count = formatNumber(1000000, 0)  // "1,000,000"
```

### 金额格式化

**功能描述：** 将数字格式化为货币格式，支持不同货币符号和小数位数。

**使用方法：**
```js
import { formatCurrency } from '@/utils'

// 人民币格式
formatCurrency(1234.56, 'CNY')     // "¥1,234.56"
formatCurrency(1234.56, 'USD')     // "$1,234.56"
formatCurrency(1234.56, 'EUR')     // "€1,234.56"

// 自定义格式
formatCurrency(1234.56, 'CNY', 0)  // "¥1,235"
```

### 数字范围限制

**功能描述：** 将数字限制在指定范围内，确保数值在有效区间内。

**使用方法：**
```js
import { clampNumber } from '@/utils'

// 基础用法
clampNumber(150, 0, 100)    // 100
clampNumber(-10, 0, 100)    // 0
clampNumber(50, 0, 100)     // 50

// 业务场景
const score = clampNumber(userScore, 0, 100)  // 确保分数在0-100之间
const percentage = clampNumber(progress, 0, 100)  // 确保进度在0-100%之间
```

---

## 📅 日期时间函数

### 日期格式化

**功能描述：** 将日期对象格式化为指定格式的字符串，支持多种格式模板。

**使用方法：**
```js
import { formatDate } from '@/utils'

// 基础用法
formatDate(new Date(), 'YYYY-MM-DD')           // "2024-01-15"
formatDate(new Date(), 'YYYY年MM月DD日')        // "2024年01月15日"
formatDate(new Date(), 'MM/DD/YYYY HH:mm:ss')  // "01/15/2024 14:30:25"

// 业务场景
const orderTime = formatDate(order.createTime, 'YYYY-MM-DD HH:mm')
const birthday = formatDate(user.birthday, 'MM月DD日')
```

### 相对时间格式化

**功能描述：** 将日期转换为相对时间描述，如"刚刚"、"5分钟前"等。

**使用方法：**
```js
import { formatRelativeTime } from '@/utils'

// 基础用法
formatRelativeTime(new Date())                    // "刚刚"
formatRelativeTime(Date.now() - 5 * 60 * 1000)   // "5分钟前"
formatRelativeTime(Date.now() - 2 * 60 * 60 * 1000) // "2小时前"

// 业务场景
const commentTime = formatRelativeTime(comment.createTime)
const lastLogin = formatRelativeTime(user.lastLoginTime)
```

### 日期计算

**功能描述：** 提供常用的日期计算功能，如加减天数、月份、年份等。

**使用方法：**
```js
import { addDays, addMonths } from '@/utils'

// 日期加减
const tomorrow = addDays(new Date(), 1)
const nextMonth = addMonths(new Date(), 1)
const lastYear = addMonths(new Date(), -12)

// 业务场景
const expireDate = addDays(new Date(), 30)  // 30天后过期
const renewalDate = addMonths(new Date(), 12)  // 续费日期
```

### 周数计算

**功能描述：** 计算指定日期在年份中的第几周。

**使用方法：**
```js
import { getWeekOfYear } from '@/utils'

// 基础用法
const weekNum = getWeekOfYear(new Date())  // 当前是第几周
const weekNum2 = getWeekOfYear('2024-01-15')  // 指定日期的周数

// 业务场景
const currentWeek = getWeekOfYear(new Date())
const reportWeek = getWeekOfYear(reportDate)
```

---

## 📝 字符串处理函数

### 字符串截取

**功能描述：** 智能截取字符串，支持省略号显示和HTML标签过滤。

**使用方法：**
```js
import { truncateString, stripHtml } from '@/utils'

// 基础截取
truncateString('这是一段很长的文本内容', 10)  // "这是一段很长的文本..."

// HTML标签过滤
const cleanText = stripHtml('<p>这是<b>HTML</b>内容</p>')  // "这是HTML内容"

// 业务场景
const summary = truncateString(article.content, 100)
const cleanDescription = stripHtml(product.description)
```

### 驼峰转换

**功能描述：** 提供驼峰命名和下划线命名之间的相互转换。

**使用方法：**
```js
import { camelToSnake, snakeToCamel } from '@/utils'

// 驼峰转下划线
camelToSnake('userName')        // "user_name"
camelToSnake('firstName')       // "first_name"

// 下划线转驼峰
snakeToCamel('user_name')       // "userName"
snakeToCamel('first_name')      // "firstName"

// 业务场景
const apiKey = camelToSnake('userProfile')  // "user_profile"
const jsKey = snakeToCamel('user_profile')  // "userProfile"
```

### 字符串脱敏

**功能描述：** 对敏感信息进行脱敏处理，如手机号、身份证号、银行卡号等。

**使用方法：**
```js
import { maskPhone, maskIdCard, maskBankCard } from '@/utils'

// 手机号脱敏
maskPhone('***********')        // "138****5678"

// 身份证脱敏
maskIdCard('110101199001011234') // "110101********1234"

// 银行卡脱敏
maskBankCard('6222021234567890123') // "6222 **** **** 0123"

// 业务场景
const maskedPhone = maskPhone(user.phone)
const maskedIdCard = maskIdCard(user.idCard)
```

---

## 📊 数组对象函数

### 数组去重

**功能描述：** 提供多种数组去重方法，支持对象数组去重和自定义去重规则。

**使用方法：**
```js
import { uniqueArray, uniqueByKey } from '@/utils'

// 基础去重
const unique = uniqueArray([1, 2, 2, 3, 3, 4])  // [1, 2, 3, 4]

// 对象数组去重
const users = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 1, name: '张三' }
]
const uniqueUsers = uniqueByKey(users, 'id')

// 业务场景
const uniqueOrders = uniqueByKey(orderList, 'orderNo')
const uniqueProducts = uniqueByKey(productList, 'sku')
```

### 数组分组

**功能描述：** 根据指定条件对数组进行分组处理。

**使用方法：**
```js
import { groupBy, sortBy } from '@/utils'

// 数组分组
const users = [
  { name: '张三', age: 25, city: '北京' },
  { name: '李四', age: 30, city: '上海' },
  { name: '王五', age: 25, city: '北京' }
]

const groupedByAge = groupBy(users, user => user.age)
const groupedByCity = groupBy(users, user => user.city)

// 数组排序
const sortedUsers = sortBy(users, 'age', 'desc')

// 业务场景
const ordersByStatus = groupBy(orderList, order => order.status)
const productsByCategory = groupBy(productList, product => product.category)
```

### 对象深拷贝

**功能描述：** 深度拷贝对象，避免引用类型数据共享问题。

**使用方法：**
```js
import { deepClone, deepMerge } from '@/utils'

// 基础深拷贝
const original = { a: 1, b: { c: 2 } }
const cloned = deepClone(original)

// 修改克隆对象不影响原对象
cloned.b.c = 3
console.log(original.b.c)  // 2
console.log(cloned.b.c)    // 3

// 对象合并
const merged = deepMerge({ a: 1 }, { b: 2 }, { c: 3 })

// 业务场景
const formData = deepClone(originalFormData)
const config = deepClone(defaultConfig)
```

---

## 🔐 加密安全函数

### MD5加密

**功能描述：** 提供MD5哈希加密功能，用于数据完整性验证和密码加密。

**使用方法：**
```js
import { md5, sha256, generateSalt } from '@/utils'

// 基础加密
const hash = md5('hello world')  // "5eb63bbbe01eeed093cb22bb8f5acdc3"

// SHA256加密
const shaHash = sha256('hello world')

// 生成盐值
const salt = generateSalt(16)

// 业务场景
const passwordHash = md5(userPassword + salt)
const token = md5(userId + timestamp + secretKey)
```

### Base64编码

**功能描述：** 提供Base64编码和解码功能，用于数据传输和存储。

**使用方法：**
```js
import { base64Encode, base64Decode } from '@/utils'

// 编码
const encoded = base64Encode('Hello World')  // "SGVsbG8gV29ybGQ="

// 解码
const decoded = base64Decode('SGVsbG8gV29ybGQ=')  // "Hello World"

// 业务场景
const imageData = base64Encode(imageFile)
const configData = base64Encode(JSON.stringify(config))
```

---

## 🌐 网络请求函数

### 请求重试

**功能描述：** 提供请求失败后的自动重试机制，支持指数退避策略。

**使用方法：**
```js
import { retryRequest } from '@/utils'

// 基础重试
const result = await retryRequest(
  () => api.getUserInfo(userId),
  { maxRetries: 3, delay: 1000 }
)

// 自定义重试条件
const result = await retryRequest(
  () => api.uploadFile(file),
  {
    maxRetries: 5,
    delay: 2000,
    shouldRetry: (error) => error.status === 500
  }
)

// 业务场景
const userData = await retryRequest(
  () => userService.getProfile(),
  { maxRetries: 3 }
)
```

### 请求防抖

**功能描述：** 防止短时间内重复发送相同请求，提升用户体验和系统性能。

**使用方法：**
```js
import { debounce, throttle } from '@/utils'

// 搜索防抖
const debouncedSearch = debounce((keyword) => {
  api.searchUsers(keyword)
}, 300)

// 输入框搜索
input.addEventListener('input', (e) => {
  debouncedSearch(e.target.value)
})

// 节流函数
const throttledScroll = throttle(() => {
  updateScrollPosition()
}, 100)

// 业务场景
const debouncedSave = debounce(() => {
  formService.saveForm(formData)
}, 1000)
```

---

## 🛠️ 其他实用函数

### UUID生成

**功能描述：** 生成唯一的UUID标识符。

**使用方法：**
```js
import { generateUUID } from '@/utils'

// 生成UUID
const id = generateUUID()  // "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"

// 业务场景
const sessionId = generateUUID()
const requestId = generateUUID()
```

### 空值检查

**功能描述：** 检查各种类型的值是否为空。

**使用方法：**
```js
import { isEmpty } from '@/utils'

// 基础检查
isEmpty(null)           // true
isEmpty(undefined)      // true
isEmpty('')             // true
isEmpty([])             // true
isEmpty({})             // true
isEmpty('hello')        // false
isEmpty([1, 2, 3])     // false

// 业务场景
if (isEmpty(formData)) {
  showError('请填写表单数据')
}
```

### 对象路径操作

**功能描述：** 安全地获取和设置对象的嵌套属性值。

**使用方法：**
```js
import { get, set } from '@/utils'

// 获取嵌套属性值
const user = {
  profile: {
    name: '张三',
    address: {
      city: '北京'
    }
  }
}

const userName = get(user, 'profile.name')           // "张三"
const userCity = get(user, 'profile.address.city')   // "北京"
const defaultValue = get(user, 'profile.age', 18)    // 18

// 设置嵌套属性值
set(user, 'profile.age', 25)
set(user, 'profile.address.street', '中关村大街')

// 业务场景
const configValue = get(config, 'api.baseUrl', 'http://localhost:3000')
set(user, 'lastLoginTime', new Date())
```

---

## 📚 使用建议

### 1. 按需引入
```js
// 推荐：按需引入需要的函数
import { formatDate, formatNumber } from '@/utils'

// 推荐：引入整个工具库
import utils from '@/utils'

// 不推荐：引入整个工具库的所有函数
import * as utils from '@/utils'
```

### 2. 性能优化
```js
// 对于频繁调用的函数，考虑缓存结果
const memoizedFormat = memoize(formatDate)
const formattedDate = memoizedFormat(date, 'YYYY-MM-DD')
```

### 3. 错误处理
```js
// 使用try-catch包装工具函数调用
try {
  const result = deepClone(complexObject)
} catch (error) {
  console.error('深拷贝失败:', error)
  // 降级处理
}
```

### 4. 类型检查
```js
// 在TypeScript项目中，为工具函数添加类型定义
interface FormatOptions {
  decimals?: number
  separator?: string
}

function formatNumber(num: number, options?: FormatOptions): string
```

---

## 📋 函数汇总表

| 分类 | 函数名 | 功能描述 |
|------|--------|----------|
| **数字处理** | `formatNumber` | 数字千分位格式化 |
| | `clampNumber` | 数字范围限制 |
| | `formatCurrency` | 货币格式化 |
| **日期时间** | `formatDate` | 日期格式化 |
| | `formatRelativeTime` | 相对时间格式化 |
| | `addDays` | 日期加减天数 |
| | `addMonths` | 日期加减月份 |
| | `getWeekOfYear` | 获取年份第几周 |
| **字符串处理** | `truncateString` | 字符串截取 |
| | `stripHtml` | 移除HTML标签 |
| | `camelToSnake` | 驼峰转下划线 |
| | `snakeToCamel` | 下划线转驼峰 |
| | `maskPhone` | 手机号脱敏 |
| | `maskIdCard` | 身份证脱敏 |
| | `maskBankCard` | 银行卡脱敏 |
| **数组对象** | `uniqueArray` | 数组去重 |
| | `uniqueByKey` | 按键去重 |
| | `groupBy` | 数组分组 |
| | `sortBy` | 数组排序 |
| | `deepClone` | 深度拷贝 |
| | `deepMerge` | 对象合并 |
| **加密安全** | `md5` | MD5加密 |
| | `sha256` | SHA256加密 |
| | `generateSalt` | 生成盐值 |
| | `base64Encode` | Base64编码 |
| | `base64Decode` | Base64解码 |
| **网络请求** | `retryRequest` | 请求重试 |
| | `debounce` | 防抖函数 |
| | `throttle` | 节流函数 |
| **其他实用** | `generateUUID` | 生成UUID |
| | `jsonClone` | JSON深拷贝 |
| | `isEmpty` | 检查空值 |
| | `get` | 获取对象路径值 |
| | `set` | 设置对象路径值 |

---

## 🗂️ 完整源代码

<details>
<summary>点击查看完整源代码</summary>

```js
/**
 * 统一工具函数库
 * 提供数字处理、日期时间、字符串处理、数组对象、加密安全、网络请求等常用功能
 */

// ==================== 数字处理函数 ====================

/**
 * 数字格式化函数
 * @param {number} num - 要格式化的数字
 * @param {number} decimals - 小数位数，默认2位
 * @param {string} separator - 千分位分隔符，默认逗号
 * @returns {string} 格式化后的字符串
 */
export function formatNumber(num, decimals = 2, separator = ',') {
  if (typeof num !== 'number' || isNaN(num)) {
    return '0'
  }
  
  const parts = num.toString().split('.')
  const integerPart = parts[0]
  const decimalPart = parts[1] || ''
  
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator)
  
  if (decimals === 0) {
    return formattedInteger
  }
  
  const formattedDecimal = decimalPart.padEnd(decimals, '0').slice(0, decimals)
  return `${formattedInteger}.${formattedDecimal}`
}

/**
 * 数字范围限制
 * @param {number} num - 输入数字
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} 限制后的数字
 */
export function clampNumber(num, min, max) {
  return Math.min(Math.max(num, min), max)
}

/**
 * 货币格式化函数
 * @param {number} amount - 金额
 * @param {string} currency - 货币代码
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的货币字符串
 */
export function formatCurrency(amount, currency = 'CNY', decimals = 2) {
  const currencyMap = {
    CNY: { symbol: '¥', position: 'before' },
    USD: { symbol: '$', position: 'before' },
    EUR: { symbol: '€', position: 'before' },
    GBP: { symbol: '£', position: 'before' }
  }
  
  const config = currencyMap[currency] || currencyMap.CNY
  const formattedNumber = formatNumber(amount, decimals)
  
  if (config.position === 'before') {
    return `${config.symbol}${formattedNumber}`
  } else {
    return `${formattedNumber}${config.symbol}`
  }
}

// ==================== 日期时间函数 ====================

/**
 * 日期格式化函数
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式化模板
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hour = d.getHours()
  const minute = d.getMinutes()
  const second = d.getSeconds()
  
  const pad = (num) => String(num).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', pad(month))
    .replace('DD', pad(day))
    .replace('HH', pad(hour))
    .replace('mm', pad(minute))
    .replace('ss', pad(second))
}

/**
 * 相对时间格式化
 * @param {Date|string|number} date - 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  const now = new Date()
  const target = new Date(date)
  const diff = now - target
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) return '刚刚'
  if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
  if (diff < day) return `${Math.floor(diff / hour)}小时前`
  if (diff < week) return `${Math.floor(diff / day)}天前`
  if (diff < month) return `${Math.floor(diff / week)}周前`
  if (diff < year) return `${Math.floor(diff / month)}个月前`
  
  return `${Math.floor(diff / year)}年前`
}

/**
 * 日期加减天数
 * @param {Date} date - 基准日期
 * @param {number} days - 天数（正数加，负数减）
 * @returns {Date} 计算后的日期
 */
export function addDays(date, days) {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}

/**
 * 日期加减月份
 * @param {Date} date - 基准日期
 * @param {number} months - 月数（正数加，负数减）
 * @returns {Date} 计算后的日期
 */
export function addMonths(date, months) {
  const result = new Date(date)
  result.setMonth(result.getMonth() + months)
  return result
}

/**
 * 获取年份中的第几周
 * @param {Date} date - 日期
 * @returns {number} 周数
 */
export function getWeekOfYear(date) {
  const d = new Date(date)
  d.setHours(0, 0, 0, 0)
  
  const yearStart = new Date(d.getFullYear(), 0, 1)
  const days = Math.floor((d - yearStart) / (24 * 60 * 60 * 1000))
  const weekNum = Math.ceil((days + yearStart.getDay() + 1) / 7)
  
  return weekNum
}

// ==================== 字符串处理函数 ====================

/**
 * 字符串截取函数
 * @param {string} str - 原字符串
 * @param {number} length - 截取长度
 * @param {string} suffix - 后缀，默认"..."
 * @returns {string} 截取后的字符串
 */
export function truncateString(str, length, suffix = '...') {
  if (!str || typeof str !== 'string') return ''
  if (str.length <= length) return str
  
  return str.substring(0, length) + suffix
}

/**
 * 移除HTML标签
 * @param {string} html - HTML字符串
 * @returns {string} 纯文本内容
 */
export function stripHtml(html) {
  if (!html) return ''
  return html.replace(/<[^>]*>/g, '')
}

/**
 * 驼峰转下划线
 * @param {string} str - 驼峰字符串
 * @returns {string} 下划线字符串
 */
export function camelToSnake(str) {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * 下划线转驼峰
 * @param {string} str - 下划线字符串
 * @returns {string} 驼峰字符串
 */
export function snakeToCamel(str) {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 手机号脱敏
 * @param {string} phone - 手机号
 * @returns {string} 脱敏后的手机号
 */
export function maskPhone(phone) {
  if (!phone || typeof phone !== 'string') return ''
  if (phone.length !== 11) return phone
  
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 身份证号脱敏
 * @param {string} idCard - 身份证号
 * @returns {string} 脱敏后的身份证号
 */
export function maskIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') return ''
  if (idCard.length !== 18) return idCard
  
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

/**
 * 银行卡号脱敏
 * @param {string} cardNumber - 银行卡号
 * @returns {string} 脱敏后的银行卡号
 */
export function maskBankCard(cardNumber) {
  if (!cardNumber || typeof cardNumber !== 'string') return ''
  
  const len = cardNumber.length
  if (len < 8) return cardNumber
  
  const prefix = cardNumber.substring(0, 4)
  const suffix = cardNumber.substring(len - 4)
  const middle = '*'.repeat(len - 8)
  
  return `${prefix} ${middle} ${suffix}`.trim()
}

// ==================== 数组对象函数 ====================

/**
 * 数组去重
 * @param {Array} arr - 原数组
 * @returns {Array} 去重后的数组
 */
export function uniqueArray(arr) {
  if (!Array.isArray(arr)) return []
  return [...new Set(arr)]
}

/**
 * 根据指定键去重
 * @param {Array} arr - 对象数组
 * @param {string} key - 去重依据的键
 * @returns {Array} 去重后的数组
 */
export function uniqueByKey(arr, key) {
  if (!Array.isArray(arr) || !key) return []
  
  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 数组分组
 * @param {Array} arr - 数组
 * @param {Function} keyFn - 分组函数
 * @returns {Object} 分组结果
 */
export function groupBy(arr, keyFn) {
  if (!Array.isArray(arr) || typeof keyFn !== 'function') return {}
  
  return arr.reduce((groups, item) => {
    const key = keyFn(item)
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
    return groups
  }, {})
}

/**
 * 数组排序
 * @param {Array} arr - 数组
 * @param {string} key - 排序键
 * @param {string} order - 排序方向 'asc' | 'desc'
 * @returns {Array} 排序后的数组
 */
export function sortBy(arr, key, order = 'asc') {
  if (!Array.isArray(arr)) return []
  
  return [...arr].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]
    
    if (order === 'desc') {
      return bVal > aVal ? 1 : -1
    }
    return aVal > bVal ? 1 : -1
  })
}

/**
 * 深度拷贝函数
 * @param {*} obj - 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 对象合并
 * @param {Object} target - 目标对象
 * @param {...Object} sources - 源对象
 * @returns {Object} 合并后的对象
 */
export function deepMerge(target, ...sources) {
  if (!sources.length) return target
  
  const source = sources.shift()
  
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }
  
  return deepMerge(target, ...sources)
}

/**
 * 判断是否为对象
 * @param {*} item - 要判断的值
 * @returns {boolean} 是否为对象
 */
function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}

// ==================== 加密安全函数 ====================

/**
 * MD5加密函数
 * @param {string} message - 要加密的消息
 * @returns {string} MD5哈希值
 */
export function md5(message) {
  try {
    const CryptoJS = require('crypto-js')
    return CryptoJS.MD5(message).toString()
  } catch (error) {
    console.warn('crypto-js未安装，请运行: npm install crypto-js')
    return ''
  }
}

/**
 * SHA256加密
 * @param {string} message - 要加密的消息
 * @returns {string} SHA256哈希值
 */
export function sha256(message) {
  try {
    const CryptoJS = require('crypto-js')
    return CryptoJS.SHA256(message).toString()
  } catch (error) {
    console.warn('crypto-js未安装，请运行: npm install crypto-js')
    return ''
  }
}

/**
 * 生成随机盐值
 * @param {number} length - 盐值长度
 * @returns {string} 随机盐值
 */
export function generateSalt(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Base64编码
 * @param {string} str - 要编码的字符串
 * @returns {string} Base64编码后的字符串
 */
export function base64Encode(str) {
  if (typeof window !== 'undefined' && window.btoa) {
    return window.btoa(unescape(encodeURIComponent(str)))
  }
  
  if (typeof Buffer !== 'undefined') {
    return Buffer.from(str, 'utf8').toString('base64')
  }
  
  throw new Error('Base64 encoding not supported in this environment')
}

/**
 * Base64解码
 * @param {string} str - Base64编码的字符串
 * @returns {string} 解码后的字符串
 */
export function base64Decode(str) {
  if (typeof window !== 'undefined' && window.atob) {
    return decodeURIComponent(escape(window.atob(str)))
  }
  
  if (typeof window !== 'undefined' && window.atob) {
    return decodeURIComponent(escape(window.atob(str)))
  }
  
  if (typeof Buffer !== 'undefined') {
    return Buffer.from(str, 'base64').toString('utf8')
  }
  
  throw new Error('Base64 decoding not supported in this environment')
}

// ==================== 网络请求函数 ====================

/**
 * 请求重试函数
 * @param {Function} fn - 要重试的函数
 * @param {Object} options - 重试选项
 * @returns {Promise} 重试结果
 */
export async function retryRequest(fn, options = {}) {
  const {
    maxRetries = 3,
    delay = 1000,
    shouldRetry = () => true,
    onRetry = () => {}
  } = options
  
  let lastError
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries || !shouldRetry(error)) {
        throw error
      }
      
      onRetry(attempt, error)
      
      if (delay > 0) {
        await sleep(delay * Math.pow(2, attempt - 1)) // 指数退避
      }
    }
  }
  
  throw lastError
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    
    const callNow = immediate && !timeout
    
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} Promise对象
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// ==================== 其他实用函数 ====================

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 深拷贝对象（JSON方式，性能更好但有限制）
 * @param {*} obj - 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export function jsonClone(obj) {
  try {
    return JSON.parse(JSON.stringify(obj))
  } catch (error) {
    console.warn('JSON深拷贝失败，使用递归方式:', error)
    return deepClone(obj)
  }
}

/**
 * 检查对象是否为空
 * @param {*} obj - 要检查的对象
 * @returns {boolean} 是否为空
 */
export function isEmpty(obj) {
  if (obj == null) return true
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0
  if (obj instanceof Map || obj instanceof Set) return obj.size === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  return false
}

/**
 * 获取对象指定路径的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径，如 'user.profile.name'
 * @param {*} defaultValue - 默认值
 * @returns {*} 路径对应的值
 */
export function get(obj, path, defaultValue) {
  const keys = path.split('.')
  let result = obj
  
  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue
    }
    result = result[key]
  }
  
  return result !== undefined ? result : defaultValue
}

/**
 * 设置对象指定路径的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径
 * @param {*} value - 要设置的值
 * @returns {Object} 修改后的对象
 */
export function set(obj, path, value) {
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
  return obj
}

// ==================== 默认导出 ====================

export default {
  // 数字处理
  formatNumber,
  clampNumber,
  formatCurrency,
  
  // 日期时间
  formatDate,
  formatRelativeTime,
  addDays,
  addMonths,
  getWeekOfYear,
  
  // 字符串处理
  truncateString,
  stripHtml,
  camelToSnake,
  snakeToCamel,
  maskPhone,
  maskIdCard,
  maskBankCard,
  
  // 数组对象
  uniqueArray,
  uniqueByKey,
  groupBy,
  sortBy,
  deepClone,
  deepMerge,
  
  // 加密安全
  md5,
  sha256,
  generateSalt,
  base64Encode,
  base64Decode,
  
  // 网络请求
  retryRequest,
  debounce,
  throttle,
  
  // 其他实用函数
  generateUUID,
  jsonClone,
  isEmpty,
  get,
  set
}
```

</details>

---

这些工具函数涵盖了业务开发中的常见需求，提供了完整的实现和使用示例。建议根据项目实际需求选择合适的函数，并注意性能优化和错误处理。

现在您可以通过以下方式使用：

```js
// 方式1：按需引入特定函数
import { formatDate, formatNumber } from '@/utils'

// 方式2：引入整个工具库
import utils from '@/utils'

// 使用示例
const price = formatNumber(2999.99, 2)  // "2,999.99"
const today = formatDate(new Date(), 'YYYY年MM月DD日')  // "2024年01月15日"
const cloned = deepClone(originalObject)

// 或者使用命名空间
const price = utils.formatNumber(2999.99, 2)
const today = utils.formatDate(new Date(), 'YYYY年MM月DD日')
```
