# 🗺️ 高德地图可视化组件

基于高德地图API的地图可视化组件，提供地图显示、点位标注、地图交互等功能.

## GmapCustom - 高德地图组件

**功能描述：** 基于高德地图JavaScript API的自定义地图组件，支持多点位显示、单点回显、地图点击打点等功能。

**主要特性：**
- 🗺️ 高德地图底图显示
- 📍 多点位标注和管理
- 🎯 单点回显和定位
- 🔍 地图点击打点功能
- 🎨 自定义标记样式
- 📱 响应式设计

### 基础用法

```vue
<template>
  <div class="custom-map-container">
    <GmapCustom
      :center="mapCenter"
      :zoom="mapZoom"
      :markers="markers"
      :single-marker="singleMarker"
      :disabled="false"
      @map-ready="onMapReady"
      @marker-click="onMarkerClick"
      @map-click="onMapClick"
    />
  </div>
</template>

<script>
import GmapCustom from '@/components/GmapCustom'

export default {
  components: { GmapCustom },
  data() {
    return {
      mapCenter: [117.987196, 37.366018], // 地图中心点 [经度, 纬度]
      mapZoom: 10, // 地图缩放级别
      markers: [
        {
          position: [117.987196, 37.366018],
          name: '水库A',
          content: '水库A监测点'
        }
      ],
      singleMarker: {
        longitude: 117.987196,
        latitude: 37.366018
      }
    }
  },
  methods: {
    onMapReady(map) {
      console.log('地图加载完成:', map)
    },
    onMarkerClick(marker, position) {
      console.log('点击标记点:', marker, position)
    },
    onMapClick(coordinates) {
      console.log('点击地图位置:', coordinates)
      // coordinates: { longitude: 117.987196, latitude: 37.366018 }
    }
  }
}
</script>

<style scoped>
.custom-map-container {
  width: 100%;
  height: 500px;
}
</style>
```

### 配置参数

```js
const mapConfig = {
  // 地图中心点
  center: [117.987196, 37.366018], // [经度, 纬度]
  
  // 地图缩放级别
  zoom: 10, // 3-20级
  
  // 多点位数据
  markers: [
    {
      position: [117.987196, 37.366018], // [经度, 纬度]
      name: '点位名称',
      content: '点位描述'
    }
  ],
  
  // 单点回显数据
  singleMarker: {
    longitude: 117.987196,
    latitude: 37.366018
  },
  
  // 是否只读（禁用点击打点）
  disabled: false
}
```

## 点位标注功能

### 多点位标注

```vue
<template>
  <div class="map-with-markers">
    <GmapCustom
      ref="gmapCustom"
      :center="mapCenter"
      :zoom="mapZoom"
      :markers="markers"
      @map-ready="onMapReady"
      @marker-click="onMarkerClick"
    />
    
    <!-- 点位管理工具栏 -->
    <div class="marker-toolbar">
      <el-button @click="addMarker">添加点位</el-button>
      <el-button @click="clearMarkers">清空点位</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mapCenter: [117.987196, 37.366018],
      mapZoom: 10,
      markers: [],
      markerCounter: 1
    }
  },
  methods: {
    onMapReady(map) {
      this.map = map
      // 加载已保存的点位数据
      this.addMarker()
    },
    onMarkerClick(marker, position) {
      console.log('点击标记点:', marker, position)
    },
    addMarker() {
      // 添加新点位
      const newMarker = {
        position: this.mapCenter,
        name: `点位${this.markerCounter}`,
        content: `这是第${this.markerCounter}个点位`
      }
      
      this.markers.push(newMarker)
      this.markerCounter++
    },
    clearMarkers() {
      this.$confirm('确认清空所有点位？', '提示').then(() => {
        this.markers = []
      })
    },

  }
}
</script>
```

### 单点回显功能

```vue
<template>
  <div class="single-marker-map">
    <GmapCustom
      :center="mapCenter"
      :zoom="mapZoom"
      :single-marker="singleMarker"
      :disabled="false"
      @map-ready="onMapReady"
      @map-click="onMapClick"
    />
    
    <!-- 单点回显控制面板 -->
    <div class="single-marker-panel">
      <el-form :model="singleMarkerForm" label-width="80px">
        <el-form-item label="经度">
          <el-input v-model="singleMarkerForm.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="singleMarkerForm.latitude" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item>
          <el-button @click="updateSingleMarker">更新点位</el-button>
          <el-button @click="locateToMarker">定位到点位</el-button>
        </el-form-item>
      </el-form>
        </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mapCenter: [117.987196, 37.366018],
      mapZoom: 10,
      singleMarker: {
        longitude: 117.987196,
        latitude: 37.366018
      },
      singleMarkerForm: {
        longitude: 117.987196,
        latitude: 37.366018
      }
    }
  },
  methods: {
    onMapReady(map) {
      this.map = map
    },
    
    onMapClick(coordinates) {
      // 地图点击时更新单点回显
      this.singleMarkerForm.longitude = coordinates.longitude
      this.singleMarkerForm.latitude = coordinates.latitude
      this.updateSingleMarker()
    },
    
    updateSingleMarker() {
      this.singleMarker = {
        longitude: parseFloat(this.singleMarkerForm.longitude),
        latitude: parseFloat(this.singleMarkerForm.latitude)
      }
    },
    
    locateToMarker() {
      // 定位到指定点位
      if (this.map) {
        this.map.panTo([this.singleMarker.longitude, this.singleMarker.latitude])
        setTimeout(() => {
          this.map.setZoom(15)
        }, 300)
      }
    }
  }
}
</script>

<style scoped>
.single-marker-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
```

### 地图点击打点功能

```vue
<template>
  <div class="click-to-add-map">
    <GmapCustom
      ref="gmapCustom"
      :center="mapCenter"
      :zoom="mapZoom"
      :markers="clickMarkers"
      :disabled="false"
      @map-ready="onMapReady"
      @map-click="onMapClick"
      @marker-click="onMarkerClick"
    />
    
    <!-- 打点控制面板 -->
    <div class="click-control-panel">
      <el-button @click="toggleClickMode" :type="clickMode ? 'primary' : 'default'">
        {{ clickMode ? '退出打点' : '开始打点' }}
      </el-button>
      <el-button @click="clearClickMarkers" type="danger">清空打点</el-button>
      <el-button @click="exportClickMarkers" type="success">导出点位</el-button>
    </div>
    
    <!-- 打点记录 -->
    <div class="click-records" v-if="clickMarkers.length > 0">
      <h4>打点记录 ({{ clickMarkers.length }})</h4>
      <div class="record-list">
        <div 
          v-for="(marker, index) in clickMarkers" 
          :key="index"
          class="record-item"
          @click="locateToClickMarker(marker)"
        >
          <span class="record-index">{{ index + 1 }}</span>
          <span class="record-coords">
            {{ marker.position[0].toFixed(6) }}, {{ marker.position[1].toFixed(6) }}
          </span>
          <span class="record-name">{{ marker.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mapCenter: [117.987196, 37.366018],
      mapZoom: 10,
      clickMarkers: [],
      clickMode: false,
      clickCounter: 1
    }
  },
  methods: {
    onMapReady(map) {
      this.map = map
    },
    
    onMapClick(coordinates) {
      if (!this.clickMode) return
      
      // 创建新的打点标记
      const newMarker = {
        position: [coordinates.longitude, coordinates.latitude],
        name: `打点${this.clickCounter}`,
        content: `经度: ${coordinates.longitude.toFixed(6)}, 纬度: ${coordinates.latitude.toFixed(6)}`
      }
      
      this.clickMarkers.push(newMarker)
      this.clickCounter++
      
      // 显示打点成功提示
      this.$message.success(`已添加打点: ${newMarker.name}`)
    },
    
    onMarkerClick(marker, position) {
      console.log('点击打点标记:', marker, position)
    },
    
    toggleClickMode() {
      this.clickMode = !this.clickMode
      if (this.clickMode) {
        this.$message.info('已进入打点模式，点击地图添加点位')
      } else {
        this.$message.info('已退出打点模式')
      }
    },
    
    clearClickMarkers() {
      this.$confirm('确认清空所有打点？', '提示').then(() => {
        this.clickMarkers = []
        this.clickCounter = 1
        this.$message.success('已清空所有打点')
      })
    },
    
    exportClickMarkers() {
      if (this.clickMarkers.length === 0) {
        this.$message.warning('没有打点数据可导出')
        return
      }
      
      const data = JSON.stringify(this.clickMarkers, null, 2)
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `click-markers-${new Date().toISOString().slice(0, 10)}.json`
      a.click()
      URL.revokeObjectURL(url)
      
      this.$message.success('打点数据导出成功')
    },
    
    locateToClickMarker(marker) {
      // 定位到指定打点
      if (this.map) {
        this.map.panTo(marker.position)
        setTimeout(() => {
          this.map.setZoom(16)
        }, 300)
      }
    }
  }
}
</script>

<style scoped>
.click-control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.click-records {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
}

.record-list {
  margin-top: 15px;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 5px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.record-item:hover {
  background: #f0f9ff;
}

.record-index {
  background: #1890ff;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 10px;
}

.record-coords {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  margin-right: 10px;
  min-width: 120px;
}

.record-name {
  font-weight: 500;
  color: #333;
}
</style>
```

## 地图交互功能

### 地图定位和跳转

```vue
<template>
  <div class="map-navigation">
    <GmapCustom
      ref="gmapCustom"
      :center="mapCenter"
      :zoom="mapZoom"
      :markers="navigationMarkers"
      @map-ready="onMapReady"
    />
    
    <!-- 导航控制面板 -->
    <div class="navigation-panel">
      <el-form :model="navigationForm" label-width="80px">
        <el-form-item label="目标位置">
          <el-input 
            v-model="navigationForm.address" 
            placeholder="输入地址或坐标"
            @keyup.enter.native="searchLocation"
          />
        </el-form-item>
        <el-form-item label="经度">
          <el-input v-model="navigationForm.longitude" placeholder="经度" />
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="navigationForm.latitude" placeholder="纬度" />
        </el-form-item>
        <el-form-item>
          <el-button @click="searchLocation" type="primary">搜索位置</el-button>
          <el-button @click="panToLocation">跳转位置</el-button>
          <el-button @click="resetMap">重置地图</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mapCenter: [117.987196, 37.366018],
      mapZoom: 10,
      navigationMarkers: [],
      navigationForm: {
        address: '',
        longitude: '',
        latitude: ''
      }
    }
  },
  methods: {
    onMapReady(map) {
      this.map = map
    },
    
    searchLocation() {
      if (this.navigationForm.address) {
        // 这里可以集成地理编码服务
        this.$message.info('搜索功能需要集成地理编码服务')
      } else if (this.navigationForm.longitude && this.navigationForm.latitude) {
        const lng = parseFloat(this.navigationForm.longitude)
        const lat = parseFloat(this.navigationForm.latitude)
        
        if (isNaN(lng) || isNaN(lat)) {
          this.$message.error('请输入有效的经纬度坐标')
          return
        }
        
        this.panToLocation()
      } else {
        this.$message.warning('请输入地址或坐标')
      }
    },
    
    panToLocation() {
      const lng = parseFloat(this.navigationForm.longitude)
      const lat = parseFloat(this.navigationForm.latitude)
      
      if (this.map) {
        // 使用组件的panTo方法
        this.$refs.gmapCustom.panTo(lng, lat, 15)
        
        // 添加标记点
        this.addNavigationMarker(lng, lat)
      }
    },
    
    addNavigationMarker(longitude, latitude) {
      // 清除之前的导航标记
      this.navigationMarkers = []
      
      // 添加新的导航标记
      this.navigationMarkers.push({
        position: [longitude, latitude],
        name: '目标位置',
        content: `经度: ${longitude.toFixed(6)}, 纬度: ${latitude.toFixed(6)}`
      })
    },
    
    resetMap() {
      // 重置地图到初始状态
      this.mapCenter = [117.987196, 37.366018]
      this.mapZoom = 10
      this.navigationMarkers = []
      this.navigationForm = {
        address: '',
        longitude: '',
        latitude: ''
      }
      
      if (this.map) {
        this.map.setCenter(this.mapCenter)
        this.map.setZoom(this.mapZoom)
      }
    }
  }
}
</script>

<style scoped>
.navigation-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 300px;
}
</style>
```

### 点位搜索和筛选

```vue
<template>
  <div class="searchable-map">
    <div class="search-panel">
      <el-input
        v-model="searchKeyword"
        placeholder="输入点位名称或描述"
        @keyup.enter.native="searchMarkers"
      >
        <el-button slot="append" @click="searchMarkers">搜索</el-button>
      </el-input>
      
      <el-button @click="locateToCenter">定位到中心</el-button>
      <el-button @click="fitAllMarkers">显示所有点位</el-button>
    </div>
    
    <GmapCustom
      ref="gmapCustom"
      :center="mapCenter"
      :zoom="mapZoom"
      :markers="searchableMarkers"
      @map-ready="onMapReady"
      @marker-click="onMarkerClick"
    />
    
    <div class="search-results" v-if="searchResults.length > 0">
      <h4>搜索结果 ({{ searchResults.length }})</h4>
      <div class="result-list">
        <div 
          v-for="result in searchResults" 
          :key="result.name"
          @click="locateToMarker(result)"
          class="search-result-item"
        >
          <span class="result-name">{{ result.name }}</span>
          <span class="result-content">{{ result.content }}</span>
          <span class="result-coords">
            {{ result.position[0].toFixed(6) }}, {{ result.position[1].toFixed(6) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mapCenter: [117.987196, 37.366018],
      mapZoom: 10,
      searchableMarkers: [],
      searchKeyword: '',
      searchResults: [],
      map: null
    }
  },
  mounted() {
    this.loadSearchableMarkers()
  },
  methods: {
    onMapReady(map) {
      this.map = map
    },
    
    onMarkerClick(marker, position) {
      console.log('点击搜索点位:', marker, position)
    },
    
    loadSearchableMarkers() {
      // 加载可搜索的点位数据
      this.searchableMarkers = [
        {
          position: [117.987196, 37.366018],
          name: '水库监测站',
          content: '主要水质监测点'
        },
        {
          position: [117.990, 37.370],
          name: '大坝控制室',
          content: '大坝运行控制中心'
        },
        {
          position: [117.985, 37.362],
          name: '进水口',
          content: '水库进水控制点'
        }
      ]
    },
    
    searchMarkers() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
        return
      }
      
      const keyword = this.searchKeyword.toLowerCase()
      this.searchResults = this.searchableMarkers.filter(marker => 
        marker.name.toLowerCase().includes(keyword) ||
        marker.content.toLowerCase().includes(keyword)
      )
      
      if (this.searchResults.length === 0) {
        this.$message.warning('未找到匹配的点位')
      }
    },
    
    locateToMarker(marker) {
      // 定位到指定点位
      if (this.map) {
        this.map.setCenter(marker.position)
        this.map.setZoom(16)
        
        // 高亮显示选中的点位
        this.highlightMarker(marker)
      }
    },
    
    locateToCenter() {
      // 定位到地图中心
      if (this.map) {
        this.map.setCenter(this.mapCenter)
        this.map.setZoom(this.mapZoom)
      }
    },
    
    fitAllMarkers() {
      // 显示所有点位
      if (this.searchableMarkers.length > 0 && this.map) {
        const bounds = new AMap.Bounds()
        this.searchableMarkers.forEach(marker => {
          bounds.extend(marker.position)
        })
        this.map.setBounds(bounds)
      }
    },
    
    highlightMarker(marker) {
      // 高亮显示指定点位
      console.log('高亮显示点位:', marker.name)
      // 这里可以添加高亮逻辑，比如改变标记样式
    }
  }
}
</script>

<style scoped>
.search-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.search-panel .el-input {
  width: 250px;
  margin-right: 10px;
}

.search-results {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-width: 350px;
  max-height: 400px;
  overflow-y: auto;
}

.result-list {
  margin-top: 15px;
}

.search-result-item {
  display: flex;
  flex-direction: column;
  padding: 10px;
  margin: 8px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #f0f0f0;
}

.search-result-item:hover {
  background: #f0f9ff;
  border-color: #1890ff;
  transform: translateY(-1px);
}

.result-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.result-content {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.result-coords {
  font-family: monospace;
  font-size: 11px;
  color: #999;
}
</style>
```

## API文档

### GmapCustom

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| center | 地图中心点 | Array | [经度, 纬度] | [117.987196, 37.366018] |
| zoom | 地图缩放级别 | Number | 3-20 | 10 |
| markers | 多点位数据数组 | Array | - | [] |
| single-marker | 单点回显数据 | Object | - | null |
| disabled | 是否只读（禁用点击打点） | Boolean | true/false | true |


### 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| map-ready | 地图初始化完成 | (map: AMap.Map) |
| marker-click | 多点位点击事件 | (marker: Object, position: Array) |
| map-click | 地图点击事件 | (coordinates: Object) |


### 方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| panTo | 跳转到指定位置 | (longitude: Number, latitude: Number, zoom?: Number) | - |
| clearMarkers | 清除所有标记 | - | - |
| destroyMap | 销毁地图 | - | - |

## 源码实现


<details>
<summary>点击查看完整源码</summary>

```vue
<template>
  <div class="custom-map-container">
    <div ref="mapContainer" class="map-container"></div>
  </div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader'

export default {
  name: 'GmapCustom',
  props: {
    // 地图中心点
    center: {
      type: Array,
      default: () => [117.987196, 37.366018]
    },
    // 地图缩放级别
    zoom: {
      type: Number,
      default: 10
    },
    // 多点位数据
    markers: {
      type: Array,
      default: () => []
    },
    // 单点回显数据
    singleMarker: {
      type: Object,
      default: null
    },
    // 只读
    disabled: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      map: null,
      markerList: [], // 存储所有marker实例
      clickMarker: null, // 存储点击打点的marker
      isMoving: false, // 添加移动状态标记
      icon:require('@/assets/images/video/positionMarker.png')// 自定义图标,可以替换成你自己的图标
    }
  },
  watch: {
    markers: {
      handler (newVal) {
        this.updateMarkers(newVal)
      },
      deep: true
    },
    singleMarker: {
      handler (newVal) {
        this.updateSingleMarker(newVal)
      },
      deep: true
    }
  },
  mounted () {
    this.initMap()
  },
  beforeDestroy () {
    this.destroyMap()
  },
  methods: {
    // 初始化地图
    async initMap () {
      window._AMapSecurityConfig = {
        securityJsCode: "cdb7dee06285d3c2b76be3650457eeef",
      }

      try {
        const AMap = await AMapLoader.load({
          key: "8d181b818e4fbab9b34258b158a11311",
          version: "2.0",
          plugins: ['AMap.ToolBar', 'AMap.Scale']
        })

        this.map = new AMap.Map(this.$refs.mapContainer, {
          zoom: this.zoom,
          center: this.center,
          zooms: [3, 20]
        })

        // 添加工具条和比例尺
        this.map.addControl(new AMap.ToolBar())
        this.map.addControl(new AMap.Scale())

        // 初始化标记点
        if (this.markers.length > 0) {
          this.updateMarkers(this.markers)
        }

        // 初始化单点
        if (this.singleMarker) {
          this.updateSingleMarker(this.singleMarker)
        }

        // 编辑模式下添加点击事件
        if (!this.disabled) {
          this.map.on('click', this.handleMapClick)
        }
        this.$emit('map-ready', this.map)
      } catch (error) {
        console.error('地图初始化失败：', error)
      }
    },

    // 更新多点标记
    updateMarkers (markers) {
      // 清除现有标记
      this.clearMarkers()
      markers.forEach(point => {
        const position = point.position
        // 创建浮动标签
        // 创建图标
        const icon = new AMap.Icon({
          url: this.icon,
          size: new AMap.Size(20, 20),
          anchor: 'bottom-center'
        })
        //打点操作
        const marker = new AMap.Marker({
          position: position,
          anchor: 'bottom-center',
          offset: new AMap.Pixel(0, 0),
          content: `
            <div class="hover-marker" data-name="${point.name || '-'}">
              <div class="marker-pin">
                <div class="marker-dot"></div>
              </div>
              <div class="marker-label">
                <span class="label-text">${point.name || '-'}</span>
              </div>
            </div>
          `
        })

        // 添加点击事件
        marker.on('click', () => {
          console.log('点击了标记：', point.name)
          this.$emit('markerClick', point, position)
        })

        marker.setMap(this.map)
        this.markerList.push(marker)
      })
    },

    // 更新单点标记
    updateSingleMarker (point) {
      if (this.clickMarker) {
        this.clickMarker.setMap(null)
        this.clickMarker = null
      }

      if (point && point.longitude && point.latitude) {
        this.clickMarker = new AMap.Marker({
          position: [point.longitude, point.latitude],
          anchor: 'bottom-center', // 设置锚点为底部中心
          offset: new AMap.Pixel(0, 0)
        })
        this.clickMarker.setMap(this.map)

        // 居中显示该点
        this.map.setCenter([point.longitude, point.latitude])
      }
    },

    // 处理地图点击
    handleMapClick (e) {
      const lnglat = e.lnglat
      const position = {
        longitude: lnglat.getLng(),
        latitude: lnglat.getLat()
      }

      // 清除之前的点击标记
      if (this.clickMarker) {
        this.clickMarker.setMap(null)
      }

      // 创建新的标记
      this.clickMarker = new AMap.Marker({
        position: [position.longitude, position.latitude],
        anchor: 'bottom-center', // 设置锚点为底部中心
        offset: new AMap.Pixel(0, 0) //
      })
      this.clickMarker.setMap(this.map)
      console.log(position, 'position')
      // 触发坐标信息
      this.$emit('mapClick', { longitude: position.longitude, latitude: position.latitude })
    },

    // 跳转到指定位置
    panTo (longitude, latitude,zoom = 13) {
      if (!this.map || !longitude || !latitude || this.isMoving) return
      this.map.panTo([longitude, latitude]);
      setTimeout(() => {
        this.map.setZoom(zoom);
      }, 300);
    },

    // 清除所有标记
    clearMarkers () {
      this.markerList.forEach(marker => {
        marker.setMap(null)
      })
      this.markerList = []
    },

    // 销毁地图
    destroyMap () {
      if (this.map) {
        this.map.destroy()
        this.map = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-map-container {
  width: 100%;
  height: 100%;
  position: relative;

  .map-container {
    width: 100%;
    height: 100%;
  }
}

// 悬停显示标记样式
::v-deep .hover-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transform: translateY(-100%);

  .marker-pin {
    position: relative;
    width: 20px;
    height: 20px;
    z-index: 2;

    // 脉冲效果背景
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 30px;
      height: 30px;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, rgba(102, 126, 234, 0.1) 70%, transparent 100%);
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    .marker-dot {
      position: relative;
      width: 20px;
      height: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      border: 3px solid #ffffff;
      box-shadow:
        0 3px 8px rgba(102, 126, 234, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background: #ffffff;
        border-radius: 50%;
      }
    }
  }

  .marker-label {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-8px);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 18px;
    padding: 6px 14px;
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.08),
      0 2px 8px rgba(0, 0, 0, 0.04);

    // 默认隐藏
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-8px) scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;

    // 小三角箭头
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid rgba(255, 255, 255, 0.95);
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.08));
    }

    .label-text {
      font-size: 12px;
      font-weight: 600;
      color: #2d3748;
      white-space: nowrap;
      letter-spacing: 0.3px;
      line-height: 1.2;
    }
  }

  // 悬停效果
  &:hover {
    .marker-pin .marker-dot {
      transform: scale(1.15);
      box-shadow:
        0 4px 12px rgba(102, 126, 234, 0.5),
        0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .marker-label {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(-8px) scale(1);
    }
  }
}

// 隐藏高德地图默认的信息窗体样式
::v-deep .amap-info-window {
  .amap-info-window-content {
    border: none !important;
    border-radius: 12px !important;
    background: transparent !important;
    box-shadow: none !important;
    padding: 0 !important;
  }

  .amap-info-window-tip {
    border-top-color: #667eea !important;
  }
}

::v-deep .amap-logo {
  display: none !important;
}

::v-deep .amap-copyright {
  display: none !important;
}
</style>
```
</details>
