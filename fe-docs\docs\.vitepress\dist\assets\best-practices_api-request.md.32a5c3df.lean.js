import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const A=JSON.parse('{"title":"API请求封装","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/api-request.md","filePath":"best-practices/api-request.md"}'),p={name:"best-practices/api-request.md"},o=l("",55),e=[o];function t(c,r,y,F,D,i){return n(),a("div",null,e)}const E=s(p,[["render",t]]);export{A as __pageData,E as default};
