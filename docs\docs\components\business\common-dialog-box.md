# commonDialogBox 通用弹窗组件

专为大屏显示场景设计的通用弹窗组件，支持全屏展开和自适应布局，适用于数据展示和交互场景。

## 功能特性

- 🖥️ 专为大屏场景设计的弹窗样式
- 🔧 支持全屏展开和收缩功能
- 🎨 内置多种主题样式，自动适配不同项目
- 📱 响应式设计，支持不同分辨率
- ⚡ 自动高度计算和布局优化
- 🔒 支持点击遮罩层不关闭弹窗

## 基础用法

```vue
<template>
  <div>
    <el-button @click="dialogVisible = true">打开弹窗</el-button>
    
    <common-dialog-box
      :visible.sync="dialogVisible"
      title="数据详情"
      width="70%"
      height="500px"
    >
      <div>弹窗内容</div>
    </common-dialog-box>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| title | 弹窗标题 | String | — | '标题' | 否 |
| height | 弹窗高度 | String | — | '600px' | 否 |
| width | 弹窗宽度 | String | — | '55%' | 否 |
| top | 距离顶部距离 | String | — | '0' | 否 |
| showdialogFull | 是否显示全屏按钮 | Boolean | true/false | true | 否 |
| dialogFull | 是否默认全屏显示 | Boolean | true/false | false | 否 |
| visible | 控制弹窗显示状态 | Boolean | true/false | false | 是 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:visible | 弹窗显示状态改变时触发 | (visible: boolean) |
| fullScreen | 全屏状态改变时触发 | (isFullScreen: boolean) |

### Slots

| 插槽名 | 说明 |
|--------|------|
| default | 弹窗内容区域 |

### 主题适配

组件会根据当前页面URL自动适配不同的主题样式：

- **默认主题**：适用于大部分场景
- **优车主题**：特殊样式适配
- **轮询主题**：轮询页面专用样式

## 使用示例

### 数据展示弹窗

```vue
<template>
  <div>
    <el-table :data="tableData" @row-click="showDetail">
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="value" label="值" />
    </el-table>
    
    <common-dialog-box
      :visible.sync="detailVisible"
      title="详细信息"
      width="80%"
      height="600px"
      :show-dialog-full="true"
      @fullScreen="handleFullScreen"
    >
      <el-descriptions :data="selectedRow" :column="2" border>
        <el-descriptions-item label="名称">{{ selectedRow.name }}</el-descriptions-item>
        <el-descriptions-item label="值">{{ selectedRow.value }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ selectedRow.type }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ selectedRow.status }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ selectedRow.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ selectedRow.updateTime }}</el-descriptions-item>
      </el-descriptions>
    </common-dialog-box>
  </div>
</template>

<script>
export default {
  data() {
    return {
      detailVisible: false,
      selectedRow: {},
      tableData: [
        { name: '设备001', value: '正常', type: '传感器', status: '在线', createTime: '2024-01-01', updateTime: '2024-01-15' },
        { name: '设备002', value: '异常', type: '控制器', status: '离线', createTime: '2024-01-02', updateTime: '2024-01-14' }
      ]
    }
  },
  methods: {
    showDetail(row) {
      this.selectedRow = row
      this.detailVisible = true
    },
    
    handleFullScreen(isFullScreen) {
      console.log('全屏状态改变:', isFullScreen)
    }
  }
}
</script>
```

### 表单编辑弹窗

```vue
<template>
  <div>
    <el-button type="primary" @click="openAddDialog">新增用户</el-button>
    <el-button type="warning" @click="openEditDialog">编辑用户</el-button>
    
    <common-dialog-box
      :visible.sync="formVisible"
      :title="formTitle"
      width="60%"
      height="500px"
    >
      <el-form :model="userForm" :rules="rules" ref="userForm" label-width="120px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" />
        </el-form-item>
        
        <el-form-item label="姓名" prop="realName">
          <el-input v-model="userForm.realName" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="用户" value="user" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="formVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </common-dialog-box>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formVisible: false,
      formTitle: '新增用户',
      userForm: {
        username: '',
        realName: '',
        email: '',
        role: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    openAddDialog() {
      this.formTitle = '新增用户'
      this.resetForm()
      this.formVisible = true
    },
    
    openEditDialog() {
      this.formTitle = '编辑用户'
      // 模拟加载用户数据
      this.userForm = {
        username: 'testuser',
        realName: '测试用户',
        email: '<EMAIL>',
        role: 'user'
      }
      this.formVisible = true
    },
    
    resetForm() {
      this.userForm = {
        username: '',
        realName: '',
        email: '',
        role: ''
      }
    },
    
    submitForm() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          console.log('提交用户数据:', this.userForm)
          this.formVisible = false
          this.$message.success('操作成功')
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
  padding: 20px 20px 0;
}
</style>
```

### 图表展示弹窗

```vue
<template>
  <div>
    <el-button @click="showChart">查看趋势图</el-button>
    
    <common-dialog-box
      :visible.sync="chartVisible"
      title="数据趋势分析"
      width="90%"
      height="700px"
      :show-dialog-full="true"
      :dialog-full="false"
    >
      <div class="chart-container">
        <div class="chart-header">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select v-model="chartParams.timeRange" @change="updateChart">
                <el-option label="最近7天" value="7d" />
                <el-option label="最近30天" value="30d" />
                <el-option label="最近90天" value="90d" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select v-model="chartParams.dataType" @change="updateChart">
                <el-option label="温度" value="temperature" />
                <el-option label="湿度" value="humidity" />
                <el-option label="压力" value="pressure" />
              </el-select>
            </el-col>
          </el-row>
        </div>
        
        <div class="chart-content" ref="chartContainer">
          <!-- 这里放置图表组件，如ECharts -->
          <div style="height: 400px; background: #f5f7fa; display: flex; align-items: center; justify-content: center;">
            图表内容区域 ({{ chartParams.dataType }} - {{ chartParams.timeRange }})
          </div>
        </div>
        
        <div class="chart-footer">
          <el-button @click="exportChart">导出图表</el-button>
          <el-button @click="refreshChart">刷新数据</el-button>
        </div>
      </div>
    </common-dialog-box>
  </div>
</template>

<script>
export default {
  data() {
    return {
      chartVisible: false,
      chartParams: {
        timeRange: '7d',
        dataType: 'temperature'
      }
    }
  },
  methods: {
    showChart() {
      this.chartVisible = true
      this.$nextTick(() => {
        this.initChart()
      })
    },
    
    initChart() {
      // 初始化图表
      console.log('初始化图表')
    },
    
    updateChart() {
      // 更新图表数据
      console.log('更新图表:', this.chartParams)
    },
    
    exportChart() {
      // 导出图表
      this.$message.success('图表导出成功')
    },
    
    refreshChart() {
      // 刷新图表数据
      this.updateChart()
      this.$message.success('数据刷新成功')
    }
  }
}
</script>

<style scoped>
.chart-container {
  padding: 20px;
}

.chart-header {
  margin-bottom: 20px;
}

.chart-content {
  margin-bottom: 20px;
}

.chart-footer {
  text-align: right;
}
</style>
```

## 高级特性

### 自适应高度

```vue
<template>
  <div>
    <common-dialog-box
      :visible.sync="adaptiveVisible"
      title="自适应内容"
      width="70%"
      height="auto"
    >
      <div class="adaptive-content">
        <div v-for="item in dynamicContent" :key="item.id">
          <p>{{ item.text }}</p>
        </div>
      </div>
    </common-dialog-box>
  </div>
</template>

<script>
export default {
  data() {
    return {
      adaptiveVisible: false,
      dynamicContent: []
    }
  },
  methods: {
    loadContent() {
      // 动态加载内容
      this.dynamicContent = Array.from({ length: 10 }, (_, i) => ({
        id: i,
        text: `动态内容 ${i + 1}`
      }))
    }
  }
}
</script>
```

## 注意事项

1. **性能考虑**：弹窗内容较重时建议使用 `v-if` 控制渲染
2. **主题适配**：组件会自动根据URL路径适配不同主题
3. **全屏模式**：全屏状态下会优化滚动和布局
4. **移动端**：在移动设备上会自动调整样式和交互

## 常见问题

### Q: 如何自定义弹窗样式？

A: 组件提供了丰富的CSS变量，可以通过覆盖样式来定制外观。

### Q: 支持嵌套弹窗吗？

A: 支持，组件使用 `append-to-body` 确保正确的层级关系。

### Q: 如何禁用全屏按钮？

A: 设置 `:show-dialog-full="false"` 即可隐藏全屏按钮。

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<template>
  <div id="largeScreen">
    <el-dialog
      :visible.sync="visible"
      :width="width"
      :top="top"
      ref="elDialog"
      :fullscreen="fullFlag"
      :append-to-body="true"
      :close-on-click-modal="false"
      :before-close="closeBtn"
      :class="{
        isfull: !showdialogFull,
        screenPopupYouche: isShow,
        screenPopup: !isShow,
      }"
    >
      <template slot="title">
        <div style="display: inline-flex">
          <div
            v-if="showdialogFull"
            style="position: absolute; right: 53px; top: 15px; cursor: pointer"
            @click.prevent="expand"
          >
            <img
              src="@/assets/images/screen/home/<USER>"
              alt=""
              style="display: block; width: 24px; height: 19px"
            />
          </div>
          <div class="title-sign">
            <img
              src="@/assets/images/screen/screenDialog/Hengshanlogheader.png.png"
              alt=""
            />
          </div>
          <div class="avue-crud__dialog__header">
            <span class="el-dialog__title">
              {{ title }}
            </span>
          </div>
        </div>
      </template>
      <div :class="fullFlag ? 'fullBody' : 'noFullBody'">
        <slot></slot>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "commonDialogBox",
  props: {
    title: {
      type: String,
      default: "标题",
    },
    height: {
      type: String,
      default: "600px",
    },
    width: {
      type: String,
      default: "55%",
    },
    top: {
      type: String,
      default: "00",
    },
    showdialogFull: {
      type: Boolean,
      default: true,
    },
    dialogFull: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fullFlag: this.dialogFull,
      isShow: false,
      widthFirst: null,
      isExpand: true,
    };
  },
  watch: {
    dialogFull: {
      handler(val) {
        this.fullFlag = val;
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.widthFirst = this.width;
    this.$nextTick(() => {
      this.$refs.elDialog.$el.firstChild.style.height = this.height;
      this.$refs.elDialog.$el.firstChild.style.transition =
        "width .2s linear, height .2s linear";
      this.width = this.widthFirst;
    });
  },
  created() {
    this.$nextTick(() => {
      this.getPath();
      let windowHref = window.location.href;
      // 获取项目id
      if (windowHref.includes("screen/hengshan")) {
        this.isShow = false;
      } else if (windowHref.includes("screen/longzhu")) {
        this.isShow = false;
      } else if (windowHref.includes("screen/youche")) {
        this.isShow = true;
      } else if (windowHref.includes("screen/index")) {
        this.isShow = false;
      } else if (windowHref.includes("screen/polling")) {
        this.isShow = true;
      }
    });
  },
  methods: {
    getPath() {
      let windowHref = window.location.href;
      if (windowHref.includes("screen/youche")) {
        var screenPopup = document.querySelector(".screenPopup");
        var elDialogHeader = screenPopup.querySelector(".el-dialog__header");
        elDialogHeader.classList.add("newClassName");
      } else {
        var screenPopup = document.querySelector(".screenPopup");
        var elDialogHeader = screenPopup.querySelector(".el-dialog__header");
        if (elDialogHeader.classList.contains("newClassName")) {
          elDialogHeader.classList.remove("newClassName");
        }
      }
    },
    expand() {
      if (this.isExpand) {
        this.$nextTick(() => {
          this.$refs.elDialog.$el.firstChild.style.height = "78%";
          this.$refs.elDialog.$el.firstChild.style.top = "47%";
          this.$refs.elDialog.$el.firstChild.style.width = "80%";
          this.isExpand = false;
        });
      } else {
        this.$nextTick(() => {
          this.$refs.elDialog.$el.firstChild.style.height = this.height;
          this.$refs.elDialog.$el.firstChild.style.top = "50%";
          this.$refs.elDialog.$el.firstChild.style.width = this.widthFirst;
          this.isExpand = true;
        });
      }
    },
    closeBtn() {
      this.$emit("update:visible", false);
    },
    clickFullScreen() {
      this.fullFlag = !this.fullFlag;
      this.$emit("fullScreen", this.fullFlag);
    },
  },
};
</script>

<style scoped lang="scss">
// 大量样式代码，包含大屏主题适配
::v-deep .screenPopup {
  background: rgba(0, 0, 0, 0.8);
}

.fullBody {
  height: 100%;
  overflow: hidden;
}

::v-deep .el-dialog__body {
  height: calc(100% - 30px);
}

::v-deep .noFullBody {
  max-width: 100%;
  padding: 8px 5px;
  height: 100%;
  overflow: auto;
}

// 更多样式定义...
</style>
```

</details>
