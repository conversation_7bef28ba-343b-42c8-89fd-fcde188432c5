# Git提交规范

本文档定义了项目中Git提交的规范，包括提交信息格式、分支管理和工作流程，旨在保持版本历史清晰，便于代码审查和版本追踪。

## 提交信息规范

我们采用 [Angular提交规范](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit) 作为标准格式，每个提交信息应包含**头部**、**正文**和**页脚**三个部分：

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

### 头部（必需）

头部是必需的，由类型（type）、作用域（scope，可选）和主题（subject）组成：

```
<type>(<scope>): <subject>
```

#### 类型（Type）

必须是以下类型之一：

- **feat**: 新功能（feature）
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码风格调整（不影响代码运行的变动）
- **refactor**: 重构（既不是新增功能，也不是修复bug的代码变动）
- **perf**: 性能优化
- **chore**: 构建过程或辅助工具的变动
- **revert**: 回滚之前的提交

#### 作用域（Scope）

作用域指定提交更改的部分，例如：

- **user**: 用户相关功能
- **auth**: 认证相关功能
- **api**: API相关功能
- **ui**: UI组件相关
- **config**: 配置相关

作用域可以根据项目实际情况灵活定义。

#### 主题（Subject）

主题是对变更的简短描述：

- 使用现在时态的动词开头："change"而非"changed"或"changes"
- 第一个字母不要大写
- 结尾不加句号（.）
- 不超过50个字符

### 正文（可选）

正文是对变更的详细描述，解释为什么要做这个变更以及它与之前行为的对比：

- 使用现在时态的动词
- 包含变更的动机，并与之前的行为进行对比
- 可以包含多个段落

### 页脚（可选）

页脚应包含关闭的Issue引用和Breaking Changes的信息：

```
Closes #123, #456
BREAKING CHANGE: 描述API变更的内容
```

### 提交信息示例

```
feat(user): 添加用户头像上传功能

实现用户头像上传功能，支持裁剪和预览。
添加了文件大小和类型的验证。

Closes #123
```

```
fix(auth): 修复登录失败不显示错误信息的问题

当API返回401错误时，现在会在登录表单下方显示错误提示。

Closes #456
```

```
docs(readme): 更新项目文档

更新了安装步骤和开发环境配置说明。
```

## 分支管理规范

### 分支命名

- **master/main**: 主分支，稳定的生产环境代码
- **develop**: 开发分支，最新的开发代码
- **feature/xxx**: 功能分支，用于开发新功能，如 `feature/user-avatar`
- **bugfix/xxx**: 修复分支，用于修复非紧急bug，如 `bugfix/login-error`
- **hotfix/xxx**: 热修复分支，用于修复紧急生产环境bug，如 `hotfix/critical-auth-issue`
- **release/xxx**: 发布分支，用于版本发布前的准备工作，如 `release/v1.2.0`

### 分支工作流

我们采用 [Git Flow](https://nvie.com/posts/a-successful-git-branching-model/) 工作流：

1. 从 `develop` 分支创建 `feature/xxx` 分支进行功能开发
2. 功能开发完成后，提交 Pull Request 到 `develop` 分支
3. 代码审查通过后，合并到 `develop` 分支
4. 当准备发布时，从 `develop` 分支创建 `release/xxx` 分支
5. 在 `release/xxx` 分支上进行最终修复
6. 完成后，将 `release/xxx` 分支同时合并到 `master` 和 `develop` 分支
7. 在 `master` 分支上打标签（tag）标记版本
8. 如果生产环境出现紧急bug，从 `master` 分支创建 `hotfix/xxx` 分支
9. 修复完成后，将 `hotfix/xxx` 分支同时合并到 `master` 和 `develop` 分支

## 提交前检查

在提交代码前，请确保：

1. 代码符合项目的编码规范（通过lint检查）
2. 提交信息符合上述规范
3. 不包含调试代码或注释
4. 不包含敏感信息（密码、密钥等）

## 自动化工具

为确保提交规范的执行，我们使用以下工具：

### Commitlint

用于检查提交信息是否符合规范：

```bash
# 安装
npm install --save-dev @commitlint/cli @commitlint/config-conventional

# 配置文件 commitlint.config.js
module.exports = {
  extends: ['@commitlint/config-conventional']
};
```

### Commitizen

用于生成符合规范的提交信息：

```bash
# 安装
npm install --save-dev commitizen cz-conventional-changelog

# 配置
# package.json
{
  "scripts": {
    "commit": "cz"
  },
  "config": {
    "commitizen": {
      "path": "cz-conventional-changelog"
    }
  }
}
```

### Husky

用于在提交前运行检查：

```bash
# 安装
npm install --save-dev husky

# 配置
# package.json
{
  "husky": {
    "hooks": {
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS",
      "pre-commit": "lint-staged"
    }
  }
}
```

## 总结

遵循一致的Git提交规范有助于：

- 自动生成更新日志（CHANGELOG）
- 简化版本管理
- 方便团队协作和代码审查
- 提高项目可维护性

每个开发人员都应该熟悉并遵循这些规范。如有任何问题或建议，请在团队会议中提出讨论。 