<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="g" x1="0" y1="0" x2="64" y2="64" gradientUnits="userSpaceOnUse">
      <stop stop-color="#0ea5e9"/>
      <stop offset="1" stop-color="#22d3ee"/>
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>
  <rect x="4" y="4" width="56" height="56" rx="14" fill="url(#g)" filter="url(#shadow)"/>
  <!-- Centered "zhy" mark -->
  <text x="32" y="41" text-anchor="middle" font-family="Inter, Arial, 'SF Pro Display', Helvetica, sans-serif" font-size="28" font-weight="700" fill="#FFFFFF" letter-spacing="0.5" style="paint-order: stroke; stroke: rgba(0,0,0,0.08); stroke-width: 0.5;">
    zhy
  </text>
</svg>


