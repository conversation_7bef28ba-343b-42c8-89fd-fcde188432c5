import{_ as s,o as a,c as l,V as n}from"./chunks/framework.3d729ebc.js";const d=JSON.parse('{"title":"Git工作流","description":"","frontmatter":{},"headers":[],"relativePath":"standards/git-workflow.md","filePath":"standards/git-workflow.md"}'),o={name:"standards/git-workflow.md"},p=n(`<h1 id="git工作流" tabindex="-1">Git工作流 <a class="header-anchor" href="#git工作流" aria-label="Permalink to &quot;Git工作流&quot;">​</a></h1><p>本文档详细介绍了我们团队在项目开发中采用的Git工作流程，包括分支策略、合并流程和常见操作指南，旨在规范开发过程，提高协作效率。</p><h2 id="git-flow工作流" tabindex="-1">Git Flow工作流 <a class="header-anchor" href="#git-flow工作流" aria-label="Permalink to &quot;Git Flow工作流&quot;">​</a></h2><p>我们采用基于 <a href="https://nvie.com/posts/a-successful-git-branching-model/" target="_blank" rel="noreferrer">Git Flow</a> 的工作流模型，该模型定义了严格的分支结构和发布流程。</p><h3 id="核心分支" tabindex="-1">核心分支 <a class="header-anchor" href="#核心分支" aria-label="Permalink to &quot;核心分支&quot;">​</a></h3><ul><li><strong>master/main</strong>: 主分支，永远保持可部署状态，只接受来自<code>release</code>和<code>hotfix</code>分支的合并</li><li><strong>develop</strong>: 开发分支，包含最新的开发代码，作为功能分支的集成点</li></ul><h3 id="辅助分支" tabindex="-1">辅助分支 <a class="header-anchor" href="#辅助分支" aria-label="Permalink to &quot;辅助分支&quot;">​</a></h3><ul><li><strong>feature/xxx</strong>: 功能分支，用于开发新功能</li><li><strong>release/xxx</strong>: 发布分支，用于版本发布前的准备工作</li><li><strong>hotfix/xxx</strong>: 热修复分支，用于修复生产环境的紧急问题</li><li><strong>bugfix/xxx</strong>: 修复分支，用于修复开发中的非紧急问题</li></ul><h2 id="详细工作流程" tabindex="-1">详细工作流程 <a class="header-anchor" href="#详细工作流程" aria-label="Permalink to &quot;详细工作流程&quot;">​</a></h2><h3 id="_1-功能开发流程" tabindex="-1">1. 功能开发流程 <a class="header-anchor" href="#_1-功能开发流程" aria-label="Permalink to &quot;1. 功能开发流程&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">功能开发</span></span>
<span class="line"><span style="color:#babed8;">        ↓</span></span>
<span class="line"><span style="color:#babed8;">develop → feature/xxx → 开发完成 → 提交PR → 代码审查 → 合并到develop</span></span></code></pre></div><ol><li><p>从<code>develop</code>分支创建功能分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pull</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-b</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">feature/user-avatar</span></span></code></pre></div></li><li><p>在功能分支上进行开发，定期提交：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">feat(user): 添加用户头像上传功能</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div></li><li><p>保持与<code>develop</code>分支同步：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">fetch</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">merge</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin/develop</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 解决冲突（如有）</span></span></code></pre></div></li><li><p>功能开发完成后，推送到远程仓库：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">push</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">feature/user-avatar</span></span></code></pre></div></li><li><p>创建Pull Request到<code>develop</code>分支，等待代码审查</p></li><li><p>代码审查通过后，合并到<code>develop</code>分支</p></li><li><p>删除功能分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">branch</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-d</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">feature/user-avatar</span></span></code></pre></div></li></ol><h3 id="_2-版本发布流程" tabindex="-1">2. 版本发布流程 <a class="header-anchor" href="#_2-版本发布流程" aria-label="Permalink to &quot;2. 版本发布流程&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">develop → release/vX.Y.Z → 修复 → 合并到master → 打标签 → 合并到develop</span></span></code></pre></div><ol><li><p>从<code>develop</code>分支创建发布分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pull</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-b</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">release/v1.2.0</span></span></code></pre></div></li><li><p>在发布分支上进行版本准备工作（修改版本号、文档等）：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 修改版本号</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">chore(release): 准备v1.2.0发布</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div></li><li><p>进行最终修复，修复发现的问题：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix(xxx): 修复xxx问题</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div></li><li><p>完成后，合并到<code>master</code>分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">master</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">merge</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--no-ff</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">release/v1.2.0</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">tag</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-a</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">v1.2.0</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">v1.2.0</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">push</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">master</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--tags</span></span></code></pre></div></li><li><p>同时合并到<code>develop</code>分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">merge</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--no-ff</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">release/v1.2.0</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">push</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span></code></pre></div></li><li><p>删除发布分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">branch</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-d</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">release/v1.2.0</span></span></code></pre></div></li></ol><h3 id="_3-热修复流程" tabindex="-1">3. 热修复流程 <a class="header-anchor" href="#_3-热修复流程" aria-label="Permalink to &quot;3. 热修复流程&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">master → hotfix/xxx → 修复完成 → 合并到master → 打标签 → 合并到develop</span></span></code></pre></div><ol><li><p>从<code>master</code>分支创建热修复分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">master</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pull</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-b</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">hotfix/critical-auth-issue</span></span></code></pre></div></li><li><p>修复问题：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix(auth): 修复认证失败问题</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div></li><li><p>修复完成后，合并到<code>master</code>分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">master</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">merge</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--no-ff</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">hotfix/critical-auth-issue</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">tag</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-a</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">v1.2.1</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">v1.2.1</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">push</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">master</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--tags</span></span></code></pre></div></li><li><p>同时合并到<code>develop</code>分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">merge</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--no-ff</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">hotfix/critical-auth-issue</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">push</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span></code></pre></div></li><li><p>删除热修复分支：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">branch</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-d</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">hotfix/critical-auth-issue</span></span></code></pre></div></li></ol><h2 id="常见git操作指南" tabindex="-1">常见Git操作指南 <a class="header-anchor" href="#常见git操作指南" aria-label="Permalink to &quot;常见Git操作指南&quot;">​</a></h2><h3 id="撤销本地修改" tabindex="-1">撤销本地修改 <a class="header-anchor" href="#撤销本地修改" aria-label="Permalink to &quot;撤销本地修改&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 撤销工作区修改</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&lt;</span><span style="color:#C3E88D;">fil</span><span style="color:#BABED8;">e</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 撤销暂存区修改</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">reset</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">HEAD</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&lt;</span><span style="color:#C3E88D;">fil</span><span style="color:#BABED8;">e</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&lt;</span><span style="color:#C3E88D;">fil</span><span style="color:#BABED8;">e</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 撤销最近一次提交</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">reset</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--soft</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">HEAD~1</span></span></code></pre></div><h3 id="处理冲突" tabindex="-1">处理冲突 <a class="header-anchor" href="#处理冲突" aria-label="Permalink to &quot;处理冲突&quot;">​</a></h3><ol><li><p>当合并或变基操作遇到冲突时：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 查看冲突文件</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">status</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 手动编辑解决冲突</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 冲突标记：</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># &lt;&lt;&lt;&lt;&lt;&lt;&lt; HEAD (当前更改)</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 你的代码</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># =======</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 其他分支的代码</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># &gt;&gt;&gt;&gt;&gt;&gt;&gt; branch-name (传入的更改)</span></span></code></pre></div></li><li><p>解决冲突后：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&lt;</span><span style="color:#C3E88D;">resolved-fil</span><span style="color:#BABED8;">e</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;"># 或继续合并/变基操作</span></span></code></pre></div></li></ol><h3 id="临时保存工作" tabindex="-1">临时保存工作 <a class="header-anchor" href="#临时保存工作" aria-label="Permalink to &quot;临时保存工作&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 保存当前工作进度</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">stash</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">save</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">正在开发登录功能</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看保存的工作进度</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">stash</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">list</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 恢复最近的工作进度</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">stash</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pop</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 恢复指定的工作进度</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">stash</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">apply</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">stash@{</span><span style="color:#F78C6C;">1</span><span style="color:#C3E88D;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 清理所有工作进度</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">stash</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">clear</span></span></code></pre></div><h2 id="最佳实践" tabindex="-1">最佳实践 <a class="header-anchor" href="#最佳实践" aria-label="Permalink to &quot;最佳实践&quot;">​</a></h2><ol><li><strong>频繁提交</strong>：每完成一个小功能点就提交一次，保持提交粒度适中</li><li><strong>定期同步</strong>：每天至少同步一次主分支的更新</li><li><strong>有意义的提交信息</strong>：遵循提交信息规范，清晰描述变更内容</li><li><strong>不提交生成的文件</strong>：确保<code>.gitignore</code>配置正确</li><li><strong>保护主分支</strong>：<code>master</code>和<code>develop</code>分支应设置保护，只能通过PR合并</li><li><strong>使用SSH密钥</strong>：避免频繁输入密码，提高安全性</li></ol><h2 id="常见问题与解决方案" tabindex="-1">常见问题与解决方案 <a class="header-anchor" href="#常见问题与解决方案" aria-label="Permalink to &quot;常见问题与解决方案&quot;">​</a></h2><h3 id="q-如何处理已经提交但需要修改的代码" tabindex="-1">Q: 如何处理已经提交但需要修改的代码？ <a class="header-anchor" href="#q-如何处理已经提交但需要修改的代码" aria-label="Permalink to &quot;Q: 如何处理已经提交但需要修改的代码？&quot;">​</a></h3><p>A: 使用<code>git commit --amend</code>修改最近一次提交，或使用<code>git rebase -i HEAD~n</code>修改多个提交。注意：不要修改已经推送到远程仓库的提交。</p><h3 id="q-合并时出现大量冲突怎么办" tabindex="-1">Q: 合并时出现大量冲突怎么办？ <a class="header-anchor" href="#q-合并时出现大量冲突怎么办" aria-label="Permalink to &quot;Q: 合并时出现大量冲突怎么办？&quot;">​</a></h3><p>A: 考虑使用以下策略：</p><ul><li>小步合并：将大的功能分支拆分成多个小分支逐步合并</li><li>使用工具：如VS Code、IntelliJ等IDE提供的冲突解决工具</li><li>与代码作者协商：对于复杂冲突，与相关代码的作者一起解决</li></ul><h3 id="q-如何回滚到之前的版本" tabindex="-1">Q: 如何回滚到之前的版本？ <a class="header-anchor" href="#q-如何回滚到之前的版本" aria-label="Permalink to &quot;Q: 如何回滚到之前的版本？&quot;">​</a></h3><p>A: 使用<code>git revert</code>创建新提交来撤销之前的更改，或在紧急情况下使用<code>git reset --hard &lt;commit-id&gt;</code>（谨慎使用，会丢失历史）。</p><h2 id="工具推荐" tabindex="-1">工具推荐 <a class="header-anchor" href="#工具推荐" aria-label="Permalink to &quot;工具推荐&quot;">​</a></h2><ul><li><strong>SourceTree</strong>：直观的Git图形界面工具</li><li><strong>GitLens</strong>：VS Code插件，增强Git功能</li><li><strong>Git Graph</strong>：可视化提交历史和分支结构</li><li><strong>GitKraken</strong>：功能强大的Git客户端</li></ul><h2 id="总结" tabindex="-1">总结 <a class="header-anchor" href="#总结" aria-label="Permalink to &quot;总结&quot;">​</a></h2><p>良好的Git工作流可以显著提高团队协作效率，减少集成冲突，保持代码库的健康状态。每位团队成员都应熟悉并严格遵循本文档中的工作流程和最佳实践。如有任何问题或改进建议，请在团队会议中提出讨论。</p>`,39),e=[p];function t(c,r,i,y,B,D){return a(),l("div",null,e)}const h=s(o,[["render",t]]);export{d as __pageData,h as default};
