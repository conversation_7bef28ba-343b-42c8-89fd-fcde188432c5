# 项目结构

本页面介绍现代化水库管理矩阵前端项目的标准目录结构及各部分的作用。理解项目结构有助于您更快地了解代码组织方式，便于开发和维护。

## Matrix-UI项目结构

```
matrix-ui/
├── public/                 # 静态资源目录（不会被构建工具处理）
│   ├── favicon.ico         # 网站图标
│   ├── index.html          # HTML模板
│   ├── Cesium/            # Cesium静态资源
│   ├── js/                # 第三方JS库
│   ├── json/              # 静态JSON配置文件
│   ├── static/            # 静态配置文件
│   │   └── config.js      # 系统配置文件
│   └── vtour/             # 全景图资源
├── src/                    # 源代码目录
│   ├── api/                # API请求封装
│   │   ├── index.js        # API模块聚合
│   │   ├── login.js        # 登录相关API
│   │   ├── menu.js         # 菜单API
│   │   ├── map.js          # 地图API
│   │   ├── admin/          # 管理员API
│   │   ├── adminIndex/     # 管理首页API
│   │   ├── calculations/   # 计算模块API
│   │   ├── digitalTwin/    # 数字孪生API
│   │   ├── hydmodel/       # 水文模型API
│   │   ├── ipm/            # IPM相关API
│   │   ├── map/            # 地图服务API
│   │   ├── matrix/         # 矩阵管理API
│   │   ├── monitor/        # 监控API
│   │   ├── projectInfo/    # 项目信息API
│   │   ├── screen/         # 大屏API
│   │   ├── system/         # 系统API
│   │   ├── tool/           # 工具API
│   │   └── typicalrrainfall/ # 典型降雨API
│   ├── assets/             # 资源文件
│   │   ├── font/           # 字体文件
│   │   ├── icons/          # SVG图标
│   │   ├── images/         # 图片资源
│   │   └── styles/         # 全局样式
│   │       ├── index.scss  # 主样式文件
│   │       ├── element-variables.scss # Element UI主题
│   │       └── zhy.scss    # 智洋定制样式
│   ├── components/         # 全局通用组件
│   │   ├── Breadcrumb/     # 面包屑导航
│   │   ├── ConfigData/     # 配置数据组件
│   │   ├── Crontab/        # 定时任务组件
│   │   ├── CustomFileUpload/ # 自定义文件上传
│   │   ├── CustomIframe/   # 自定义iframe
│   │   ├── DictData/       # 字典数据组件
│   │   ├── DictTag/        # 字典标签组件
│   │   ├── DigitalTwin/    # 数字孪生组件
│   │   ├── DigitalTwin5/   # 数字孪生5.0组件
│   │   ├── Editor/         # 富文本编辑器
│   │   ├── ElYearPicker/   # 年份选择器
│   │   ├── FilePreview/    # 文件预览组件
│   │   ├── FileUpload/     # 文件上传组件
│   │   ├── Hamburger/      # 汉堡菜单
│   │   ├── HeaderSearch/   # 头部搜索
│   │   ├── IconSelect/     # 图标选择器
│   │   ├── ImageUpload/    # 图片上传组件
│   │   ├── InputNumber/    # 自定义数字输入框
│   │   ├── InputWord/      # 自定义输入框
│   │   ├── Pagination/     # 分页组件
│   │   ├── PanThumb/       # 缩略图组件
│   │   ├── ParentView/     # 父级视图
│   │   ├── RightPanel/     # 右侧面板
│   │   ├── RightToolbar/   # 右侧工具栏
│   │   ├── Screenfull/     # 全屏组件
│   │   ├── SizeSelect/     # 尺寸选择器
│   │   ├── SvgIcon/        # SVG图标组件
│   │   ├── ThemePicker/    # 主题选择器
│   │   ├── TopNav/         # 顶部导航
│   │   ├── commonDialogBox/ # 通用对话框
│   │   ├── dictSelect/     # 字典选择器
│   │   └── iFrame/         # iframe组件
│   ├── directive/          # 全局指令
│   ├── layout/             # 布局组件
│   ├── mixins/             # 混入
│   ├── plugins/            # 插件配置
│   ├── router/             # 路由配置
│   │   └── index.js        # 路由主文件
│   ├── store/              # Vuex状态管理
│   │   ├── index.js        # Store主文件
│   │   ├── getters.js      # 全局getters
│   │   └── modules/        # Store模块
│   │       ├── app.js      # 应用状态
│   │       ├── common.js   # 通用状态
│   │       ├── deepseek.js # DeepSeek集成
│   │       ├── dict.js     # 字典状态
│   │       ├── jiankong.js # 监控状态
│   │       ├── managePlatform.js # 管理平台状态
│   │       ├── map.js      # 地图状态
│   │       ├── pageDialog.js # 页面对话框状态
│   │       ├── permission.js # 权限状态
│   │       ├── screenParams.js # 大屏参数状态
│   │       ├── settings.js # 设置状态
│   │       ├── tagsView.js # 标签视图状态
│   │       └── user.js     # 用户状态
│   ├── utils/              # 工具函数
│   │   ├── index.js        # 通用工具函数
│   │   ├── request.js      # HTTP请求封装
│   │   ├── auth.js         # 认证相关工具
│   │   ├── errorCode.js    # 错误码定义
│   │   ├── formatTime.js   # 时间格式化
│   │   ├── formValidate.js # 表单验证工具
│   │   ├── jsencrypt.js    # 加密解密工具
│   │   ├── math.js         # 数学计算工具
│   │   ├── permission.js   # 权限工具
│   │   ├── resize.js       # 窗口缩放工具
│   │   ├── scroll-to.js    # 滚动工具
│   │   ├── uav.js          # 无人机相关工具
│   │   ├── validate.js     # 验证工具
│   │   ├── zhy.js          # 智洋定制工具
│   │   ├── dict/           # 字典工具
│   │   └── generator/      # 代码生成工具
│   ├── views/              # 页面组件
│   │   ├── index.vue       # 首页
│   │   ├── login.vue       # 登录页
│   │   ├── register.vue    # 注册页
│   │   ├── redirect.vue    # 重定向页
│   │   ├── sharing.vue     # 分享页
│   │   ├── adminIndex/     # 管理首页
│   │   ├── components/     # 页面级组件
│   │   ├── dashboard/      # 仪表板
│   │   ├── document/       # 文档管理
│   │   ├── error/          # 错误页面
│   │   ├── hydmodel/       # 水文模型
│   │   ├── ipm/            # IPM模块
│   │   ├── map/            # 地图相关页面
│   │   ├── matrix/         # 矩阵管理页面
│   │   ├── monitor/        # 监控页面
│   │   ├── screenPage/     # 大屏页面
│   │   │   ├── index.vue   # 大屏入口
│   │   │   ├── homePage/   # 首页大屏
│   │   │   ├── siquan/     # 四全大屏
│   │   │   │   ├── qfg/    # 全覆盖大屏
│   │   │   │   ├── qth/    # 全天候大屏
│   │   │   │   ├── qys/    # 全要素大屏
│   │   │   │   └── qzq/    # 全周期大屏
│   │   │   ├── sizhi/      # 四制大屏
│   │   │   │   ├── fazhi/  # 法制大屏
│   │   │   │   ├── jizhi/  # 机制大屏
│   │   │   │   ├── tizhi/  # 体制大屏
│   │   │   │   └── zerenzhi/ # 责任制大屏
│   │   │   ├── siyuNew/    # 四预大屏
│   │   │   │   ├── yuan/   # 预案大屏
│   │   │   │   ├── yubao/  # 预报大屏
│   │   │   │   ├── yujing/ # 预警大屏
│   │   │   │   └── yuyan/  # 预演大屏
│   │   │   └── siguan/     # 四管大屏
│   │   │       ├── sg-anquan/   # 安全管理大屏
│   │   │       ├── sg-chuxian/  # 除险大屏
│   │   │       ├── sg-tijian/   # 体检大屏
│   │   │       └── sg-weihu/    # 维护大屏
│   │   ├── system/         # 系统管理
│   │   └── tool/           # 工具页面
│   ├── App.vue             # 根组件
│   ├── main.js             # 入口文件
│   ├── mixin.js            # 全局混入
│   ├── permission.js       # 权限控制
│   └── settings.js         # 项目设置
├── .editorconfig           # 编辑器配置
├── .eslintignore           # ESLint忽略文件
├── .eslintrc.js            # ESLint配置
├── .gitignore              # Git忽略文件
├── babel.config.js         # Babel配置
├── package.json            # 项目依赖和脚本
├── package-lock.json       # 依赖版本锁定
├── README.md               # 项目说明（24行）
└── vue.config.js           # Vue CLI配置（200行）
```

## 核心目录详解

### 1. `src/api` - API接口层

按业务模块组织API接口，支持水库管理的各个功能领域：

```js
// src/api/monitor/waterLevel.js - 水位监测API
import request from '@/utils/request'

export function getWaterLevelData(params) {
  return request({
    url: '/monitor/water-level/list',
    method: 'get',
    params
  })
}

export function getRealtimeWaterLevel(stationId) {
  return request({
    url: `/monitor/water-level/realtime/${stationId}`,
    method: 'get'
  })
}
```

```js
// src/api/screen/dashboard.js - 大屏数据API
import request from '@/utils/request'

export function getScreenData(screenType) {
  return request({
    url: `/screen/data/${screenType}`,
    method: 'get'
  })
}

export function getDigitalTwinData() {
  return request({
    url: '/digital-twin/scene-data',
    method: 'get'
  })
}
```

### 2. `src/components` - 组件库

#### 基础组件
- `Pagination` - 分页组件，支持大数据量展示
- `FileUpload/ImageUpload` - 文件上传组件
- `Editor` - 富文本编辑器
- `DictTag/DictData` - 字典数据展示组件

#### 业务组件
- `DigitalTwin` - 数字孪生3D展示组件
- `ScreenCommonDialog` - 大屏通用弹窗组件
- `CustomFileUpload` - 自定义文件上传组件

#### 布局组件
- `RightToolbar` - 右侧工具栏
- `Breadcrumb` - 面包屑导航
- `HeaderSearch` - 头部搜索

### 3. `src/views` - 页面视图层

#### 大屏页面结构 (`screenPage/`)
按照水库管理"四全四制四预四管"体系组织：

```vue
<!-- src/views/screenPage/siquan/qfg/index.vue -->
<template>
  <div class="screen-container siquan-qfg">
    <!-- 全覆盖大屏内容 -->
    <div class="screen-header">
      <h1>全覆盖监测大屏</h1>
    </div>
    <div class="screen-content">
      <!-- 监测点位分布图 -->
      <!-- 实时数据展示 -->
      <!-- 预警信息 -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'SiquanQfgScreen',
  data() {
    return {
      screenData: {},
      timer: null
    }
  },
  mounted() {
    this.initScreen()
    this.startDataRefresh()
  },
  beforeDestroy() {
    this.stopDataRefresh()
  }
}
</script>
```

#### 系统管理页面 (`system/`)
- 用户管理
- 角色权限
- 菜单管理
- 字典管理
- 系统配置

#### 业务功能页面
- `hydmodel/` - 水文模型相关页面
- `monitor/` - 监控管理页面
- `matrix/` - 矩阵管理页面
- `map/` - 地图功能页面

### 4. `src/store` - 状态管理

使用Vuex模块化管理状态：

```js
// src/store/modules/map.js - 地图状态管理
export default {
  namespaced: true,
  state: {
    currentMap: null,
    layers: [],
    markers: [],
    viewerConfig: {
      center: [116.404, 39.915],
      zoom: 10
    }
  },
  mutations: {
    SET_CURRENT_MAP(state, map) {
      state.currentMap = map
    },
    ADD_LAYER(state, layer) {
      state.layers.push(layer)
    },
    ADD_MARKER(state, marker) {
      state.markers.push(marker)
    }
  },
  actions: {
    initMap({ commit }, config) {
      // 初始化地图
    },
    addMonitoringPoint({ commit }, point) {
      // 添加监测点
    }
  }
}
```

```js
// src/store/modules/screenParams.js - 大屏参数状态
export default {
  namespaced: true,
  state: {
    currentScreen: '',
    screenData: {},
    refreshInterval: 30000,
    isFullscreen: false
  },
  mutations: {
    SET_CURRENT_SCREEN(state, screen) {
      state.currentScreen = screen
    },
    UPDATE_SCREEN_DATA(state, data) {
      state.screenData = { ...state.screenData, ...data }
    }
  }
}
```

### 5. `src/utils` - 工具函数库

#### 核心工具文件
- `request.js` - HTTP请求封装，包含拦截器配置
- `auth.js` - 认证相关工具（Token管理）
- `zhy.js` - 智洋定制工具函数（322行）
- `formatTime.js` - 时间格式化工具（317行）

```js
// src/utils/request.js - 请求拦截器示例
service.interceptors.request.use(config => {
  // 添加认证Token
  if (getToken() && !config.headers.isToken) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  
  // 防重复提交
  if (!config.headers.repeatSubmit && (config.method === 'post' || config.method === 'put')) {
    // 防重复提交逻辑
  }
  
  return config
}, error => {
  return Promise.reject(error)
})
```

### 6. `public/static/config.js` - 系统配置

```js
// 系统默认配置（1314行大型配置文件）
export const systemDefault = {
  // 系统基础配置
  systemName: '现代化水库管理矩阵',
  version: '3.6.3',
  
  // 地图配置
  mapConfig: {
    defaultCenter: [116.404, 39.915],
    defaultZoom: 10,
    cesiumToken: 'your_cesium_token'
  },
  
  // 监测站点配置
  monitoringStations: [
    // 大量监测站点配置
  ],
  
  // 大屏配置
  screenConfig: {
    refreshInterval: 30000,
    resolution: '1920x1080'
  }
}
```

## 路由结构设计

### 路由层级规划

```js
// src/router/index.js - 路由配置示例
export const constantRoutes = [
  // 大屏路由（独立布局）
  {
    path: '',
    redirect: 'home',
    component: () => import('@/views/screenPage/index.vue'),
    children: [
      {
        path: 'home',
        component: () => import('@/views/screenPage/homePage/index.vue'),
        meta: { title: '首页大屏' }
      },
      // 四全大屏路由
      {
        path: 'siquanQfg',
        component: () => import('@/views/screenPage/siquan/qfg/index'),
        meta: { title: '四全-全覆盖' }
      }
      // ... 更多大屏路由
    ]
  },
  
  // 管理后台路由（Layout布局）
  {
    path: '/admin',
    component: Layout,
    children: [
      // 后台管理页面
    ]
  }
]
```

### 路由元信息配置

```js
meta: {
  title: 'title',           // 页面标题
  icon: 'svg-name',         // 菜单图标
  noCache: true,            // 是否缓存
  requiresAuth: true,       // 是否需要认证
  roles: ['admin'],         // 角色权限
  permissions: ['system:user:view'], // 功能权限
  activeMenu: '/system/user', // 高亮菜单
  breadcrumb: false         // 面包屑显示
}
```

## 开发规范

### 命名规范

```js
// 文件命名：kebab-case
water-level-monitor.vue
digital-twin-scene.js

// 组件名：PascalCase
export default {
  name: 'WaterLevelMonitor'
}

// 变量/方法：camelCase
const waterLevelData = []
const getWaterLevelData = () => {}

// 常量：UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3
const API_BASE_URL = '/api'
```

### 组件开发规范

```vue
<!-- 组件模板规范 -->
<template>
  <div class="component-container">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {
    // props定义
  },
  data() {
    return {
      // 响应式数据
    }
  },
  computed: {
    // 计算属性
  },
  watch: {
    // 监听器
  },
  created() {
    // 生命周期
  },
  mounted() {
    // DOM挂载后
  },
  beforeDestroy() {
    // 组件销毁前清理
  },
  methods: {
    // 方法定义
  }
}
</script>

<style lang="scss" scoped>
.component-container {
  // 样式定义
}
</style>
```

### 样式开发规范

```scss
// 使用BEM命名法
.screen-container {
  &__header {
    // 头部样式
  }
  
  &__content {
    // 内容样式
  }
  
  &--fullscreen {
    // 全屏修饰符
  }
}

// 响应式设计
@media (max-width: 1920px) {
  .screen-container {
    transform: scale(0.8);
  }
}
```

## 技术栈说明

### 核心依赖
- **Vue 2.6.12** - 前端框架
- **Element UI 2.15.14** - UI组件库
- **Vue Router 3.4.9** - 路由管理
- **Vuex 3.6.0** - 状态管理
- **Axios 0.24.0** - HTTP请求库

### 业务相关依赖
- **Cesium 1.108.0** - 3D地图引擎
- **ECharts 5.4.0** - 图表库
- **ECharts-GL 2.0.9** - 3D图表
- **Leaflet 1.9.4** - 2D地图库
- **Three.js 0.178.0** - 3D图形库

### 工具依赖
- **Day.js 1.11.13** - 时间处理
- **Lodash 4.17.21** - 工具函数库
- **File-saver 2.0.5** - 文件下载
- **JS-Cookie 3.0.1** - Cookie管理

这个项目结构体现了现代化水库管理系统的复杂性和专业性，通过模块化的组织方式确保了代码的可维护性和可扩展性。