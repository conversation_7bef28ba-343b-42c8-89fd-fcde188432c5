import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const A=JSON.parse('{"title":"路由管理","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/routing.md","filePath":"best-practices/routing.md"}'),p={name:"best-practices/routing.md"},o=l("",55),e=[o];function t(c,r,F,y,D,i){return n(),a("div",null,e)}const E=s(p,[["render",t]]);export{A as __pageData,E as default};
