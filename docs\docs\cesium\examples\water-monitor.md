# 智慧水务监控系统

基于Vue.js和Cesium构建的智慧水务3D监控系统，实现水务设施的实时监控、告警管理和数据可视化。

## 功能概览

- 🏭 **水务设施3D标注** - 水厂、泵站、水库等设施的3D可视化
- 📊 **实时数据监控** - 水位、流量、水质等关键指标实时显示
- ⚠️ **告警点显示** - 异常设施的动态告警效果
- 📈 **数据统计图表** - ECharts集成的数据分析图表
- 🎮 **交互式控制** - 设施查询、图层控制、视角操作

## 系统架构

```
水务监控系统
├── 前端展示层 (Vue.js + Cesium)
│   ├── 3D地图组件
│   ├── 控制面板组件
│   ├── 图表组件
│   └── 信息面板组件
├── 数据服务层 (Mock API / WebSocket)
│   ├── 设施数据接口
│   ├── 实时监控数据
│   └── 告警信息推送
└── 业务逻辑层
    ├── 数据处理
    ├── 告警逻辑
    └── 状态管理
```

## 核心组件实现

### 1. 主要Cesium组件

```vue
<template>
  <div class="water-monitor-container">
    <!-- 3D地图容器 -->
    <div id="cesiumContainer" class="cesium-viewer"></div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <el-card class="panel-card">
        <div slot="header" class="panel-header">
          <span>水务监控系统</span>
          <el-button size="mini" type="primary" @click="refreshData">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>
        
        <!-- 设施类型过滤 -->
        <el-row class="control-row">
          <el-col :span="24">
            <span class="control-label">设施类型：</span>
            <el-checkbox-group v-model="visibleFacilityTypes" @change="filterFacilities">
              <el-checkbox label="waterPlant">水厂</el-checkbox>
              <el-checkbox label="pumpStation">泵站</el-checkbox>
              <el-checkbox label="reservoir">水库</el-checkbox>
              <el-checkbox label="pipeline">管网</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
        
        <!-- 告警开关 -->
        <el-row class="control-row">
          <el-col :span="12">
            <span class="control-label">告警显示：</span>
            <el-switch v-model="showAlarms" @change="toggleAlarms"></el-switch>
          </el-col>
          <el-col :span="12">
            <span class="control-label">实时更新：</span>
            <el-switch v-model="autoUpdate" @change="toggleAutoUpdate"></el-switch>
          </el-col>
        </el-row>
        
        <!-- 图层控制 -->
        <el-row class="control-row">
          <el-col :span="24">
            <span class="control-label">图层透明度：</span>
            <el-slider 
              v-model="layerAlpha" 
              :min="0" 
              :max="100" 
              :step="10"
              @change="updateLayerAlpha"
            ></el-slider>
          </el-col>
        </el-row>
      </el-card>
    </div>
    
    <!-- 统计面板 -->
    <div class="stats-panel">
      <el-row :gutter="15">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ stats.totalFacilities }}</div>
              <div class="stats-label">总设施数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card normal">
            <div class="stats-item">
              <div class="stats-value">{{ stats.normalCount }}</div>
              <div class="stats-label">正常运行</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card warning">
            <div class="stats-item">
              <div class="stats-value">{{ stats.warningCount }}</div>
              <div class="stats-label">异常告警</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card offline">
            <div class="stats-item">
              <div class="stats-value">{{ stats.offlineCount }}</div>
              <div class="stats-label">离线设备</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 详情面板 -->
    <div v-if="selectedFacility" class="info-panel">
      <el-card class="info-card">
        <div slot="header" class="info-header">
          <span>{{ selectedFacility.name }}</span>
          <el-button size="mini" type="text" @click="closeInfoPanel">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
        
        <div class="facility-info">
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="设施类型">
              {{ getFacilityTypeName(selectedFacility.type) }}
            </el-descriptions-item>
            <el-descriptions-item label="运行状态">
              <el-tag :type="getStatusTagType(selectedFacility.status)">
                {{ getStatusText(selectedFacility.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="水位">
              {{ selectedFacility.waterLevel }} m
            </el-descriptions-item>
            <el-descriptions-item label="流量">
              {{ selectedFacility.flowRate }} m³/h
            </el-descriptions-item>
            <el-descriptions-item label="水质">
              {{ selectedFacility.waterQuality }}
            </el-descriptions-item>
            <el-descriptions-item label="最后更新">
              {{ formatTime(selectedFacility.lastUpdate) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <!-- 实时数据图表 -->
          <div class="facility-chart">
            <div ref="facilityChart" style="width: 100%; height: 200px;"></div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import * as Cesium from 'cesium'
import * as echarts from 'echarts'

export default {
  name: 'WaterMonitorSystem',
  data() {
    return {
      viewer: null,
      facilityEntities: [],
      alarmEntities: [],
      selectedFacility: null,
      facilityChart: null,
      
      // 控制状态
      visibleFacilityTypes: ['waterPlant', 'pumpStation', 'reservoir', 'pipeline'],
      showAlarms: true,
      autoUpdate: true,
      layerAlpha: 80,
      
      // 数据状态
      facilities: [],
      alarmPoints: [],
      stats: {
        totalFacilities: 0,
        normalCount: 0,
        warningCount: 0,
        offlineCount: 0
      },
      
      // 定时器
      updateTimer: null,
      
      // 初始化位置 - 以某个城市为例
      initialPosition: {
        longitude: 116.391,
        latitude: 39.904,
        height: 10000
      }
    }
  },
  
  mounted() {
    this.initCesium()
    this.loadInitialData()
    this.setupEventHandlers()
    
    if (this.autoUpdate) {
      this.startAutoUpdate()
    }
  },
  
  beforeDestroy() {
    this.cleanup()
  },
  
  methods: {
    // 初始化Cesium
    initCesium() {
      // 设置Cesium Ion令牌
      Cesium.Ion.defaultAccessToken = 'your_access_token_here'
      
      // 创建Cesium查看器
      this.viewer = new Cesium.Viewer('cesiumContainer', {
        terrainProvider: Cesium.createWorldTerrain(),
        animation: false,
        baseLayerPicker: false,
        fullscreenButton: false,
        vrButton: false,
        geocoder: false,
        homeButton: false,
        infoBox: false,
        sceneModePicker: false,
        selectionIndicator: false,
        timeline: false,
        navigationHelpButton: false,
        navigationInstructionsInitiallyVisible: false,
        scene3DOnly: true,
        shouldAnimate: true,
        shadows: true
      })
      
      // 设置初始视角
      this.viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          this.initialPosition.longitude,
          this.initialPosition.latitude,
          this.initialPosition.height
        ),
        orientation: {
          heading: Cesium.Math.toRadians(0),
          pitch: Cesium.Math.toRadians(-45),
          roll: 0.0
        },
        duration: 2
      })
      
      // 禁用默认的双击行为
      this.viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      )
    },
    
    // 设置事件处理器
    setupEventHandlers() {
      const handler = new Cesium.ScreenSpaceEventHandler(this.viewer.scene.canvas)
      
      // 左键点击事件
      handler.setInputAction((event) => {
        const pickedObject = this.viewer.scene.pick(event.position)
        
        if (Cesium.defined(pickedObject) && pickedObject.id) {
          const entity = pickedObject.id
          
          // 检查是否是设施实体
          if (entity.properties && entity.properties.facilityData) {
            this.selectFacility(entity.properties.facilityData)
          }
        } else {
          // 点击空白处取消选择
          this.selectedFacility = null
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
      
      // 鼠标悬停效果
      handler.setInputAction((event) => {
        const pickedObject = this.viewer.scene.pick(event.endPosition)
        
        // 重置所有实体的高亮状态
        this.facilityEntities.forEach(entity => {
          if (entity.point) {
            entity.point.scale = 1.0
          }
        })
        
        if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.point) {
          // 高亮悬停的实体
          pickedObject.id.point.scale = 1.3
          this.viewer.canvas.style.cursor = 'pointer'
        } else {
          this.viewer.canvas.style.cursor = 'default'
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    },
    
    // 加载初始数据
    async loadInitialData() {
      try {
        // 模拟加载水务设施数据
        this.facilities = await this.mockLoadFacilities()
        
        // 模拟加载告警数据
        this.alarmPoints = await this.mockLoadAlarms()
        
        // 渲染设施
        this.renderFacilities()
        
        // 渲染告警点
        if (this.showAlarms) {
          this.renderAlarms()
        }
        
        // 更新统计数据
        this.updateStats()
        
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('数据加载失败')
      }
    },
    
    // 模拟加载设施数据
    async mockLoadFacilities() {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      return [
        {
          id: 'plant_001',
          name: '第一水厂',
          type: 'waterPlant',
          longitude: 116.391,
          latitude: 39.904,
          height: 0,
          status: 'normal',
          waterLevel: 15.2,
          flowRate: 2500,
          waterQuality: '优',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'pump_001',
          name: '中央泵站',
          type: 'pumpStation',
          longitude: 116.395,
          latitude: 39.908,
          height: 0,
          status: 'warning',
          waterLevel: 8.7,
          flowRate: 1800,
          waterQuality: '良',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'reservoir_001',
          name: '城东水库',
          type: 'reservoir',
          longitude: 116.400,
          latitude: 39.915,
          height: 0,
          status: 'normal',
          waterLevel: 45.6,
          flowRate: 0,
          waterQuality: '优',
          lastUpdate: new Date().toISOString()
        },
        {
          id: 'pipeline_001',
          name: '主供水管道',
          type: 'pipeline',
          longitude: 116.385,
          latitude: 39.898,
          height: 0,
          status: 'alarm',
          waterLevel: 0,
          flowRate: 3200,
          waterQuality: '异常',
          lastUpdate: new Date().toISOString()
        },
        // 添加更多模拟数据...
        ...Array.from({ length: 20 }, (_, i) => ({
          id: `facility_${i + 5}`,
          name: `设施点${i + 5}`,
          type: ['waterPlant', 'pumpStation', 'reservoir', 'pipeline'][i % 4],
          longitude: 116.391 + (Math.random() - 0.5) * 0.05,
          latitude: 39.904 + (Math.random() - 0.5) * 0.05,
          height: 0,
          status: ['normal', 'warning', 'alarm'][Math.floor(Math.random() * 3)],
          waterLevel: Math.random() * 50,
          flowRate: Math.random() * 5000,
          waterQuality: ['优', '良', '一般', '异常'][Math.floor(Math.random() * 4)],
          lastUpdate: new Date().toISOString()
        }))
      ]
    },
    
    // 模拟加载告警数据
    async mockLoadAlarms() {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      return [
        {
          id: 'alarm_001',
          name: '压力异常告警',
          longitude: 116.395,
          latitude: 39.908,
          level: 'high',
          type: '压力异常',
          message: '管道压力超出正常范围',
          time: new Date().toISOString()
        },
        {
          id: 'alarm_002',
          name: '水质异常告警',
          longitude: 116.385,
          latitude: 39.898,
          level: 'medium',
          type: '水质异常',
          message: '检测到水质指标异常',
          time: new Date().toISOString()
        }
      ]
    },
    
    // 渲染水务设施
    renderFacilities() {
      // 清除现有实体
      this.facilityEntities.forEach(entity => {
        this.viewer.entities.remove(entity)
      })
      this.facilityEntities = []
      
      // 添加设施实体
      this.facilities.forEach(facility => {
        if (!this.visibleFacilityTypes.includes(facility.type)) {
          return
        }
        
        const color = this.getFacilityColor(facility.status)
        const icon = this.getFacilityIcon(facility.type)
        
        const entity = this.viewer.entities.add({
          id: facility.id,
          position: Cesium.Cartesian3.fromDegrees(
            facility.longitude,
            facility.latitude,
            facility.height
          ),
          
          // 使用图标标记
          billboard: {
            image: icon,
            scale: 0.6,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          },
          
          // 添加文字标签
          label: {
            text: facility.name,
            font: '14px sans-serif',
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.TOP,
            pixelOffset: new Cesium.Cartesian2(0, 10),
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 5000)
          },
          
          // 状态指示点
          point: {
            pixelSize: 12,
            color: color,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            distanceDisplayCondition: new Cesium.DistanceDisplayCondition(5000, 50000)
          },
          
          // 存储设施数据
          properties: {
            facilityData: facility
          }
        })
        
        this.facilityEntities.push(entity)
      })
    },
    
    // 渲染告警点
    renderAlarms() {
      // 清除现有告警实体
      this.alarmEntities.forEach(entity => {
        this.viewer.entities.remove(entity)
      })
      this.alarmEntities = []
      
      if (!this.showAlarms) return
      
      this.alarmPoints.forEach(alarm => {
        const entity = this.viewer.entities.add({
          position: Cesium.Cartesian3.fromDegrees(
            alarm.longitude,
            alarm.latitude,
            100
          ),
          
          // 告警图标
          billboard: {
            image: 'data:image/svg+xml;base64,' + btoa(`
              <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
                <circle cx="16" cy="16" r="12" fill="#ff4444" stroke="#ffffff" stroke-width="2"/>
                <text x="16" y="22" text-anchor="middle" fill="white" font-size="16" font-weight="bold">!</text>
              </svg>
            `),
            scale: 1.0,
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM
          },
          
          // 告警标签
          label: {
            text: alarm.name,
            font: '12px sans-serif',
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            fillColor: Cesium.Color.RED,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
            verticalOrigin: Cesium.VerticalOrigin.TOP,
            pixelOffset: new Cesium.Cartesian2(0, 10)
          },
          
          // 告警范围圆圈
          ellipse: {
            semiMinorAxis: 200.0,
            semiMajorAxis: 200.0,
            height: 1,
            material: new Cesium.ColorMaterialProperty(
              Cesium.Color.RED.withAlpha(0.3)
            ),
            outline: true,
            outlineColor: Cesium.Color.RED
          }
        })
        
        this.alarmEntities.push(entity)
      })
    },
    
    // 获取设施颜色
    getFacilityColor(status) {
      const colors = {
        normal: Cesium.Color.GREEN,
        warning: Cesium.Color.ORANGE,
        alarm: Cesium.Color.RED,
        offline: Cesium.Color.GRAY
      }
      return colors[status] || Cesium.Color.GRAY
    },
    
    // 获取设施图标
    getFacilityIcon(type) {
      const icons = {
        waterPlant: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
            <rect x="4" y="12" width="24" height="16" fill="#4CAF50" stroke="#ffffff" stroke-width="2"/>
            <rect x="8" y="8" width="4" height="8" fill="#2196F3"/>
            <rect x="14" y="6" width="4" height="10" fill="#2196F3"/>
            <rect x="20" y="8" width="4" height="8" fill="#2196F3"/>
          </svg>
        `),
        pumpStation: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="12" fill="#FF9800" stroke="#ffffff" stroke-width="2"/>
            <polygon points="12,12 20,16 12,20" fill="white"/>
          </svg>
        `),
        reservoir: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
            <ellipse cx="16" cy="20" rx="12" ry="8" fill="#2196F3" stroke="#ffffff" stroke-width="2"/>
            <ellipse cx="16" cy="18" rx="12" ry="8" fill="#4FC3F7"/>
          </svg>
        `),
        pipeline: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
            <rect x="4" y="12" width="24" height="8" fill="#9C27B0" stroke="#ffffff" stroke-width="2"/>
            <circle cx="8" cy="16" r="3" fill="#E1BEE7"/>
            <circle cx="24" cy="16" r="3" fill="#E1BEE7"/>
          </svg>
        `)
      }
      return icons[type] || icons.waterPlant
    },
    
    // 选择设施
    selectFacility(facility) {
      this.selectedFacility = facility
      
      // 飞行到选中的设施
      this.viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          facility.longitude,
          facility.latitude,
          1000
        ),
        duration: 1.5
      })
      
      // 初始化设施详情图表
      this.$nextTick(() => {
        this.initFacilityChart()
      })
    },
    
    // 关闭信息面板
    closeInfoPanel() {
      this.selectedFacility = null
    },
    
    // 初始化设施详情图表
    initFacilityChart() {
      if (!this.$refs.facilityChart) return
      
      this.facilityChart = echarts.init(this.$refs.facilityChart)
      
      // 模拟历史数据
      const hours = Array.from({ length: 24 }, (_, i) => i)
      const waterLevelData = hours.map(() => 
        this.selectedFacility.waterLevel + (Math.random() - 0.5) * 2
      )
      const flowRateData = hours.map(() => 
        this.selectedFacility.flowRate + (Math.random() - 0.5) * 100
      )
      
      const option = {
        title: {
          text: '24小时数据趋势',
          textStyle: { fontSize: 14 }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['水位', '流量'],
          bottom: 0
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '15%',
          top: '20%'
        },
        xAxis: {
          type: 'category',
          data: hours.map(h => `${h}:00`)
        },
        yAxis: [
          {
            type: 'value',
            name: '水位(m)',
            position: 'left'
          },
          {
            type: 'value',
            name: '流量(m³/h)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '水位',
            type: 'line',
            yAxisIndex: 0,
            data: waterLevelData,
            smooth: true,
            lineStyle: { color: '#2196F3' },
            areaStyle: { opacity: 0.3 }
          },
          {
            name: '流量',
            type: 'line',
            yAxisIndex: 1,
            data: flowRateData,
            smooth: true,
            lineStyle: { color: '#4CAF50' }
          }
        ]
      }
      
      this.facilityChart.setOption(option)
    },
    
    // 设施类型过滤
    filterFacilities() {
      this.renderFacilities()
    },
    
    // 切换告警显示
    toggleAlarms() {
      this.renderAlarms()
    },
    
    // 切换自动更新
    toggleAutoUpdate() {
      if (this.autoUpdate) {
        this.startAutoUpdate()
      } else {
        this.stopAutoUpdate()
      }
    },
    
    // 更新图层透明度
    updateLayerAlpha() {
      const alpha = this.layerAlpha / 100
      if (this.viewer.imageryLayers.length > 0) {
        this.viewer.imageryLayers.get(0).alpha = alpha
      }
    },
    
    // 开始自动更新
    startAutoUpdate() {
      this.updateTimer = setInterval(() => {
        this.refreshData()
      }, 30000) // 30秒更新一次
    },
    
    // 停止自动更新
    stopAutoUpdate() {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },
    
    // 刷新数据
    async refreshData() {
      try {
        // 重新加载数据
        this.facilities = await this.mockLoadFacilities()
        this.alarmPoints = await this.mockLoadAlarms()
        
        // 重新渲染
        this.renderFacilities()
        this.renderAlarms()
        
        // 更新统计
        this.updateStats()
        
        this.$message.success('数据已更新')
      } catch (error) {
        console.error('刷新数据失败:', error)
        this.$message.error('数据更新失败')
      }
    },
    
    // 更新统计数据
    updateStats() {
      this.stats.totalFacilities = this.facilities.length
      this.stats.normalCount = this.facilities.filter(f => f.status === 'normal').length
      this.stats.warningCount = this.facilities.filter(f => f.status === 'warning').length
      this.stats.offlineCount = this.facilities.filter(f => f.status === 'alarm').length
    },
    
    // 获取设施类型名称
    getFacilityTypeName(type) {
      const names = {
        waterPlant: '水厂',
        pumpStation: '泵站',
        reservoir: '水库',
        pipeline: '管网'
      }
      return names[type] || '未知'
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const types = {
        normal: 'success',
        warning: 'warning',
        alarm: 'danger',
        offline: 'info'
      }
      return types[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const texts = {
        normal: '正常',
        warning: '异常',
        alarm: '告警',
        offline: '离线'
      }
      return texts[status] || '未知'
    },
    
    // 格式化时间
    formatTime(timeString) {
      return new Date(timeString).toLocaleString('zh-CN')
    },
    
    // 清理资源
    cleanup() {
      this.stopAutoUpdate()
      
      if (this.facilityChart) {
        this.facilityChart.dispose()
        this.facilityChart = null
      }
      
      if (this.viewer) {
        this.viewer.destroy()
        this.viewer = null
      }
    }
  }
}
</script>

<style scoped>
.water-monitor-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.cesium-viewer {
  width: 100%;
  height: 100%;
}

.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 350px;
  z-index: 1000;
}

.panel-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-row {
  margin-bottom: 15px;
}

.control-label {
  font-size: 13px;
  color: #666;
  margin-right: 10px;
}

.stats-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  text-align: center;
  min-height: 80px;
}

.stats-card.normal {
  border-left: 4px solid #67C23A;
}

.stats-card.warning {
  border-left: 4px solid #E6A23C;
}

.stats-card.offline {
  border-left: 4px solid #F56C6C;
}

.stats-item {
  padding: 10px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stats-label {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.info-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 400px;
  z-index: 1000;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.facility-info {
  max-height: 400px;
  overflow-y: auto;
}

.facility-chart {
  margin-top: 15px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    width: 300px;
  }
  
  .stats-panel {
    position: static;
    margin: 20px;
  }
  
  .info-panel {
    width: 350px;
  }
}
</style>
```

这个智慧水务监控系统案例展示了如何将Cesium与Vue.js和ECharts完美集成，实现了：

1. **完整的组件化架构** - 模块化的Vue组件设计
2. **丰富的交互功能** - 点击选择、悬停高亮、图层控制
3. **实时数据更新** - 模拟实时数据推送和自动刷新
4. **数据可视化** - 结合ECharts实现图表展示
5. **响应式设计** - 适配不同屏幕尺寸
6. **性能优化** - 合理的资源管理和事件处理

## 使用方法

1. 将代码保存为Vue组件文件
2. 在项目中引入必要的依赖（Cesium、Element UI、ECharts）
3. 配置Cesium Ion访问令牌
4. 根据实际需求调整数据源和样式

这个案例可以作为实际水务监控项目的基础框架，根据具体业务需求进行扩展和定制。 