# DictTag 字典标签组件

字典标签组件，用于根据字典数据显示对应的标签，支持多种样式主题。

## 功能特性

- 🏷️ 支持单个值和多个值显示
- 🎨 支持多种标签样式主题（primary、success、warning、danger等）
- 🔧 支持自定义分隔符
- 📝 未匹配数据时可选择显示原值
- 🔄 支持数组和字符串格式的值

## 基础用法

### 单个值显示

```vue
<template>
  <div>
    <dict-tag :options="statusOptions" :value="1" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      statusOptions: [
        { value: '1', label: '正常', raw: { listClass: 'success' } },
        { value: '2', label: '停用', raw: { listClass: 'danger' } }
      ]
    }
  }
}
</script>
```

### 多个值显示

```vue
<template>
  <div>
    <!-- 数组格式 -->
    <dict-tag :options="typeOptions" :value="[1,2,3]" />
    
    <!-- 字符串格式 -->
    <dict-tag :options="statusOptions" value="1,2" separator="," />
  </div>
</template>

<script>
export default {
  data() {
    return {
      typeOptions: [
        { value: 1, label: '类型A', raw: { listClass: 'primary' } },
        { value: 2, label: '类型B', raw: { listClass: 'warning' } },
        { value: 3, label: '类型C', raw: { listClass: 'info' } }
      ],
      statusOptions: [
        { value: '1', label: '正常', raw: { listClass: 'success' } },
        { value: '2', label: '停用', raw: { listClass: 'danger' } }
      ]
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| options | 字典选项数据 | Array | — | null | 是 |
| value | 绑定值 | Number/String/Array | — | — | 是 |
| showValue | 当未找到匹配数据时，是否显示原值 | Boolean | true/false | true | 否 |
| separator | 多个值的分隔符 | String | — | ',' | 否 |

### options 数据格式

```typescript
interface DictOption {
  value: string | number;    // 选项值
  label: string;            // 显示文本
  raw: {                    // 原始数据
    listClass?: string;     // 标签样式类名：'default'|'primary'|'success'|'info'|'warning'|'danger'
    cssClass?: string;      // 自定义CSS类名
  }
}
```

### 样式主题

| 主题 | 说明 | 颜色 |
|------|------|------|
| default | 默认样式 | 灰色 |
| primary | 主要 | 蓝色 |
| success | 成功 | 绿色 |
| info | 信息 | 蓝色 |
| warning | 警告 | 橙色 |
| danger | 危险 | 红色 |

## 使用示例

### 状态标签

```vue
<template>
  <div>
    <h3>用户状态</h3>
    <dict-tag :options="userStatusOptions" :value="user.status" />
    
    <h3>订单状态</h3>
    <dict-tag :options="orderStatusOptions" :value="order.status" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      user: { status: '1' },
      order: { status: '3' },
      userStatusOptions: [
        { value: '0', label: '禁用', raw: { listClass: 'danger' } },
        { value: '1', label: '正常', raw: { listClass: 'success' } }
      ],
      orderStatusOptions: [
        { value: '1', label: '待付款', raw: { listClass: 'warning' } },
        { value: '2', label: '待发货', raw: { listClass: 'primary' } },
        { value: '3', label: '已发货', raw: { listClass: 'info' } },
        { value: '4', label: '已完成', raw: { listClass: 'success' } },
        { value: '5', label: '已取消', raw: { listClass: 'danger' } }
      ]
    }
  }
}
</script>
```

### 权限标签

```vue
<template>
  <div>
    <h3>用户权限</h3>
    <dict-tag 
      :options="permissionOptions" 
      :value="user.permissions" 
      :show-value="false"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      user: { 
        permissions: ['read', 'write', 'delete'] 
      },
      permissionOptions: [
        { value: 'read', label: '查看', raw: { listClass: 'info' } },
        { value: 'write', label: '编辑', raw: { listClass: 'warning' } },
        { value: 'delete', label: '删除', raw: { listClass: 'danger' } },
        { value: 'admin', label: '管理员', raw: { listClass: 'primary' } }
      ]
    }
  }
}
</script>
```

## 注意事项

1. **数据格式**：确保 options 数组中的每个对象都包含 `value`、`label` 和 `raw` 属性
2. **值类型匹配**：props 中的 value 类型应与 options 中的 value 类型保持一致
3. **性能考虑**：当选项数据很大时，建议在计算属性中处理数据格式转换
4. **样式自定义**：可以通过 `raw.cssClass` 属性添加自定义CSS类名

## 常见问题

### Q: 为什么标签不显示？

A: 请检查以下几点：
- options 数据格式是否正确
- value 值是否与 options 中的 value 匹配
- raw.listClass 是否设置正确

### Q: 如何自定义标签样式？

A: 可以通过两种方式：
1. 使用内置主题：设置 `raw.listClass` 为预定义值
2. 自定义样式：设置 `raw.cssClass` 并编写对应CSS

### Q: 支持动态更新数据吗？

A: 是的，组件支持响应式数据更新，当 options 或 value 变化时会自动重新渲染

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
        <span
          v-if="(item.raw.listClass == 'default' || item.raw.listClass == '') && (item.raw.cssClass == '' || item.raw.cssClass == null)"
          :key="item.value"
          :index="index"
          :class="item.raw.cssClass"
        >{{ item.label + ' ' }}</span
        >
        <el-tag
          v-else
          :disable-transitions="true"
          :key="item.value"
          :index="index"
          :type="item.raw.listClass == 'primary' ? '' : item.raw.listClass"
          :class="item.raw.cssClass"
        >
          {{ item.label + ' ' }}
        </el-tag>
      </template>
    </template>
    <template v-if="unmatch && showValue">
      {{ unmatchArray | handleArray }}
    </template>
  </div>
</template>

<script>
export default {
  name: "DictTag",
  props: {
    options: {
      type: Array,
      default: null,
    },
    value: [Number, String, Array],
    // 当未找到匹配的数据时，显示value
    showValue: {
      type: Boolean,
      default: true,
    },
    separator: {
      type: String,
      default: ","
    }
  },
  data() {
    return {
      unmatchArray: [], // 记录未匹配的项
    }
  },
  computed: {
    values() {
      if (this.value === null || typeof this.value === 'undefined' || this.value === '') return []
      return Array.isArray(this.value) ? this.value.map(item => '' + item) : String(this.value).split(this.separator)
    },
    unmatch() {
      this.unmatchArray = []
      // 没有value不显示
      if (this.value === null || typeof this.value === 'undefined' || this.value === '' || this.options.length === 0) return false
      // 传入值为数组
      let unmatch = false // 添加一个标志来判断是否有未匹配项
      this.values.forEach(item => {
        if (!this.options.some(v => v.value === item)) {
          this.unmatchArray.push(item)
          unmatch = true // 如果有未匹配项，将标志设置为true
        }
      })
      return unmatch // 返回标志的值
    },

  },
  filters: {
    handleArray(array) {
      if (array.length === 0) return '';
      return array.reduce((pre, cur) => {
        return pre + ' ' + cur;
      })
    },
  }
};
</script>
<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
```

</details>
