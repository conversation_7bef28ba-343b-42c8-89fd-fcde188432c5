<template>
	<div class="flex">
		<!--画布内容 -->
		<div class="main" ref="wrapRef">
			<canvas ref="canvas">
				<p>当前你的浏览器不支持canvas,请跟换或升级浏览器！</p>
			</canvas>
		</div>
		<!-- 工具栏 -->
		<div class="tool">
			<el-card>
				<div class="block">
					<el-select v-model="imgSrc" placeholder="请选择" @change="imageChange">
						<el-option
							v-for="item in options"
							:key="item.src"
							:label="item.label"
							:value="item.src"
						>
						</el-option>
					</el-select>
				</div>
				<div class="block">
					<h4>功能区</h4>
					<el-radio-group v-model="mouseMode" @input="changeMouse">
						<el-radio label="MOVE_MODE">移动</el-radio>
						<el-radio label="LINE_MODE">画笔</el-radio>
						<el-radio label="ERASER_MODE">橡皮檫</el-radio>
					</el-radio-group>
				</div>
				<div class="block">
					<h4>缩放大小</h4>
					<el-slider :min="0.1" :max="2" :step="0.1" v-model="canvasScale"></el-slider>
				</div>
				<div class="block">
					<h4>线条宽度</h4>

					<el-slider :min="1" :max="10" :step="1" v-model="lineWidth"></el-slider>
				</div>
				<div>
					<h4>画笔颜色</h4>
					<el-color-picker v-model="lineColor"></el-color-picker>
				</div>

				<div class="buttons block">
					<el-button
						size="mini"
						icon="el-icon-back"
						:disabled="canvasCurrentHistory === 1"
						@click="canvasCurrentHistory -= 1"
						>上一步</el-button
					>
					<el-button
						size="mini"
						icon="el-icon-right"
						:disabled="
							canvasCurrentHistory === canvasHistroyList.length &&
							canvasHistroyList.length > 0
						"
						@click="canvasCurrentHistory += 1"
						>下一步</el-button
					>
					<el-button
						size="mini"
						icon="el-icon-delete"
						:disabled="canvasHistroyList.length === 0"
						@click="clearCanvas"
						>清空</el-button
					>
				</div>
				<div class="block">
					<el-button size="mini" @click="download">保存图片</el-button>
				</div>
			</el-card>
		</div>
	</div>
</template>

<script>
export default {
	components: {},
	props: {},
	data() {
		return {
			mouseMode: "MOVE_MODE", // 画笔模式
			translatePointX: 0, //当前位移的距离
			translatePointY: 0,
			fillStartPointX: 0, // 上一次位移结束的位移距离
			fillStartPointY: 0,
			canvasScale: 1,
			lineColor: "#409EFF",
			imgSrc: "drawSrc",
			lineWidth: 2, //画笔的宽度
			canvasHistroyList: [], // 画笔的历史记录
			canvasCurrentHistory: 0,
			drawSrc: require("/src/assets/draw.jpg"),
			examSrc: require("/src/assets/examination.jpeg"),
			options: [
				{ label: "风景", src: "drawSrc" },
				{ label: "试卷", src: "examSrc" },
			],
		};
	},
	watch: {
		canvasScale: {
			handler(val) {
				const canvas = this.$refs.canvas;
				// 更新画布的css变化
				canvas.style.transform = `scale(${val},${val}) translate(${this.translatePointX}px,${this.translatePointY}px)`;
			},
		},
		lineColor: {
			handler(val) {
				const canvas = this.$refs.canvas;
				const context = canvas?.getContext("2d");
				context.strokeStyle = val;
				context.lineWidth = this.lineWidth;
			},
		},
		lineWidth: {
			handler(newVal) {
				const canvas = this.$refs.canvas;
				const context = canvas?.getContext("2d");
				context.strokeStyle = this.lineColor;
				context.lineWidth = newVal;
			},
		},
		canvasCurrentHistory: {
			handler(newVal) {
				const canvas = this.$refs.canvas;
				const context = canvas?.getContext("2d");
				if (!canvas || !context || newVal === 0) return;
				context?.putImageData(this.canvasHistroyList[newVal - 1], 0, 0);
			},
		},
	},
	methods: {
		//更改图片
		imageChange(val) {
			this.initAttr();
			this.initCanvas();
			this.handleCanvas();
		},

		clearCanvas() {
			this.canvasHistroyList = [this.canvasHistroyList[0]];
			this.canvasCurrentHistory = 1;
		},
		changeMouse(val) {
			const wrap = this.$refs.wrapRef;
			const canvas = this.$refs.canvas;
			if (!canvas || !wrap) return;
			switch (val) {
				case "MOVE_MODE":
					this.lineWidth = 2;
					canvas.style.cursor = "move";
					wrap.style.cursor = "move";

					break;
				case "LINE_MODE":
					this.lineWidth = 2;
					canvas.style.cursor = `pointer`;
					wrap.style.cursor = "default";
					break;
				case "ERASER_MODE":
					this.lineWidth = 9;
					canvas.style.cursor = `pointer`;
					wrap.style.cursor = "default";
					break;
				default:
					canvas.style.cursor = "default";
					wrap.style.cursor = "default";

					break;
			}
		},
		initAttr() {
			this.canvasHistroyList = [];
			this.canvasCurrentHistory = 0;
			this.mouseMode = "MOVE_MODE";
			this.canvasScale = 1;
			this.translatePointX = 0;
			this.translatePointY = 0;
			this.fillStartPointX = 0;
			this.fillStartPointY = 0;
		},
		// 初始化画布
		initCanvas() {
			const wrap = this.$refs.wrapRef;
			const canvas = this.$refs.canvas;
			canvas.style.cursor = "move";
			wrap.style.cursor = "move";
			const context = canvas?.getContext("2d");
			const img = new Image();
			img.src = this[this.imgSrc];
			img.onload = () => {
				canvas.width = img.width;
				canvas.height = img.height;
				// 背景设置为图片，橡皮擦的效果才能出来
				canvas.style.background = `url(${img.src})`;
				context.drawImage(img, 0, 0);
				context.strokeStyle = this.lineColor;
				context.lineWidth = this.lineWidth;
				context.lineJoin = "round"; //线的相连部分 圆形
				context.lineCap = "round"; //线段末端属性，圆形

				// context.fillStyle = "#999";
				// 		context.lineWidth = 7;
				// 		context.fillRect(100, 100, 500, 300);
				// 		context.font = "100px Microsoft YaHei";
				// 		context.textAlign = "center";
				// 		context.strokeText("刮一刮", 350, 300);

				this.initHistory(context, canvas);
				// 设置变化基点，为画布容器中央
				canvas.style.transformOrigin = `${wrap?.offsetWidth / 2}px ${
					wrap?.offsetHeight / 2
				}px`;
				// 清除上一次变化的效果
				canvas.style.transform = "";
			};
		},
		initHistory(context, canvas) {
			const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
			this.canvasHistroyList = [];
			this.canvasHistroyList.push(imageData);
			this.canvasCurrentHistory += 1;
		},

		//监听画布
		handleCanvas() {
			const canvas = this.$refs.canvas;
			const wrap = this.$refs.wrapRef;
			const context = canvas?.getContext("2d");
			if (!context || !wrap) return;
			// 清除上一次设置的监听，以防获取参数错误
			wrap.onmousedown = null;
			wrap.onmousedown = (event) => {
				console.log("event: ", event);
				const downX = event.pageX;
				const downY = event.pageY;
				// 区分我们现在选择的鼠标模式：移动、画笔、橡皮擦
				switch (this.mouseMode) {
					case "MOVE_MODE":
						this.handleMoveMode(downX, downY);
						break;
					case "LINE_MODE":
						this.handleLineMode(downX, downY);
						break;
					case "ERASER_MODE":
						this.handleEraserMode(downX, downY);
						break;
					default:
						break;
				}
			};
			wrap.onwheel = null;
			wrap.onwheel = (e) => {
				e.preventDefault();
				const { deltaY } = e;
				console.log("deltaY: ", deltaY);
				// 0.1来递增递减 ，小于0 鼠标往上滚动
				const newScale =
					deltaY > 0
						? (this.canvasScale * 10 - 0.1 * 10) / 10
						: (this.canvasScale * 10 + 0.1 * 10) / 10;
				if (newScale < 0.1 || newScale > 2) return;
				this.setCanvasScale(newScale);
			};
		},
		setCanvasScale(val) {
			this.canvasScale = val;
		},
		// 公式转换成画布上的坐标
		generateLinePoint(x, y) {
			const canvas = this.$refs.canvas;
			const wrap = this.$refs.wrapRef;
			const translatePointX = this.translatePointX;
			const translatePointY = this.translatePointY;
			const wrapWidth = wrap?.offsetWidth || 0;
			const wrapHeight = wrap?.offsetHeight || 0;
			// 缩放位移坐标变化规律
			// (transformOrigin - downX) / scale * (scale-1) + downX - translateX = pointX
			const pointX =
				((wrapWidth / 2 - x) / this.canvasScale) * (this.canvasScale - 1) +
				x -
				translatePointX;
			const pointY =
				((wrapHeight / 2 - y) / this.canvasScale) * (this.canvasScale - 1) +
				y -
				translatePointY;

			return {
				pointX,
				pointY,
			};
		},

		// 移动模式
		handleMoveMode(downX, downY) {
			const canvas = this.$refs.canvas;
			const wrap = this.$refs.wrapRef;
			const context = canvas?.getContext("2d");
			const fillStartPointX = this.fillStartPointX;
			const fillStartPointY = this.fillStartPointY;
			// 为容器添加移动事件，可以在空白处移动图片
			wrap.onmousemove = (event) => {
				const moveX = event.pageX;
				const moveY = event.pageY;
				// 更新现在的位移距离，值为：上一次位移结束的坐标+移动的距离
				this.translatePointX = fillStartPointX + (moveX - downX);
				this.translatePointY = fillStartPointY + (moveY - downY);
				// 更新画布的css变化
				canvas.style.transform = `scale(${this.canvasScale},${this.canvasScale}) translate(${this.translatePointX}px,${this.translatePointY}px)`;
			};

			wrap.onmouseup = (event) => {
				const upX = event.pageX;
				const upY = event.pageY;

				// 取消事件监听
				wrap.onmousemove = null;
				wrap.onmouseup = null;
				// 鼠标抬起时候，更新“上一次唯一结束的坐标”
				this.fillStartPointX = fillStartPointX + (upX - downX);
				this.fillStartPointY = fillStartPointY + (upY - downY);
			};
		},
		// 画笔
		handleLineMode(downX, downY) {
			const canvas = this.$refs.canvas;
			const wrap = this.$refs.wrapRef;
			const context = canvas?.getContext("2d");
			if (!canvas || !wrap || !context) return;

			const offsetLeft = canvas.offsetLeft;
			const offsetTop = canvas.offsetTop;
			// 减去画布偏移的距离（以画布为基准进行计算坐标）
			downX = downX - offsetLeft;
			downY = downY - offsetTop;

			const { pointX, pointY } = this.generateLinePoint(downX, downY);
			context.globalCompositeOperation = "source-over";
			context.beginPath();
			// 设置画笔起点
			context.moveTo(pointX, pointY);
			canvas.onmousemove = null;
			canvas.onmousemove = (event) => {
				const moveX = event.pageX - offsetLeft;
				const moveY = event.pageY - offsetTop;
				const { pointX, pointY } = this.generateLinePoint(moveX, moveY);
				// 开始绘制画笔线条~
				context.lineTo(pointX, pointY);
				context.stroke();
			};
			canvas.onmouseup = () => {
				//记录状态
				const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
				// 如果此时处于撤销状态，此时再使用画笔，则将之后的状态清空，以刚画的作为最新的画布状态
				if (this.canvasCurrentHistory < this.canvasHistroyList.length) {
					this.canvasHistroyList = this.canvasHistroyList.slice(
						0,
						this.canvasCurrentHistory
					);
				}
				this.canvasHistroyList.push(imageData);
				this.canvasCurrentHistory += 1;

				context.closePath();
				canvas.onmousemove = null;
				canvas.onmouseup = null;
			};
		},
		// 橡皮檫
		handleEraserMode(downX, downY) {
			const canvas = this.$refs.canvas;
			const wrap = this.$refs.wrapRef;
			const context = canvas?.getContext("2d");
			if (!canvas || !wrap || !context) return;

			const offsetLeft = canvas.offsetLeft;
			const offsetTop = canvas.offsetTop;
			downX = downX - offsetLeft;
			downY = downY - offsetTop;

			const { pointX, pointY } = this.generateLinePoint(downX, downY);

			context.beginPath();
			context.moveTo(pointX, pointY);

			canvas.onmousemove = null;
			canvas.onmousemove = (event) => {
				const moveX = event.pageX - offsetLeft;
				const moveY = event.pageY - offsetTop;
				const { pointX, pointY } = this.generateLinePoint(moveX, moveY);
				context.globalCompositeOperation = "destination-out";
				context.lineWidth = this.lineWidth;
				context.lineTo(pointX, pointY);
				context.stroke();
			};
			canvas.onmouseup = () => {
				//记录状态
				const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

				// 如果此时处于撤销状态，此时再使用画笔，则将之后的状态清空，以刚画的作为最新的画布状态
				if (this.canvasCurrentHistory < this.canvasHistroyList.length) {
					this.canvasHistroyList = this.canvasHistroyList.slice(
						0,
						this.canvasCurrentHistory
					);
				}
				this.canvasHistroyList.push(imageData);
				this.canvasCurrentHistory += 1;
				context.closePath();
				canvas.onmousemove = null;
				canvas.onmouseup = null;
			};
		},
		//下载
		download() {
			const canvas = this.$refs.canvas;
			const el = document.createElement("a");
			el.href = canvas.toDataURL();
			el.download = "绘制图片";
			// 创建一个点击事件并对 a 标签进行触发
			const event = new MouseEvent("click");
			el.dispatchEvent(event);
		},
	},
	mounted() {
		this.initCanvas();
		this.handleCanvas();
	},
};
</script>
<style scoped lang="less">
.flex {
	display: flex;
	justify-content: center;
	overflow: hidden;
	padding: 10px;
	.tool {
		width: 300px;
		margin: 0 20px;
	}
	.buttons {
		display: flex;
	}
	.block {
		margin-bottom: 5px;
	}
}
.main {
	flex: 1;
	border: 1px solid #000;
	//background: #3d6;
	overflow: hidden;
}
</style>
<!-- 
    刮刮乐
		context.fillStyle = "#999";
				context.lineWidth = 7;
				context.fillRect(100, 100, 500, 300);
				context.font = "100px Microsoft YaHei";
				context.textAlign = "center";
				context.strokeText("刮一刮", 350, 300);

 -->
