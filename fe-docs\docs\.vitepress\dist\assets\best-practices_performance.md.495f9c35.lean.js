import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"性能优化","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/performance.md","filePath":"best-practices/performance.md"}'),p={name:"best-practices/performance.md"},o=l("",58),e=[o];function t(c,r,D,y,F,i){return a(),n("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
