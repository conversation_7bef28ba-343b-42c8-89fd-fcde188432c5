# 路由管理

本文档提供Vue Router的最佳实践和配置技巧，帮助开发者构建清晰、高效的路由系统。

## 路由基础配置

### 路由模式选择

Vue Router提供了三种路由模式：

```js
import Vue from 'vue'
import VueRouter from 'vue-router'
import routes from './routes'

Vue.use(VueRouter)

const router = new VueRouter({
  // 1. Hash模式（默认）- 使用URL hash实现
  // mode: 'hash',
  
  // 2. History模式 - 利用HTML5 History API
  mode: 'history',
  
  // 3. Abstract模式 - 适用于非浏览器环境
  // mode: 'abstract',
  
  routes,
  // 滚动行为控制
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

export default router
```

**最佳实践建议**：
- 在生产环境中推荐使用`history`模式，提供更友好的URL
- 使用`history`模式时，确保服务器配置正确的回退路由
- 在开发环境中，可以使用`hash`模式以简化配置

### 路由配置分离

对于中大型应用，推荐将路由配置拆分为多个文件：

```js
// routes/index.js
import userRoutes from './user-routes'
import productRoutes from './product-routes'
import dashboardRoutes from './dashboard-routes'

export default [
  {
    path: '/',
    component: () => import('@/views/Home.vue')
  },
  ...userRoutes,
  ...productRoutes,
  ...dashboardRoutes,
  {
    path: '*',
    component: () => import('@/views/NotFound.vue')
  }
]

// routes/user-routes.js
export default [
  {
    path: '/users',
    component: () => import('@/views/users/UserList.vue')
  },
  {
    path: '/users/:id',
    component: () => import('@/views/users/UserDetail.vue')
  }
]
```

## 路由懒加载

### 命名分块

使用webpack的动态导入功能实现路由懒加载，并用注释控制分块名称：

```js
const routes = [
  {
    path: '/dashboard',
    component: () => import(/* webpackChunkName: "dashboard" */ '@/views/Dashboard.vue'),
    children: [
      {
        path: 'overview',
        component: () => import(/* webpackChunkName: "dashboard" */ '@/views/dashboard/Overview.vue')
      },
      {
        path: 'stats',
        component: () => import(/* webpackChunkName: "dashboard" */ '@/views/dashboard/Stats.vue')
      }
    ]
  },
  {
    path: '/settings',
    component: () => import(/* webpackChunkName: "settings" */ '@/views/Settings.vue')
  }
]
```

**提示**：将相关路由分组到相同的块中，可以减少网络请求次数。

### 预加载策略

实现智能预加载策略，提高用户体验：

```js
import router from './router'

// 鼠标悬停预加载
const preloadOnHover = (event) => {
  const link = event.target.closest('a')
  if (link && link.dataset.preload) {
    const route = router.resolve(link.getAttribute('href'))
    if (route) {
      router.getMatchedComponents(route).forEach(component => {
        if (typeof component === 'function') {
          component()
        }
      })
    }
  }
}

document.addEventListener('mouseover', preloadOnHover)
```

## 路由元信息

### 权限控制

使用meta字段存储路由元信息，实现权限控制：

```js
const routes = [
  {
    path: '/admin',
    component: AdminLayout,
    meta: { 
      requiresAuth: true,
      roles: ['admin']
    },
    children: [
      {
        path: 'users',
        component: () => import('@/views/admin/Users.vue'),
        meta: { roles: ['admin', 'manager'] }
      },
      {
        path: 'settings',
        component: () => import('@/views/admin/Settings.vue'),
        meta: { roles: ['admin'] }
      }
    ]
  }
]

// 全局前置守卫中检查权限
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters['auth/isAuthenticated']
  const userRoles = store.getters['auth/roles']
  
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isAuthenticated) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 检查用户角色权限
      const hasPermission = to.matched.every(record => {
        if (record.meta.roles) {
          return userRoles.some(role => record.meta.roles.includes(role))
        }
        return true
      })
      
      if (hasPermission) {
        next()
      } else {
        next({ path: '/forbidden' })
      }
    }
  } else {
    next()
  }
})
```

### 页面标题和元数据

利用meta字段设置页面标题和其他元数据：

```js
const routes = [
  {
    path: '/products/:id',
    component: ProductDetail,
    meta: {
      title: '产品详情',
      description: '查看产品的详细信息',
      dynamicTitle: route => `产品: ${route.params.id}`
    }
  }
]

// 设置文档标题
router.afterEach((to) => {
  let title = to.meta.title || '默认标题'
  
  // 处理动态标题
  if (to.meta.dynamicTitle && typeof to.meta.dynamicTitle === 'function') {
    title = to.meta.dynamicTitle(to)
  }
  
  document.title = `${title} | 应用名称`
  
  // 更新元描述
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', to.meta.description)
    }
  }
})
```

## 导航守卫

### 全局守卫组织

对于复杂应用，将守卫逻辑分离到独立模块：

```js
// guards/auth.js
export default function authGuard(to, from, next) {
  // 身份验证逻辑
  if (requiresAuth(to) && !isAuthenticated()) {
    next('/login')
  } else {
    next()
  }
}

// guards/permission.js
export default function permissionGuard(to, from, next) {
  // 权限检查逻辑
  if (!hasPermission(to)) {
    next('/forbidden')
  } else {
    next()
  }
}

// router/index.js
import authGuard from './guards/auth'
import permissionGuard from './guards/permission'

const router = new VueRouter({ /* 配置 */ })

router.beforeEach(authGuard)
router.beforeEach(permissionGuard)

export default router
```

### 组件内守卫最佳实践

在组件内使用路由守卫：

```js
export default {
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      item: null,
      loading: false,
      error: null
    }
  },
  // 路由进入前
  beforeRouteEnter(to, from, next) {
    // 在渲染该组件的对应路由被验证前调用
    // 不能获取组件实例 `this`
    next(vm => {
      // 通过 `vm` 访问组件实例
      vm.fetchData(to.params.id)
    })
  },
  // 路由更新时（参数变化）
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但是该组件被复用时调用
    this.item = null
    this.fetchData(to.params.id)
    next()
  },
  // 路由离开前
  beforeRouteLeave(to, from, next) {
    // 导航离开该组件的对应路由时调用
    if (this.hasUnsavedChanges) {
      const answer = window.confirm('有未保存的更改，确定要离开吗？')
      if (answer) {
        next()
      } else {
        next(false)
      }
    } else {
      next()
    }
  },
  methods: {
    fetchData(id) {
      this.loading = true
      this.$api.getItem(id)
        .then(response => {
          this.item = response.data
          this.loading = false
        })
        .catch(error => {
          this.error = error
          this.loading = false
        })
    }
  }
}
```

## 路由模式最佳实践

### 命名路由与命名视图

使用命名路由和命名视图提高可维护性：

```js
// 命名路由
const routes = [
  {
    path: '/user/:id',
    name: 'user-profile',
    component: UserProfile
  }
]

// 在代码中使用
this.$router.push({ name: 'user-profile', params: { id: 123 }})

// 在模板中使用
<router-link :to="{ name: 'user-profile', params: { id: 123 }}">用户资料</router-link>

// 命名视图
const routes = [
  {
    path: '/settings',
    component: SettingsLayout,
    children: [
      {
        path: 'profile',
        components: {
          default: UserSettings,
          sidebar: SettingsSidebar,
          header: SettingsHeader
        }
      }
    ]
  }
]

// 模板中使用命名视图
<router-view name="header"></router-view>
<div class="content">
  <router-view name="sidebar"></router-view>
  <router-view></router-view>
</div>
```

### 路由过渡效果

结合Vue的过渡系统，为路由切换添加动画效果：

```vue
<template>
  <div class="app">
    <transition name="fade" mode="out-in">
      <router-view></router-view>
    </transition>
  </div>
</template>

<style>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>
```

对于不同路由使用不同过渡效果：

```vue
<template>
  <div class="app">
    <transition :name="transitionName" mode="out-in">
      <router-view></router-view>
    </transition>
  </div>
</template>

<script>
export default {
  data() {
    return {
      transitionName: 'fade'
    }
  },
  watch: {
    '$route'(to, from) {
      // 根据路由深度确定过渡方向
      const toDepth = to.path.split('/').length
      const fromDepth = from.path.split('/').length
      this.transitionName = toDepth < fromDepth ? 'slide-right' : 'slide-left'
      
      // 或根据路由名称设置过渡
      if (to.name === 'home') {
        this.transitionName = 'zoom'
      }
    }
  }
}
</script>

<style>
.slide-left-enter-active, 
.slide-left-leave-active, 
.slide-right-enter-active, 
.slide-right-leave-active {
  transition: transform 0.5s;
}

.slide-left-enter, .slide-right-leave-to {
  transform: translateX(100%);
}

.slide-left-leave-to, .slide-right-enter {
  transform: translateX(-100%);
}

.zoom-enter-active, .zoom-leave-active {
  transition: all 0.4s;
}

.zoom-enter, .zoom-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>
```

## 高级技巧

### 动态路由

在运行时添加或删除路由：

```js
// 动态添加路由
router.addRoutes([
  {
    path: '/dynamic',
    component: () => import('@/views/Dynamic.vue')
  }
])

// 更新路由（Vue Router 4.x）
router.addRoute({
  path: '/new-route',
  component: NewRoute
})

// 删除路由（Vue Router 4.x）
const removeRoute = router.addRoute(newRoute)
removeRoute() // 删除路由
```

### 数据预取

实现路由数据预取，提高用户体验：

```js
// 在路由组件中定义数据预取钩子
const Home = {
  data() {
    return {
      posts: null
    }
  },
  // 路由组件上定义数据预取钩子
  asyncData({ store, route }) {
    // 返回 Promise
    return store.dispatch('fetchPosts')
  },
  created() {
    // 组件创建时，如果服务器端已预取数据，则直接使用
    if (this.$store.state.posts) {
      this.posts = this.$store.state.posts
    } else {
      // 客户端再次请求
      this.$store.dispatch('fetchPosts')
        .then(() => {
          this.posts = this.$store.state.posts
        })
    }
  }
}

// 路由配置
const routes = [
  {
    path: '/',
    component: Home,
    // 标记需要预取数据的路由
    meta: { fetchData: true }
  }
]

// 全局解析守卫中处理数据预取
router.beforeResolve((to, from, next) => {
  const matched = router.getMatchedComponents(to)
  const prevMatched = router.getMatchedComponents(from)
  
  // 找出需要预取数据的组件
  const activated = matched.filter((component, i) => {
    return component !== prevMatched[i] && 
      to.matched[i].meta.fetchData
  })
  
  if (!activated.length) {
    return next()
  }
  
  // 展示加载指示器
  store.commit('SET_LOADING', true)
  
  // 执行所有预取钩子
  Promise.all(activated.map(component => {
    if (component.asyncData) {
      return component.asyncData({ store, route: to })
    }
  }))
  .then(() => {
    // 隐藏加载指示器
    store.commit('SET_LOADING', false)
    next()
  })
  .catch(next)
})
```

### 路由分析与跟踪

集成分析工具，跟踪路由行为：

```js
router.afterEach((to, from) => {
  // Google Analytics
  if (window.gtag) {
    gtag('config', 'UA-XXXXX-Y', {
      'page_path': to.fullPath
    })
  }
  
  // 或自定义分析逻辑
  logRouteChange({
    from: from.fullPath,
    to: to.fullPath,
    timeStamp: Date.now()
  })
})
```

## 最佳实践总结

1. **路由组织**
   - 将路由配置分割成模块
   - 使用命名路由和路由别名
   - 保持路由结构与视图层次一致

2. **性能优化**
   - 实现路由懒加载
   - 使用命名分块控制代码分割
   - 考虑预加载关键路由

3. **权限管理**
   - 使用meta字段存储权限信息
   - 在全局守卫中统一验证权限
   - 将复杂的权限逻辑抽象到独立模块

4. **用户体验**
   - 添加路由过渡动画
   - 实现数据预取
   - 维护合适的滚动行为

5. **维护性**
   - 标准化路由命名
   - 利用路由元信息增强可维护性

遵循以上最佳实践，可以构建高效、可维护的Vue路由系统，为用户提供流畅的导航体验。 