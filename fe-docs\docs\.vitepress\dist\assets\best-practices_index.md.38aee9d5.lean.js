import{_ as e,o as a,c as t,V as i}from"./chunks/framework.3d729ebc.js";const u=JSON.parse('{"title":"最佳实践概述","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/index.md","filePath":"best-practices/index.md"}'),r={name:"best-practices/index.md"},l=i("",8),s=[l];function c(o,n,h,p,d,m){return a(),t("div",null,s)}const f=e(r,[["render",c]]);export{u as __pageData,f as default};
