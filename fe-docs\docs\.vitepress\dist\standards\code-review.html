<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>代码审查 | 前端技术开发文档</title>
    <meta name="description" content="A VitePress site">
    <link rel="preload stylesheet" href="/assets/style.5ccb9172.css" as="style">
    <script type="module" src="/assets/app.473eac78.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.2ed14f66.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="modulepreload" href="/assets/chunks/framework.3d729ebc.js">
  <link rel="modulepreload" href="/assets/chunks/theme.533e1ce4.js">
  <link rel="modulepreload" href="/assets/standards_code-review.md.04c78cff.lean.js">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="apple-touch-icon" href="/logo.png">
  <meta name="theme-color" content="#0ea5e9">
  <script id="check-dark-light">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-21678b25 data-v-b2cf3e0b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-c8616af1></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-c8616af1> Skip to content </a><!--]--><!----><header class="VPNav" data-v-b2cf3e0b data-v-7e5bc4a5><div class="VPNavBar has-sidebar" data-v-7e5bc4a5 data-v-94c81dcc><div class="container" data-v-94c81dcc><div class="title" data-v-94c81dcc><div class="VPNavBarTitle has-sidebar" data-v-94c81dcc data-v-f4ef19a3><a class="title" href="/" data-v-f4ef19a3><!--[--><!--]--><!--[--><img class="VPImage logo" src="/logo.jpeg" alt data-v-6db2186b><!--]--><!--[-->前端技术开发文档<!--]--><!--[--><!--]--></a></div></div><div class="content" data-v-94c81dcc><div class="curtain" data-v-94c81dcc></div><div class="content-body" data-v-94c81dcc><!--[--><!--]--><div class="VPNavBarSearch search" style="--vp-meta-key:&#39;Meta&#39;;" data-v-94c81dcc><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><svg class="DocSearch-Search-Icon" width="20" height="20" viewBox="0 0 20 20" aria-label="search icon"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-94c81dcc data-v-7f418b0f><span id="main-nav-aria-label" class="visually-hidden" data-v-7f418b0f>Main Navigation</span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->首页<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/guide/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->指南<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/components/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->组件<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/best-practices/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->最佳实践<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/standards/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->规范标准<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/tools/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->工具配置<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/cesium/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->Cesium<!--]--><!----></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-94c81dcc data-v-f6a63727><label title="toggle dark mode" data-v-f6a63727 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div><!----><div class="VPFlyout VPNavBarExtra extra" data-v-94c81dcc data-v-40855f84 data-v-764effdf><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-764effdf><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="icon" data-v-764effdf><circle cx="12" cy="12" r="2"></circle><circle cx="19" cy="12" r="2"></circle><circle cx="5" cy="12" r="2"></circle></svg></button><div class="menu" data-v-764effdf><div class="VPMenu" data-v-764effdf data-v-e7ea1737><!----><!--[--><!--[--><!----><div class="group" data-v-40855f84><div class="item appearance" data-v-40855f84><p class="label" data-v-40855f84>Appearance</p><div class="appearance-action" data-v-40855f84><label title="toggle dark mode" data-v-40855f84 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div></div></div><!----><!--]--><!--]--></div></div></div><!--[--><!--[--><!--[--><div class="nav-color-picker" data-v-21678b25><div class="color-picker" data-v-21678b25 data-v-917787cc><button class="color-picker-trigger" title="打开色彩选择器" data-v-917787cc><div class="palette-icon" data-v-917787cc><div class="palette-circle" data-v-917787cc></div><div class="palette-colors" data-v-917787cc><div class="color-dot color-1" data-v-917787cc></div><div class="color-dot color-2" data-v-917787cc></div><div class="color-dot color-3" data-v-917787cc></div><div class="color-dot color-4" data-v-917787cc></div></div></div></button><!----><!----></div></div><!--]--><!--]--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-94c81dcc data-v-e5dd9c1c><span class="container" data-v-e5dd9c1c><span class="top" data-v-e5dd9c1c></span><span class="middle" data-v-e5dd9c1c></span><span class="bottom" data-v-e5dd9c1c></span></span></button></div></div></div></div><!----></header><div class="VPLocalNav" data-v-b2cf3e0b data-v-392e1bf8><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-392e1bf8><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="menu-icon" data-v-392e1bf8><path d="M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"></path><path d="M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"></path><path d="M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"></path><path d="M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"></path></svg><span class="menu-text" data-v-392e1bf8>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-392e1bf8 data-v-079b16a8><button data-v-079b16a8>Return to top</button><!----></div></div><aside class="VPSidebar" data-v-b2cf3e0b data-v-af16598e><div class="curtain" data-v-af16598e></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-af16598e><span class="visually-hidden" id="sidebar-aria-label" data-v-af16598e> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 has-active" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>规范标准</h2><!----></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>规范概述</p><!--]--><!----></a><!----></div><!----></div><section class="VPSidebarItem level-1 collapsible" data-v-c4656e6d data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h3 class="text" data-v-c4656e6d>编码规范</h3><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-c4656e6d><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="caret-icon" data-v-c4656e6d><path d="M9,19c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l5.3-5.3L8.3,6.7c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l6,6c0.4,0.4,0.4,1,0,1.4l-6,6C9.5,18.9,9.3,19,9,19z"></path></svg></div></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-2 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/js-standard.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>JavaScript规范</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/css-standard.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>CSS规范</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/html-standard.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>HTML规范</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-2 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/vue-standard.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Vue开发规范</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/git-commit.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Git提交规范</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/git-workflow.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Git工作流</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link is-active has-active" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/code-review.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>代码审查</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/standards/documentation.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>文档规范</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-b2cf3e0b data-v-a494bd1d><div class="VPDoc has-sidebar has-aside" data-v-a494bd1d data-v-c4b0d3cf><!--[--><!--]--><div class="container" data-v-c4b0d3cf><div class="aside" data-v-c4b0d3cf><div class="aside-curtain" data-v-c4b0d3cf></div><div class="aside-container" data-v-c4b0d3cf><div class="aside-content" data-v-c4b0d3cf><div class="VPDocAside" data-v-c4b0d3cf data-v-3f215769><!--[--><!--]--><!--[--><!--]--><div class="VPDocAsideOutline" data-v-3f215769 data-v-ff0f39c8><div class="content" data-v-ff0f39c8><div class="outline-marker" data-v-ff0f39c8></div><div class="outline-title" data-v-ff0f39c8>On this page</div><nav aria-labelledby="doc-outline-aria-label" data-v-ff0f39c8><span class="visually-hidden" id="doc-outline-aria-label" data-v-ff0f39c8> Table of Contents for current page </span><ul class="root" data-v-ff0f39c8 data-v-9a431c33><!--[--><!--]--></ul></nav></div></div><!--[--><!--]--><div class="spacer" data-v-3f215769></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-c4b0d3cf><div class="content-container" data-v-c4b0d3cf><!--[--><!--]--><!----><main class="main" data-v-c4b0d3cf><div style="position:relative;" class="vp-doc _standards_code-review" data-v-c4b0d3cf><div><h1 id="代码审查" tabindex="-1">代码审查 <a class="header-anchor" href="#代码审查" aria-label="Permalink to &quot;代码审查&quot;">​</a></h1><p>本文档定义了项目中代码审查的流程、标准和最佳实践，旨在提高代码质量，促进知识共享，确保代码库的健康发展。</p><h2 id="代码审查目标" tabindex="-1">代码审查目标 <a class="header-anchor" href="#代码审查目标" aria-label="Permalink to &quot;代码审查目标&quot;">​</a></h2><p>代码审查是软件开发过程中的关键环节，主要目标包括：</p><ol><li><strong>提高代码质量</strong>：发现并修复潜在问题，确保代码符合项目标准</li><li><strong>知识共享</strong>：促进团队成员间的技术交流和知识传递</li><li><strong>保持一致性</strong>：确保整个代码库风格和实现方式的一致性</li><li><strong>防止技术债务</strong>：及早发现和解决可能导致技术债务的问题</li><li><strong>培养团队文化</strong>：建立相互学习、共同提高的团队文化</li></ol><h2 id="审查流程" tabindex="-1">审查流程 <a class="header-anchor" href="#审查流程" aria-label="Permalink to &quot;审查流程&quot;">​</a></h2><h3 id="_1-创建pull-request" tabindex="-1">1. 创建Pull Request <a class="header-anchor" href="#_1-创建pull-request" aria-label="Permalink to &quot;1. 创建Pull Request&quot;">​</a></h3><ul><li>在功能分支上完成开发后，创建Pull Request（PR）到目标分支</li><li>填写详细的PR描述，包括： <ul><li>修改目的和内容</li><li>影响范围</li><li>可能的风险点</li><li>需要关注的代码部分</li></ul></li></ul><h3 id="_2-分配审查者" tabindex="-1">2. 分配审查者 <a class="header-anchor" href="#_2-分配审查者" aria-label="Permalink to &quot;2. 分配审查者&quot;">​</a></h3><ul><li>至少分配一名审查者</li><li>对于核心功能或复杂逻辑，建议分配多名审查者</li><li>审查者应当是对相关代码有深入了解的团队成员</li></ul><h3 id="_3-进行审查" tabindex="-1">3. 进行审查 <a class="header-anchor" href="#_3-进行审查" aria-label="Permalink to &quot;3. 进行审查&quot;">​</a></h3><ul><li>仔细阅读代码变更</li><li>关注代码质量、逻辑正确性、性能问题</li><li>提供建设性的反馈和建议</li><li>对于不确定的地方，主动提出疑问</li></ul><h3 id="_4-响应反馈" tabindex="-1">4. 响应反馈 <a class="header-anchor" href="#_4-响应反馈" aria-label="Permalink to &quot;4. 响应反馈&quot;">​</a></h3><ul><li>开发者根据审查意见进行修改</li><li>对每条意见进行回应（接受、解释或讨论）</li><li>推送更新后的代码</li><li>重复以上过程，直至代码符合要求</li></ul><h3 id="_5-合并代码" tabindex="-1">5. 合并代码 <a class="header-anchor" href="#_5-合并代码" aria-label="Permalink to &quot;5. 合并代码&quot;">​</a></h3><ul><li>当至少一名审查者批准后，代码可以被合并</li><li>对于核心模块或重大变更，需要至少两名审查者批准</li><li>合并前确保所有自动化检查通过</li><li>使用&quot;Squash and merge&quot;策略，保持提交历史清晰</li></ul><h2 id="审查重点" tabindex="-1">审查重点 <a class="header-anchor" href="#审查重点" aria-label="Permalink to &quot;审查重点&quot;">​</a></h2><h3 id="_1-功能性" tabindex="-1">1. 功能性 <a class="header-anchor" href="#_1-功能性" aria-label="Permalink to &quot;1. 功能性&quot;">​</a></h3><ul><li>代码是否正确实现了需求</li><li>是否考虑了边缘情况和异常处理</li><li>是否有潜在的性能问题</li><li>是否有安全漏洞</li></ul><h3 id="_2-代码质量" tabindex="-1">2. 代码质量 <a class="header-anchor" href="#_2-代码质量" aria-label="Permalink to &quot;2. 代码质量&quot;">​</a></h3><ul><li>代码是否简洁、清晰、易于理解</li><li>是否遵循DRY（Don&#39;t Repeat Yourself）原则</li><li>命名是否准确、一致且有意义</li><li>注释是否充分且有价值</li></ul><h3 id="_3-架构设计" tabindex="-1">3. 架构设计 <a class="header-anchor" href="#_3-架构设计" aria-label="Permalink to &quot;3. 架构设计&quot;">​</a></h3><ul><li>组件划分是否合理</li><li>是否符合项目的架构设计原则</li><li>是否考虑了可扩展性和可维护性</li><li>依赖关系是否清晰且合理</li></ul><h3 id="_4-文档" tabindex="-1">4. 文档 <a class="header-anchor" href="#_4-文档" aria-label="Permalink to &quot;4. 文档&quot;">​</a></h3><ul><li>是否更新了相关文档</li><li>API文档是否完整</li><li>复杂逻辑是否有必要的注释说明</li></ul><h2 id="vue-js项目特定检查点" tabindex="-1">Vue.js项目特定检查点 <a class="header-anchor" href="#vue-js项目特定检查点" aria-label="Permalink to &quot;Vue.js项目特定检查点&quot;">​</a></h2><h3 id="组件设计" tabindex="-1">组件设计 <a class="header-anchor" href="#组件设计" aria-label="Permalink to &quot;组件设计&quot;">​</a></h3><ul><li>组件职责是否单一</li><li>是否正确使用Props和Events</li><li>是否避免直接操作DOM</li><li>组件命名是否符合规范（PascalCase）</li></ul><h3 id="状态管理" tabindex="-1">状态管理 <a class="header-anchor" href="#状态管理" aria-label="Permalink to &quot;状态管理&quot;">​</a></h3><ul><li>是否合理使用Vuex/Pinia</li><li>状态更新逻辑是否清晰</li><li>是否避免不必要的状态共享</li></ul><h3 id="性能优化" tabindex="-1">性能优化 <a class="header-anchor" href="#性能优化" aria-label="Permalink to &quot;性能优化&quot;">​</a></h3><ul><li>是否使用了<code>v-for</code>的<code>key</code>属性</li><li>是否避免不必要的计算属性</li><li>大型列表是否考虑虚拟滚动</li><li>是否合理使用懒加载</li></ul><h3 id="路由管理" tabindex="-1">路由管理 <a class="header-anchor" href="#路由管理" aria-label="Permalink to &quot;路由管理&quot;">​</a></h3><ul><li>路由配置是否合理</li><li>是否实现了必要的路由守卫</li><li>路由参数是否得到正确处理</li></ul><h3 id="样式管理" tabindex="-1">样式管理 <a class="header-anchor" href="#样式管理" aria-label="Permalink to &quot;样式管理&quot;">​</a></h3><ul><li>是否使用了适当的CSS预处理器</li><li>样式作用域是否正确设置</li><li>是否避免全局样式污染</li><li>是否遵循项目的样式命名规范</li></ul><h2 id="代码审查礼仪" tabindex="-1">代码审查礼仪 <a class="header-anchor" href="#代码审查礼仪" aria-label="Permalink to &quot;代码审查礼仪&quot;">​</a></h2><h3 id="对于审查者" tabindex="-1">对于审查者 <a class="header-anchor" href="#对于审查者" aria-label="Permalink to &quot;对于审查者&quot;">​</a></h3><ol><li><p><strong>保持尊重和建设性</strong>：</p><ul><li>关注代码，而非开发者个人</li><li>提供具体、建设性的反馈</li><li>解释为什么需要改变，而不只是指出问题</li></ul></li><li><p><strong>设定优先级</strong>：</p><ul><li>区分必须修改的问题和建议性改进</li><li>不要过于纠结于代码风格等次要问题</li></ul></li><li><p><strong>及时响应</strong>：</p><ul><li>尽快进行审查，避免阻塞开发进度</li><li>如无法及时审查，应通知开发者并推荐其他审查者</li></ul></li><li><p><strong>教学相长</strong>：</p><ul><li>分享知识和最佳实践</li><li>推荐有用的资源和文档</li><li>承认并学习新的技术和方法</li></ul></li></ol><h3 id="对于开发者" tabindex="-1">对于开发者 <a class="header-anchor" href="#对于开发者" aria-label="Permalink to &quot;对于开发者&quot;">​</a></h3><ol><li><p><strong>保持开放心态</strong>：</p><ul><li>欢迎反馈，视为学习机会</li><li>不要将批评个人化</li><li>愿意讨论和解释设计决策</li></ul></li><li><p><strong>提交合适大小的PR</strong>：</p><ul><li>避免超大型PR，理想大小为200-400行代码变更</li><li>如需大量变更，考虑拆分为多个相关PR</li></ul></li><li><p><strong>积极响应反馈</strong>：</p><ul><li>及时处理审查意见</li><li>对每条评论做出回应</li><li>解释为什么接受或不接受某项建议</li></ul></li><li><p><strong>自我改进</strong>：</p><ul><li>从审查中学习，避免重复同样的错误</li><li>主动询问如何改进代码</li></ul></li></ol><h2 id="工具支持" tabindex="-1">工具支持 <a class="header-anchor" href="#工具支持" aria-label="Permalink to &quot;工具支持&quot;">​</a></h2><p>为提高代码审查效率，我们使用以下工具：</p><ol><li><p><strong>自动化检查</strong>：</p><ul><li>ESLint/StyleLint：代码风格和质量检查</li><li>SonarQube：代码质量分析</li></ul></li><li><p><strong>PR模板</strong>：</p><ul><li>在<code>.github/PULL_REQUEST_TEMPLATE.md</code>中定义标准PR模板</li></ul></li><li><p><strong>代码审查清单</strong>：</p><ul><li>提供标准化的审查清单，确保覆盖所有重要方面</li></ul></li></ol><h2 id="代码审查清单" tabindex="-1">代码审查清单 <a class="header-anchor" href="#代码审查清单" aria-label="Permalink to &quot;代码审查清单&quot;">​</a></h2><p>以下是代码审查时可参考的基本清单：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">### 功能性</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 代码完全实现了需求</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 处理了边缘情况和异常</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 没有明显的性能问题</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 没有安全漏洞</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">### 代码质量</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 代码简洁清晰</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 没有重复代码</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 命名准确且有意义</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 注释充分且有价值</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">### 架构设计</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 组件划分合理</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 符合项目架构设计</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 考虑了可扩展性</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 依赖关系清晰</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">### 文档</span></span>
<span class="line"><span style="color:#babed8;">- [ ] 更新了相关文档</span></span>
<span class="line"><span style="color:#babed8;">- [ ] API文档完整</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">## 常见问题与解决方案</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">### Q: 如何处理审查意见分歧？</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">A: 当开发者和审查者对某个问题有不同意见时：</span></span>
<span class="line"><span style="color:#babed8;">1. 保持开放讨论，理解彼此的观点</span></span>
<span class="line"><span style="color:#babed8;">2. 基于项目目标和技术原则，而非个人偏好做决定</span></span>
<span class="line"><span style="color:#babed8;">3. 必要时邀请第三方（如技术负责人）参与讨论</span></span>
<span class="line"><span style="color:#babed8;">4. 记录决策理由，作为未来参考</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">### Q: 如何处理大型变更的代码审查？</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">A: 对于大型变更：</span></span>
<span class="line"><span style="color:#babed8;">1. 将变更拆分为多个相关但独立的PR</span></span>
<span class="line"><span style="color:#babed8;">2. 提供高级设计文档，帮助审查者理解整体架构</span></span>
<span class="line"><span style="color:#babed8;">3. 考虑进行面对面或在线审查会议</span></span>
<span class="line"><span style="color:#babed8;">4. 为审查者提供足够的上下文和背景信息</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">### Q: 如何避免代码审查成为开发瓶颈？</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">A: 可以采取以下措施：</span></span>
<span class="line"><span style="color:#babed8;">1. 建立审查时间预期（如24小时内首次反馈）</span></span>
<span class="line"><span style="color:#babed8;">2. 分配多个审查者，避免依赖单一人员</span></span>
<span class="line"><span style="color:#babed8;">3. 对于紧急修复，建立快速通道流程</span></span>
<span class="line"><span style="color:#babed8;">4. 定期分析审查流程，找出并解决瓶颈</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">## 总结</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">有效的代码审查是提高代码质量和团队能力的关键实践。通过遵循本文档中的流程和标准，我们可以确保代码库的健康发展，同时促进团队成员的技术成长。代码审查不仅是一种质量保证机制，更是一种知识共享和团队协作的方式。</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">每位团队成员都应积极参与代码审查过程，无论是作为审查者还是被审查者，都应抱着学习和改进的态度。如有任何关于代码审查流程的问题或建议，请在团队会议中提出讨论。</span></span></code></pre></div></div></div></main><footer class="VPDocFooter" data-v-c4b0d3cf data-v-face870a><!--[--><!--]--><!----><div class="prev-next" data-v-face870a><div class="pager" data-v-face870a><a class="pager-link prev" href="/standards/git-workflow.html" data-v-face870a><span class="desc" data-v-face870a>Previous page</span><span class="title" data-v-face870a>Git工作流</span></a></div><div class="has-prev pager" data-v-face870a><a class="pager-link next" href="/standards/documentation.html" data-v-face870a><span class="desc" data-v-face870a>Next page</span><span class="title" data-v-face870a>文档规范</span></a></div></div></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-b2cf3e0b data-v-2f86ebd2><div class="container" data-v-2f86ebd2><!----><p class="copyright" data-v-2f86ebd2>Copyright © 2025 智洋上水</p></div></footer><!--[--><!--]--></div></div>
    <script>__VP_HASH_MAP__ = JSON.parse("{\"best-practices_index.md\":\"38aee9d5\",\"components_directives_permission.md\":\"72d28b46\",\"best-practices_component-design.md\":\"d570a27f\",\"index.md\":\"3e96beb2\",\"cesium_index.md\":\"440ac056\",\"standards_code-review.md\":\"04c78cff\",\"tools_vscode.md\":\"71a1eb8e\",\"tools_husky.md\":\"586bda1d\",\"components_charts.md\":\"1b67c1bd\",\"tools_debugging.md\":\"506e7f52\",\"components_directives_table-height.md\":\"1acd3698\",\"components_business_common-dialog-box.md\":\"605964f5\",\"cesium_concepts.md\":\"608db15b\",\"best-practices_vuex-best-practices.md\":\"42c910e9\",\"cesium_operations.md\":\"4af21b3f\",\"tools_index.md\":\"6738f00c\",\"components_directives_index.md\":\"e6ec9d5d\",\"best-practices_project-structure.md\":\"1dfc19f7\",\"guide_index.md\":\"a218fcb9\",\"best-practices_performance.md\":\"495f9c35\",\"best-practices_async-data.md\":\"d6f4dbd8\",\"standards_vue-standard.md\":\"204374d5\",\"guide_project-structure.md\":\"83547606\",\"standards_css-standard.md\":\"43da9895\",\"standards_html-standard.md\":\"87257590\",\"components_directives_throttle.md\":\"b30fc5ee\",\"components_directives_loading.md\":\"0bd89f42\",\"cesium_examples_water-monitor.md\":\"432751c3\",\"standards_js-standard.md\":\"e5f97f25\",\"tools_eslint.md\":\"eea2bb46\",\"components_directives_copy.md\":\"1f572838\",\"standards_documentation.md\":\"4ebedf27\",\"components_directives_debounce.md\":\"6c3b5296\",\"standards_git-workflow.md\":\"a7004f93\",\"best-practices_modular-development.md\":\"ce72d502\",\"components_index.md\":\"06a93849\",\"components_business_dict-select.md\":\"5f3747e9\",\"components_business_custom-file-upload.md\":\"40d498f0\",\"components_business_map-visualization.md\":\"11e90cc6\",\"tools_prettier.md\":\"dc0ae721\",\"best-practices_charts.md\":\"3ff7afe8\",\"components_form.md\":\"a7aefeb8\",\"components_business_dict-tag.md\":\"f87374af\",\"components_business_input-number.md\":\"186c78fb\",\"best-practices_state-management.md\":\"6cde5f2d\",\"best-practices_api-request.md\":\"32a5c3df\",\"components_business_input-word.md\":\"28d03cd2\",\"best-practices_utils.md\":\"08da2e9d\",\"guide_development-process.md\":\"7d0d3459\",\"tools_package-manager.md\":\"7c6faea2\",\"components_screen.md\":\"a126a925\",\"components_business_file-preview.md\":\"f077827f\",\"components_directives_drag-dialog.md\":\"fc009f97\",\"cesium_basics.md\":\"18bfba98\",\"components_business.md\":\"********\",\"standards_index.md\":\"e5fe1d60\",\"standards_git-commit.md\":\"4953e1ed\",\"best-practices_error-handling.md\":\"37d5b8d1\",\"best-practices_i18n.md\":\"83c49ca7\",\"components_table.md\":\"9742235f\",\"best-practices_reuse.md\":\"d4c27921\",\"best-practices_component-communication.md\":\"da381d67\",\"best-practices_routing.md\":\"a83fde5f\",\"guide_admin-development.md\":\"b638fd3e\",\"components_business_image-upload.md\":\"f2d49e5f\"}")
__VP_SITE_DATA__ = JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"前端技术开发文档\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"appearance\":true,\"themeConfig\":{\"logo\":\"/logo.jpeg\",\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"指南\",\"link\":\"/guide/\"},{\"text\":\"组件\",\"link\":\"/components/\"},{\"text\":\"最佳实践\",\"link\":\"/best-practices/\"},{\"text\":\"规范标准\",\"link\":\"/standards/\"},{\"text\":\"工具配置\",\"link\":\"/tools/\"},{\"text\":\"Cesium\",\"link\":\"/cesium/\"}],\"search\":{\"provider\":\"local\",\"options\":{\"translations\":{\"button\":{\"buttonText\":\"搜索文档\",\"buttonAriaLabel\":\"搜索文档\"},\"modal\":{\"noResultsText\":\"无法找到相关结果\",\"resetButtonTitle\":\"清除查询条件\",\"footer\":{\"selectText\":\"选择\",\"navigateText\":\"切换\",\"closeText\":\"关闭\"}}}}},\"sidebar\":{\"/guide/\":[{\"text\":\"开发指南\",\"items\":[{\"text\":\"快速开始\",\"link\":\"/guide/\"},{\"text\":\"项目结构\",\"link\":\"/guide/project-structure\"},{\"text\":\"开发流程\",\"link\":\"/guide/development-process\"},{\"text\":\"后台管理开发\",\"link\":\"/guide/admin-development\"}]}],\"/components/\":[{\"text\":\"组件库\",\"items\":[{\"text\":\"组件概览\",\"link\":\"/components/\"},{\"text\":\"业务组件\",\"collapsed\":false,\"items\":[{\"text\":\"业务组件总览\",\"link\":\"/components/business\"},{\"text\":\"字典标签组件\",\"link\":\"/components/business/dict-tag\"},{\"text\":\"字典选择器\",\"link\":\"/components/business/dict-select\"},{\"text\":\"自定义文件上传\",\"link\":\"/components/business/custom-file-upload\"},{\"text\":\"图片上传组件\",\"link\":\"/components/business/image-upload\"},{\"text\":\"自定义数字输入框\",\"link\":\"/components/business/input-number\"},{\"text\":\"自定义文本输入框\",\"link\":\"/components/business/input-word\"},{\"text\":\"文件预览组件\",\"link\":\"/components/business/file-preview\"},{\"text\":\"地图可视化组件\",\"link\":\"/components/business/map-visualization\"}]},{\"text\":\"表单组件\",\"link\":\"/components/form\"},{\"text\":\"表格组件\",\"link\":\"/components/table\"},{\"text\":\"图表组件\",\"link\":\"/components/charts\"},{\"text\":\"全局指令\",\"collapsed\":false,\"items\":[{\"text\":\"指令概览\",\"link\":\"/components/directives/index\"},{\"text\":\"表格高度\",\"link\":\"/components/directives/table-height\"},{\"text\":\"权限控制\",\"link\":\"/components/directives/permission\"},{\"text\":\"弹窗拖拽\",\"link\":\"/components/directives/drag-dialog\"},{\"text\":\"防抖处理\",\"link\":\"/components/directives/debounce\"},{\"text\":\"节流处理\",\"link\":\"/components/directives/throttle\"},{\"text\":\"一键复制\",\"link\":\"/components/directives/copy\"}]},{\"text\":\"大屏开发\",\"link\":\"/components/screen\"}]}],\"/best-practices/\":[{\"text\":\"最佳实践\",\"items\":[{\"text\":\"概述\",\"link\":\"/best-practices/\"},{\"text\":\"性能优化\",\"link\":\"/best-practices/performance\"},{\"text\":\"代码复用\",\"link\":\"/best-practices/reuse\"},{\"text\":\"状态管理\",\"link\":\"/best-practices/state-management\"},{\"text\":\"路由管理\",\"link\":\"/best-practices/routing\"},{\"text\":\"组件通信\",\"link\":\"/best-practices/component-communication\"},{\"text\":\"异步数据处理\",\"link\":\"/best-practices/async-data\"},{\"text\":\"模块化开发\",\"link\":\"/best-practices/modular-development\"},{\"text\":\"错误处理\",\"link\":\"/best-practices/error-handling\"},{\"text\":\"国际化实现\",\"link\":\"/best-practices/i18n\"},{\"text\":\"Vue组件设计\",\"link\":\"/best-practices/component-design\"},{\"text\":\"Vuex最佳实践\",\"link\":\"/best-practices/vuex-best-practices\"},{\"text\":\"Vue项目结构\",\"link\":\"/best-practices/project-structure\"},{\"text\":\"API请求封装\",\"link\":\"/best-practices/api-request\"},{\"text\":\"工具函数\",\"link\":\"/best-practices/utils\"}]}],\"/standards/\":[{\"text\":\"规范标准\",\"items\":[{\"text\":\"规范概述\",\"link\":\"/standards/\"},{\"text\":\"编码规范\",\"collapsed\":false,\"items\":[{\"text\":\"JavaScript规范\",\"link\":\"/standards/js-standard\"},{\"text\":\"CSS规范\",\"link\":\"/standards/css-standard\"},{\"text\":\"HTML规范\",\"link\":\"/standards/html-standard\"},{\"text\":\"Vue开发规范\",\"link\":\"/standards/vue-standard\"}]},{\"text\":\"Git提交规范\",\"link\":\"/standards/git-commit\"},{\"text\":\"Git工作流\",\"link\":\"/standards/git-workflow\"},{\"text\":\"代码审查\",\"link\":\"/standards/code-review\"},{\"text\":\"文档规范\",\"link\":\"/standards/documentation\"}]}],\"/tools/\":[{\"text\":\"开发工具配置\",\"items\":[{\"text\":\"工具概览\",\"link\":\"/tools/\"},{\"text\":\"VS Code配置\",\"link\":\"/tools/vscode\"},{\"text\":\"调试工具\",\"link\":\"/tools/debugging\"},{\"text\":\"包管理工具\",\"link\":\"/tools/package-manager\"}]},{\"text\":\"代码质量工具\",\"collapsed\":false,\"items\":[{\"text\":\"ESLint配置\",\"link\":\"/tools/eslint\"},{\"text\":\"Prettier配置\",\"link\":\"/tools/prettier\"},{\"text\":\"Husky配置\",\"link\":\"/tools/husky\"}]}],\"/cesium/\":[{\"text\":\"Cesium 3D地图引擎\",\"items\":[{\"text\":\"简介\",\"link\":\"/cesium/\"},{\"text\":\"基础配置\",\"link\":\"/cesium/basics\"},{\"text\":\"核心概念\",\"link\":\"/cesium/concepts\"},{\"text\":\"常用操作\",\"link\":\"/cesium/operations\"}]},{\"text\":\"实战案例\",\"collapsed\":false,\"items\":[{\"text\":\"智慧水务监控\",\"link\":\"/cesium/examples/water-monitor\"}]}]},\"footer\":{\"copyright\":\"Copyright © 2025 智洋上水\"}},\"locales\":{},\"scrollOffset\":90,\"cleanUrls\":false}")</script>
    
  </body>
</html>