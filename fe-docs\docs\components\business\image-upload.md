# ImageUpload 图片上传组件

专门用于图片上传的组件，基于ElementUI Upload组件封装，支持图片预览和多图上传功能。

## 功能特性

- 🖼️ 专为图片上传设计，支持多种图片格式
- 🔍 内置图片预览功能，点击可放大查看
- 📏 支持图片大小和数量限制
- 🎨 卡片式上传界面，美观易用
- 🗑️ 支持图片删除功能
- ⚡ 自动处理上传状态和错误
- 📱 响应式设计，适配各种屏幕

## 基础用法

### 单图上传

```vue
<template>
  <div>
    <image-upload
      v-model="avatar"
      :limit="1"
      :file-size="2"
      @input="handleAvatarChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      avatar: ''
    }
  },
  methods: {
    handleAvatarChange(imageList) {
      console.log('头像更新:', imageList)
    }
  }
}
</script>
```

### 多图上传

```vue
<template>
  <div>
    <image-upload
      v-model="gallery"
      :limit="9"
      :file-size="5"
      :file-type="['jpg', 'png', 'gif']"
      @input="handleGalleryChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      gallery: []
    }
  },
  methods: {
    handleGalleryChange(imageList) {
      console.log('图片库更新:', imageList)
      // imageList 是文件对象数组
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| value / v-model | 绑定值，图片URL字符串或图片对象数组 | String/Object/Array | — | — | 是 |
| limit | 图片数量限制 | Number | — | 5 | 否 |
| fileSize | 图片大小限制(MB) | Number | — | 5 | 否 |
| fileType | 允许的图片类型 | Array | — | ['png', 'jpg', 'jpeg'] | 否 |
| isShowTip | 是否显示上传提示 | Boolean | true/false | true | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 图片列表改变时触发 | (imageList: Array) |

### Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| handleBeforeUpload | 上传前的钩子函数 | (file: File) | Boolean |
| handleUploadSuccess | 上传成功的钩子函数 | (response: Object, file: File) | — |
| handleUploadError | 上传失败的钩子函数 | (error: Error) | — |
| handleDelete | 删除图片的方法 | (file: Object) | — |
| handlePictureCardPreview | 预览图片的方法 | (file: Object) | — |

## 图片格式支持

### 默认支持的图片类型

| 格式 | 扩展名 | 特点 |
|------|--------|------|
| JPEG | jpg, jpeg | 有损压缩，适合照片 |
| PNG | png | 无损压缩，支持透明 |
| GIF | gif | 支持动画 |
| WebP | webp | 现代格式，压缩率高 |
| BMP | bmp | 位图格式 |

### 自定义图片类型

```vue
<template>
  <div>
    <!-- 只支持JPG和PNG -->
    <image-upload
      v-model="photos"
      :file-type="['jpg', 'png']"
      :file-size="10"
    />
    
    <!-- 支持所有常见格式 -->
    <image-upload
      v-model="images"
      :file-type="['jpg', 'jpeg', 'png', 'gif', 'webp']"
      :file-size="3"
    />
  </div>
</template>
```

## 使用示例

### 商品图片上传

```vue
<template>
  <div>
    <el-form :model="productForm" label-width="120px">
      <el-form-item label="商品名称">
        <el-input v-model="productForm.name" />
      </el-form-item>
      
      <el-form-item label="主图">
        <image-upload
          v-model="productForm.mainImage"
          :limit="1"
          :file-size="5"
          :file-type="['jpg', 'png']"
        />
      </el-form-item>
      
      <el-form-item label="详情图">
        <image-upload
          v-model="productForm.detailImages"
          :limit="10"
          :file-size="3"
          :file-type="['jpg', 'png', 'gif']"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      productForm: {
        name: '',
        mainImage: [],
        detailImages: []
      }
    }
  },
  methods: {
    handleSubmit() {
      console.log('商品数据:', this.productForm)
    }
  }
}
</script>
```

### 用户头像上传

```vue
<template>
  <div>
    <el-card header="个人信息">
      <el-form :model="userInfo" label-width="100px">
        <el-form-item label="用户头像">
          <image-upload
            v-model="userInfo.avatar"
            :limit="1"
            :file-size="2"
            :file-type="['jpg', 'png']"
            @input="handleAvatarUpdate"
          />
          <div class="upload-tip">
            建议上传正方形图片，大小不超过2MB
          </div>
        </el-form-item>
        
        <el-form-item label="用户名">
          <el-input v-model="userInfo.username" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        username: '',
        avatar: []
      }
    }
  },
  methods: {
    handleAvatarUpdate(imageList) {
      console.log('头像更新:', imageList)
      // 可以在这里调用API更新头像
      this.updateUserAvatar(imageList)
    },
    
    async updateUserAvatar(imageList) {
      try {
        // 调用更新头像的API
        await this.$api.updateAvatar({ avatar: imageList[0] })
        this.$message.success('头像更新成功')
      } catch (error) {
        this.$message.error('头像更新失败')
      }
    }
  }
}
</script>

<style scoped>
.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
```

### 相册管理

```vue
<template>
  <div>
    <el-card header="照片相册">
      <image-upload
        v-model="albumImages"
        :limit="20"
        :file-size="10"
        :file-type="['jpg', 'jpeg', 'png']"
        @input="handleAlbumChange"
      />
      
      <div class="album-stats" v-if="albumImages.length > 0">
        <p>共 {{ albumImages.length }} 张照片</p>
        <p>总大小: {{ calculateTotalSize() }}MB</p>
      </div>
      
      <el-button 
        type="primary" 
        @click="saveAlbum"
        :disabled="albumImages.length === 0"
      >
        保存相册
      </el-button>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      albumImages: []
    }
  },
  methods: {
    handleAlbumChange(imageList) {
      console.log('相册更新:', imageList)
    },
    
    calculateTotalSize() {
      // 模拟计算总大小
      return (this.albumImages.length * 2.5).toFixed(1)
    },
    
    async saveAlbum() {
      try {
        await this.$api.saveAlbum({ images: this.albumImages })
        this.$message.success('相册保存成功')
      } catch (error) {
        this.$message.error('相册保存失败')
      }
    }
  }
}
</script>

<style scoped>
.album-stats {
  margin: 15px 0;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
```

## 高级特性

### 图片压缩

```vue
<template>
  <div>
    <image-upload
      v-model="compressedImages"
      :file-size="1"
      :before-upload="compressImage"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      compressedImages: []
    }
  },
  methods: {
    compressImage(file) {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        const img = new Image()
        
        img.onload = () => {
          // 设置压缩尺寸
          const maxWidth = 800
          const maxHeight = 600
          let { width, height } = img
          
          if (width > height) {
            if (width > maxWidth) {
              height = height * (maxWidth / width)
              width = maxWidth
            }
          } else {
            if (height > maxHeight) {
              width = width * (maxHeight / height)
              height = maxHeight
            }
          }
          
          canvas.width = width
          canvas.height = height
          
          ctx.drawImage(img, 0, 0, width, height)
          
          canvas.toBlob(resolve, 'image/jpeg', 0.8)
        }
        
        img.src = URL.createObjectURL(file)
      })
    }
  }
}
</script>
```

## 注意事项

1. **图片格式**：确保上传的图片格式被服务器支持
2. **文件大小**：合理设置图片大小限制，平衡质量和性能
3. **数量限制**：根据业务需求设置合适的图片数量限制
4. **预览功能**：大图预览会消耗一定内存，注意性能影响
5. **移动端适配**：在移动设备上测试上传和预览功能

## 常见问题

### Q: 如何获取已上传的图片？

A: 组件的 `v-model` 绑定值会返回图片对象数组，每个对象包含 `name` 和 `url` 属性。

### Q: 支持图片编辑吗？

A: 当前版本不支持内置图片编辑，可以集成第三方图片编辑库。

### Q: 如何实现图片懒加载？

A: 可以结合 `vue-lazyload` 等懒加载库来优化图片加载性能。

### Q: 上传失败如何处理？

A: 组件内置了错误处理机制，失败时会显示错误提示，可以重新上传。

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :on-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="viewList"
      :on-preview="handlePictureCardPreview"
      :class="{hide: this.fileList.length >= this.limit}"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b></template>
      的文件
    </div>

    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      title="预览"
      width="800"
      append-to-body
      @closed="dialogImageUrl=null"
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; height: 600px; object-fit: contain; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>

<script>
import {getToken} from "@/utils/auth";
import {getAbsoluteUrl} from "@/utils/zhy";
import request from "@/utils/request";

export default {
  props: {
    value: [String, Object, Array],
    // 图片数量限制
    limit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      hideUpload: false,
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/file/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      viewList: []
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',');
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              item = {name: item, url: item};
            }
            return item;
          });

          if (this.viewList.length === 0) {
            this.viewList = list.map(item => {
              if (typeof item === "string") {
                item = {name: item, url: getAbsoluteUrl(item)};
              }else {
                item = {...item, url: getAbsoluteUrl(item.url)}
              }
              return item;
            });
          }
        } else {
          this.fileList = [];
          this.viewList = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    }
  },
  methods: {
    // 上传前loading加载
    handleBeforeUpload(file) {
      let isImg = false;
      if (this.fileType.length) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        isImg = this.fileType.some(type => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
      } else {
        isImg = file.type.indexOf("image") > -1;
      }

      if (!isImg) {
        this.$modal.msgError(`文件格式不正确, 请上传${this.fileType.join("/")}图片格式文件!`);
        return false;
      }
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      this.$modal.loading("正在上传图片，请稍候...");
      this.number++;
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({name: res.data.name, url: res.data.url});
        this.uploadedSuccessfully();
      } else {
        this.number--;
        this.$modal.closeLoading();
        this.$modal.msgError(res.msg);
        this.$refs.imageUpload.handleRemove(file);
        this.uploadedSuccessfully();
      }
    },
    // 删除图片
    handleDelete(file) {
      const findex = this.fileList.map(f => f.name).indexOf(file.name);
      if (findex > -1) {
        // this.deleteFile(this.fileList[findex])
        this.fileList.splice(findex, 1);
        this.$emit("input", this.fileList);
      }
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError("上传图片失败，请重试");
      this.$modal.closeLoading();
    },
    deleteFile(file) {
      request({
        url: '/file/delete',
        method: 'delete',
        data: file
      })
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList);
        this.uploadList = [];
        this.number = 0;
        this.$emit("input", this.fileList);
        this.$modal.closeLoading();
      }
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = "";
      separator = separator || ",";
      for (let i in list) {
        if (list[i].url) {
          strs += list[i].url.replace(this.baseUrl, "") + separator;
        }
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : '';
    }
  }
};
</script>
<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
  display: none;
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter, .el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}
</style>
```

</details>
