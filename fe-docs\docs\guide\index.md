# 开发指南

欢迎使用公司前端开发指南！本指南旨在帮助开发人员快速了解我们的前端技术栈、开发流程和项目结构，以便能够更快地融入团队并开始工作。

## 技术栈

我们的前端项目主要基于以下技术栈：

- **框架**：**Vue 2.x**
- **构建工具**：**Webpack**
- **UI组件库**：**Element UI**
- **状态管理**：**Vuex**
- **路由管理**：**Vue Router**
- **HTTP请求**：**Axios**
- **代码规范**：**ESLint + Prettier**
- **CSS预处理器**：**SCSS**
- **图表库**：**ECharts**

## 快速开始

### 环境准备

开发前，请确保您的计算机已安装以下软件：

1. **Node.js**：推荐使用 node16版本
2. **npm** 或 **yarn**：包管理工具
3. **Git**：版本控制工具

### 克隆项目

```bash
# 公司内网  
http://192.168.9.5:3000/
```

### 安装依赖

```bash

# 使用npm 内部源
npm install  --registry http://172.20.2.11:8081/repository/npm/

# 或使用yarn
yarn

# 如果有依赖冲突可使用下方指令，强行安装，不建议使用
npm install --legacy-peer-deps --registry http://172.20.2.11:8081/repository/npm/ 
```

### 启动开发服务器

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

## 项目结构

请查看[项目结构](/guide/project-structure)页面了解详细的项目结构说明。

## 开发流程

请查看[开发流程](/guide/development-process)页面了解我们的开发流程、分支管理策略和代码提交规范。 