import{_ as s,o as a,c as l,V as n}from"./chunks/framework.3d729ebc.js";const g=JSON.parse('{"title":"开发流程","description":"","frontmatter":{},"headers":[],"relativePath":"guide/development-process.md","filePath":"guide/development-process.md"}'),e={name:"guide/development-process.md"},o=n(`<h1 id="开发流程" tabindex="-1">开发流程 <a class="header-anchor" href="#开发流程" aria-label="Permalink to &quot;开发流程&quot;">​</a></h1><p>本页面介绍现代化水库管理矩阵前端项目的标准开发流程、分支管理和代码提交规范。规范化的开发流程有助于提高团队协作效率，保证代码质量。</p><h2 id="项目概述" tabindex="-1">项目概述 <a class="header-anchor" href="#项目概述" aria-label="Permalink to &quot;项目概述&quot;">​</a></h2><p>Matrix-UI是一个基于Vue 2.x技术栈的现代化水库管理系统前端项目，集成了以下核心功能：</p><ul><li>🌊 <strong>四全管理</strong>：全覆盖、全要素、全天候、全周期</li><li>⚖️ <strong>四制体系</strong>：体制、机制、法制、责任制</li><li>🔮 <strong>四预系统</strong>：预报、预警、预演、预案</li><li>🛡️ <strong>四管功能</strong>：安全、除险、体检、维护</li><li>🗺️ <strong>3D可视化</strong>：Cesium三维地图、数字孪生</li><li>📊 <strong>数据大屏</strong>：ECharts图表、实时监控</li></ul><h2 id="git工作流" tabindex="-1">Git工作流 <a class="header-anchor" href="#git工作流" aria-label="Permalink to &quot;Git工作流&quot;">​</a></h2><p>我们采用基于Git Flow的分支管理策略，主要包含以下分支：</p><ul><li><strong>master/main</strong>: 主分支，用于生产环境，只能从release或hotfix分支合并</li><li><strong>develop</strong>: 开发分支，所有功能开发完成后合并到此分支</li><li><strong>feature/xxx</strong>: 功能分支，从develop分支创建，开发完成后合并回develop分支</li><li><strong>release/vx.x.x</strong>: 发布分支，从develop分支创建，准备发布版本</li><li><strong>hotfix/xxx</strong>: 热修复分支，从master分支创建，修复生产环境bug</li></ul><h2 id="开发流程-1" tabindex="-1">开发流程 <a class="header-anchor" href="#开发流程-1" aria-label="Permalink to &quot;开发流程&quot;">​</a></h2><h3 id="_1-功能开发" tabindex="-1">1. 功能开发 <a class="header-anchor" href="#_1-功能开发" aria-label="Permalink to &quot;1. 功能开发&quot;">​</a></h3><ol><li><p>从最新的develop分支创建功能分支</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pull</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-b</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">feature/screen-siyuan-yubao</span></span></code></pre></div></li><li><p>在功能分支上进行开发</p><ul><li>遵循Vue 2.x组合式API开发规范</li><li>使用Element UI组件库</li><li>集成Cesium/ECharts等第三方库</li></ul></li><li><p>提交代码，遵循提交规范</p></li><li><p>创建Pull Request到develop分支</p></li><li><p>代码审查通过后合并到develop分支</p></li></ol><h3 id="_2-版本发布" tabindex="-1">2. 版本发布 <a class="header-anchor" href="#_2-版本发布" aria-label="Permalink to &quot;2. 版本发布&quot;">​</a></h3><ol><li><p>从develop分支创建release分支</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">develop</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pull</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-b</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">release/v3.7.0</span></span></code></pre></div></li><li><p>在release分支上进行集成和问题修复</p></li><li><p>完成后合并到master分支和develop分支</p></li><li><p>在master分支上打版本标签</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">tag</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-a</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">v3.7.0</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">版本3.7.0发布 - 新增四预系统功能</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">push</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">origin</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">v3.7.0</span></span></code></pre></div></li></ol><h3 id="_3-生产问题修复" tabindex="-1">3. 生产问题修复 <a class="header-anchor" href="#_3-生产问题修复" aria-label="Permalink to &quot;3. 生产问题修复&quot;">​</a></h3><ol><li><p>从master分支创建hotfix分支</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">master</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pull</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">checkout</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-b</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">hotfix/screen-memory-leak</span></span></code></pre></div></li><li><p>修复问题</p></li><li><p>合并到master分支和develop分支</p></li><li><p>在master分支上打补丁版本标签</p></li></ol><h2 id="代码提交规范" tabindex="-1">代码提交规范 <a class="header-anchor" href="#代码提交规范" aria-label="Permalink to &quot;代码提交规范&quot;">​</a></h2><p>我们采用Angular提交规范，针对水库管理系统增加特定的scope：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">&lt;type&gt;(&lt;scope&gt;): &lt;subject&gt;</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">&lt;body&gt;</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">&lt;footer&gt;</span></span></code></pre></div><h3 id="type类型" tabindex="-1">Type类型 <a class="header-anchor" href="#type类型" aria-label="Permalink to &quot;Type类型&quot;">​</a></h3><ul><li><strong>feat</strong>: 新功能</li><li><strong>fix</strong>: 修复bug</li><li><strong>docs</strong>: 文档更新</li><li><strong>style</strong>: 代码风格调整，不影响功能</li><li><strong>refactor</strong>: 重构代码，不新增功能或修复bug</li><li><strong>perf</strong>: 性能优化</li><li><strong>chore</strong>: 构建过程或辅助工具的变动</li></ul><h3 id="scope范围-业务模块" tabindex="-1">Scope范围（业务模块） <a class="header-anchor" href="#scope范围-业务模块" aria-label="Permalink to &quot;Scope范围（业务模块）&quot;">​</a></h3><ul><li><strong>screen</strong>: 大屏相关功能</li><li><strong>monitor</strong>: 监测模块</li><li><strong>dispatch</strong>: 调度模块</li><li><strong>digital-twin</strong>: 数字孪生</li><li><strong>map</strong>: 地图功能</li><li><strong>chart</strong>: 图表组件</li><li><strong>auth</strong>: 认证授权</li><li><strong>api</strong>: 接口相关</li><li><strong>utils</strong>: 工具函数</li></ul><h3 id="提交示例" tabindex="-1">提交示例 <a class="header-anchor" href="#提交示例" aria-label="Permalink to &quot;提交示例&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">feat(screen): 新增四预系统预报大屏</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">- 添加水文预报数据展示组件</span></span>
<span class="line"><span style="color:#babed8;">Closes: #JIRA-456</span></span></code></pre></div><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">fix(cesium): 修复三维地图加载异常</span></span>
<span class="line"><span style="color:#babed8;">- 解决Cesium地形数据加载失败问题</span></span>
<span class="line"><span style="color:#babed8;">- 优化Cesium Worker配置</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;">Fixes: #BUG-123</span></span></code></pre></div><h2 id="代码审查-code-review" tabindex="-1">代码审查(Code Review) <a class="header-anchor" href="#代码审查-code-review" aria-label="Permalink to &quot;代码审查(Code Review)&quot;">​</a></h2><p>所有代码必须经过至少一位团队成员的代码审查后才能合并到develop分支</p><h3 id="_1-功能完整性" tabindex="-1">1. 功能完整性 <a class="header-anchor" href="#_1-功能完整性" aria-label="Permalink to &quot;1. 功能完整性&quot;">​</a></h3><ul><li>✅ 是否实现了需求的所有功能点</li><li>✅ 大屏适配是否正确（1920x1080）</li><li>✅ 数据展示是否准确</li></ul><h3 id="_2-性能问题" tabindex="-1">2. 性能问题 <a class="header-anchor" href="#_2-性能问题" aria-label="Permalink to &quot;2. 性能问题&quot;">​</a></h3><ul><li>✅ Cesium场景渲染性能</li><li>✅ ECharts图表加载速度</li><li>✅ 大数据量处理优化</li><li>✅ 内存泄漏检查</li></ul><h3 id="_3-兼容性问题" tabindex="-1">3. 兼容性问题 <a class="header-anchor" href="#_3-兼容性问题" aria-label="Permalink to &quot;3. 兼容性问题&quot;">​</a></h3><ul><li>✅ 浏览器兼容性（Chrome、Edge、Firefox）</li><li>✅ 分辨率适配</li><li>✅ 第三方库版本兼容</li></ul><h3 id="_4-安全问题" tabindex="-1">4. 安全问题 <a class="header-anchor" href="#_4-安全问题" aria-label="Permalink to &quot;4. 安全问题&quot;">​</a></h3><ul><li>✅ XSS防护</li><li>✅ API接口鉴权</li><li>✅ 敏感数据加密</li></ul><h3 id="环境变量配置" tabindex="-1">环境变量配置 <a class="header-anchor" href="#环境变量配置" aria-label="Permalink to &quot;环境变量配置&quot;">​</a></h3><p>创建环境变量文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .env.development</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_TITLE</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">现代化水库管理矩阵</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_BASE_API</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">/dev-api</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_CESIUM_TOKEN</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">your_cesium_token</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_MAP_KEY</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">your_map_key</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># .env.production  </span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_TITLE</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">现代化水库管理矩阵</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_BASE_API</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">/prod-api</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_CESIUM_TOKEN</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">your_cesium_token</span></span>
<span class="line"><span style="color:#BABED8;">VUE_APP_MAP_KEY</span><span style="color:#89DDFF;">=</span><span style="color:#C3E88D;">your_map_key</span></span></code></pre></div>`,38),p=[o];function t(i,r,c,d,h,y){return a(),l("div",null,p)}const b=s(e,[["render",t]]);export{g as __pageData,b as default};
