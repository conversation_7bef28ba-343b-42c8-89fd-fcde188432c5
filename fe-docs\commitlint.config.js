module.exports = {
    extends: ['@commitlint/config-conventional'],
    parserPreset: {
        parserOpts: {
            headerPattern: /^(\w+)(?:\(([\w\s-]*)\))?[：:]\s*(.+)$/,
            headerCorrespondence: ['type', 'scope', 'subject'],
        },
    },
    rules: {
        'type-enum': [
            2,
            'always',
            [
                'feat',
                'fix',
                'docs',
                'style',
                'refactor',
                'perf',
                'test',
                'chore',
                'ci',
                'build',
                'revert',
            ],
        ],
        'type-case': [2, 'always', 'lower-case'],
        'type-empty': [2, 'never'],
        // 不校验 subject
        'subject-empty': [0],
        'subject-case': [0],
        'subject-full-stop': [0],
        'header-max-length': [2, 'always', 100],
        'scope-empty': [0],
        'scope-case': [0],
    },
}
