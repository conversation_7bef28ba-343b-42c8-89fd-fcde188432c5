# v-drag-dialog

为 Element UI 的 `el-dialog` 组件增加拖拽功能，使用户可以通过拖拽标题栏自由移动弹窗位置。

## 使用场景

- 需要移动弹窗位置以查看被遮挡的内容
- 多个弹窗同时显示时的位置调整
- 提升用户交互体验的弹窗操作

## 基本用法

```vue
<template>
  <!-- 基础拖拽弹窗 -->
  <el-dialog
    v-drag-dialog
    title="可拖拽弹窗"
    :visible.sync="dialogVisible"
  >
    <p>点击标题栏并拖拽即可移动位置</p>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false
    };
  }
};
</script>
```

## 高级用法

### 限制拖拽范围

```vue
<template>
  <el-dialog
    v-drag-dialog="{ boundary: true }"
    title="限制拖拽范围"
    :visible.sync="dialogVisible"
  >
    <p>此弹窗的拖拽范围被限制在可视区域内</p>
  </el-dialog>
</template>
```

### 监听拖拽事件

```vue
<template>
  <el-dialog
    v-drag-dialog
    title="监听拖拽事件"
    :visible.sync="dialogVisible"
    @dragStart="handleDragStart"
    @dragEnd="handleDragEnd"
  >
    <p>拖拽状态：{{ dragStatus }}</p>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      dragStatus: '未开始'
    };
  },
  methods: {
    handleDragStart() {
      this.dragStatus = '开始拖拽';
    },
    handleDragEnd() {
      this.dragStatus = '拖拽结束';
    }
  }
};
</script>
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| boundary | Boolean | false | 是否限制拖拽范围在可视区域内 |
| handle | String | '.el-dialog__header' | 拖拽手柄的选择器 |
| disabled | Boolean | false | 是否禁用拖拽功能 |

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directives/drag-dialog.js
export default {
  bind(el, binding, vnode) {
    const dialogHeaderEl = el.querySelector('.el-dialog__header');
    const dragDom = el.querySelector('.el-dialog');

    if (!dialogHeaderEl || !dragDom) {
      console.warn('v-drag-dialog: 未找到必要的DOM元素');
      return;
    }

    // 获取指令参数
    const options = binding.value || {};
    const { boundary = false, disabled = false } = options;

    if (disabled) return;

    // 设置拖拽手柄样式
    dialogHeaderEl.style.cursor = 'move';
    dialogHeaderEl.style.userSelect = 'none';

    // 拖拽状态变量
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let startLeft = 0;
    let startTop = 0;

    // 鼠标按下事件
    const handleMouseDown = (e) => {
      if (disabled || e.button !== 0) return;

      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;

      // 获取当前位置
      const style = window.getComputedStyle(dragDom);
      startLeft = parseInt(style.left, 10) || 0;
      startTop = parseInt(style.top, 10) || 0;

      // 触发拖拽开始事件
      if (vnode.componentInstance) {
        vnode.componentInstance.$emit('dragStart', { x: startX, y: startY });
      }

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      e.preventDefault();
    };

    // 鼠标移动事件
    const handleMouseMove = (e) => {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newLeft = startLeft + deltaX;
      let newTop = startTop + deltaY;

      // 边界限制
      if (boundary) {
        const rect = dragDom.getBoundingClientRect();
        const maxLeft = window.innerWidth - rect.width;
        const maxTop = window.innerHeight - rect.height;

        newLeft = Math.max(0, Math.min(newLeft, maxLeft));
        newTop = Math.max(0, Math.min(newTop, maxTop));
      }

      // 更新位置
      dragDom.style.left = newLeft + 'px';
      dragDom.style.top = newTop + 'px';
      dragDom.style.marginTop = '0';
      dragDom.style.marginLeft = '0';

      // 触发拖拽事件
      if (vnode.componentInstance) {
        vnode.componentInstance.$emit('dragDialog');
      }
    };

    // 鼠标释放事件
    const handleMouseUp = (e) => {
      if (!isDragging) return;

      isDragging = false;

      // 触发拖拽结束事件
      if (vnode.componentInstance) {
        vnode.componentInstance.$emit('dragEnd', { x: e.clientX, y: e.clientY });
      }

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    // 绑定事件
    dialogHeaderEl.addEventListener('mousedown', handleMouseDown);

    // 存储清理函数
    el._dragCleanup = () => {
      dialogHeaderEl.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  },

  update(el, binding) {
    const options = binding.value || {};
    const { disabled = false } = options;

    const dialogHeaderEl = el.querySelector('.el-dialog__header');
    if (dialogHeaderEl) {
      dialogHeaderEl.style.cursor = disabled ? 'default' : 'move';
    }
  },

  unbind(el) {
    if (el._dragCleanup) {
      el._dragCleanup();
      delete el._dragCleanup;
    }
  }
};
```

</details>


