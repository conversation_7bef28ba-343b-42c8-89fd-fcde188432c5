import{_ as b,o as c,c as p,C as r,J as g,E as f,T as x,G as v,F as S,R as T,a5 as h,a6 as y,K as d,V as I,n as E,S as A,U as O,H as _,D as L,Q as m,a7 as R,a8 as F,a9 as V,aa as D,ab as B,ac as N,ad as j,ae as z,af as M,ag as U,M as G,d as H,u as q,k as J,q as K,ah as Q,ai as X,aj as W,ak as Y}from"./chunks/framework.3d729ebc.js";import{t as k}from"./chunks/theme.533e1ce4.js";const Z={name:"ColorPicker",data(){return{isOpen:!1,currentColor:"#2563eb",customColor:"#2563eb",previewColor:"#2563eb",colorPresets:[{name:"蓝色",primary:"#2563eb",light:"#3b82f6"},{name:"青色",primary:"#0891b2",light:"#06b6d4"},{name:"绿色",primary:"#059669",light:"#10b981"},{name:"紫色",primary:"#7c3aed",light:"#8b5cf6"},{name:"粉色",primary:"#db2777",light:"#ec4899"},{name:"橙色",primary:"#ea580c",light:"#f97316"},{name:"红色",primary:"#dc2626",light:"#ef4444"},{name:"灰色",primary:"#4b5563",light:"#6b7280"}]}},mounted(){const e=localStorage.getItem("vitepress-theme-color");e&&(this.currentColor=e,this.customColor=e,this.previewColor=e,this.applyColorToCSS(e))},methods:{togglePicker(){this.isOpen=!this.isOpen,this.isOpen&&(this.previewColor=this.currentColor,this.customColor=this.currentColor)},closePicker(){this.isOpen=!1},selectColor(e){this.previewColor=e.primary,this.customColor=e.primary},onCustomColorChange(){this.previewColor=this.customColor},onCustomColorInput(){/^#[0-9A-F]{6}$/i.test(this.customColor)&&(this.previewColor=this.customColor)},applyColor(){this.currentColor=this.previewColor,this.applyColorToCSS(this.previewColor),localStorage.setItem("vitepress-theme-color",this.previewColor),this.closePicker(),this.showToast("主题色已更新！")},resetColor(){const e="#2563eb";this.previewColor=e,this.customColor=e,this.currentColor=e,this.applyColorToCSS(e),localStorage.removeItem("vitepress-theme-color"),this.showToast("已重置为默认主题色")},applyColorToCSS(e){const o=this.lightenColor(e,20),s=this.lightenColor(e,40);document.documentElement.style.setProperty("--vp-c-brand",e),document.documentElement.style.setProperty("--vp-c-brand-light",o),document.documentElement.style.setProperty("--vp-c-brand-lighter",s),document.documentElement.style.setProperty("--vp-c-brand-1",e),document.documentElement.style.setProperty("--vp-c-brand-2",o),document.documentElement.style.setProperty("--vp-c-brand-3",s);const a=this.hexToRgba(e,.14);document.documentElement.style.setProperty("--vp-c-brand-soft",a)},lightenColor(e,o){const s=parseInt(e.replace("#",""),16),a=Math.round(2.55*o),n=(s>>16)+a,l=(s>>8&255)+a,t=(s&255)+a;return"#"+(16777216+(n<255?n<1?0:n:255)*65536+(l<255?l<1?0:l:255)*256+(t<255?t<1?0:t:255)).toString(16).slice(1)},hexToRgba(e,o){const s=parseInt(e.slice(1,3),16),a=parseInt(e.slice(3,5),16),n=parseInt(e.slice(5,7),16);return`rgba(${s}, ${a}, ${n}, ${o})`},showToast(e){const o=document.createElement("div");o.textContent=e,o.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--vp-c-brand);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
      `,document.body.appendChild(o),setTimeout(()=>{o.style.opacity="0",o.style.transform="translateX(100%)",setTimeout(()=>{document.body.removeChild(o)},300)},2e3)}}},u=e=>(A("data-v-917787cc"),e=e(),O(),e),$={class:"color-picker"},ee=["title"],oe=I('<div class="palette-icon" data-v-917787cc><div class="palette-circle" data-v-917787cc></div><div class="palette-colors" data-v-917787cc><div class="color-dot color-1" data-v-917787cc></div><div class="color-dot color-2" data-v-917787cc></div><div class="color-dot color-3" data-v-917787cc></div><div class="color-dot color-4" data-v-917787cc></div></div></div>',1),te=[oe],se={key:0,class:"color-picker-panel"},re={class:"color-picker-header"},ne=u(()=>r("h3",null,"选择主题色",-1)),le={class:"color-presets"},ae=u(()=>r("div",{class:"preset-label"},"预设颜色",-1)),ce={class:"preset-colors"},ie=["onClick","title"],pe={class:"custom-color"},de=u(()=>r("div",{class:"custom-label"},"自定义颜色",-1)),ue={class:"color-input-group"},me={class:"color-preview"},Ce=u(()=>r("div",{class:"preview-label"},"预览效果",-1)),ve={class:"preview-items"},he={class:"color-actions"};function ye(e,o,s,a,n,l){return c(),p("div",$,[r("button",{class:"color-picker-trigger",onClick:o[0]||(o[0]=(...t)=>l.togglePicker&&l.togglePicker(...t)),title:n.isOpen?"关闭色彩选择器":"打开色彩选择器"},te,8,ee),g(x,{name:"picker-fade"},{default:f(()=>[n.isOpen?(c(),p("div",se,[r("div",re,[ne,r("button",{class:"close-btn",onClick:o[1]||(o[1]=(...t)=>l.closePicker&&l.closePicker(...t))},"×")]),r("div",le,[ae,r("div",ce,[(c(!0),p(S,null,T(n.colorPresets,t=>(c(),p("button",{key:t.name,class:E(["preset-color",{active:n.currentColor===t.primary}]),style:d({backgroundColor:t.primary}),onClick:C=>l.selectColor(t),title:t.name},null,14,ie))),128))])]),r("div",pe,[de,r("div",ue,[h(r("input",{type:"color","onUpdate:modelValue":o[2]||(o[2]=t=>n.customColor=t),onInput:o[3]||(o[3]=(...t)=>l.onCustomColorChange&&l.onCustomColorChange(...t)),class:"color-input"},null,544),[[y,n.customColor]]),h(r("input",{type:"text","onUpdate:modelValue":o[4]||(o[4]=t=>n.customColor=t),onInput:o[5]||(o[5]=(...t)=>l.onCustomColorInput&&l.onCustomColorInput(...t)),class:"color-text",placeholder:"#2563eb"},null,544),[[y,n.customColor]])])]),r("div",me,[Ce,r("div",ve,[r("div",{class:"preview-button",style:d({backgroundColor:n.previewColor})}," 按钮样式 ",4),r("div",{class:"preview-link",style:d({color:n.previewColor})}," 链接样式 ",4),r("div",{class:"preview-border",style:d({borderColor:n.previewColor})}," 边框样式 ",4)])]),r("div",he,[r("button",{class:"reset-btn",onClick:o[6]||(o[6]=(...t)=>l.resetColor&&l.resetColor(...t))},"重置默认"),r("button",{class:"apply-btn",onClick:o[7]||(o[7]=(...t)=>l.applyColor&&l.applyColor(...t)),style:d({backgroundColor:n.previewColor})}," 应用主题 ",4)])])):v("",!0)]),_:1}),n.isOpen?(c(),p("div",{key:0,class:"color-picker-overlay",onClick:o[8]||(o[8]=(...t)=>l.closePicker&&l.closePicker(...t))})):v("",!0)])}const w=b(Z,[["render",ye],["__scopeId","data-v-917787cc"]]);const{Layout:_e}=k,be={name:"CustomLayout",components:{Layout:_e,ColorPicker:w}},ge={class:"nav-color-picker"};function fe(e,o,s,a,n,l){const t=_("ColorPicker"),C=_("Layout",!0);return c(),L(C,null,{"nav-bar-content-after":f(()=>[r("div",ge,[g(t)])]),_:1})}const ke=b(be,[["render",fe],["__scopeId","data-v-21678b25"]]),we={extends:k,Layout:ke,enhanceApp({app:e}){e.component("ColorPicker",w)}};function P(e){if(e.extends){const o=P(e.extends);return{...o,...e,async enhanceApp(s){o.enhanceApp&&await o.enhanceApp(s),e.enhanceApp&&await e.enhanceApp(s)}}}return e}const i=P(we),Pe=H({name:"VitePressApp",setup(){const{site:e}=q();return J(()=>{K(()=>{document.documentElement.lang=e.value.lang,document.documentElement.dir=e.value.dir})}),Q(),X(),W(),i.setup&&i.setup(),()=>Y(i.Layout)}});async function xe(){const e=Te(),o=Se();o.provide(F,e);const s=V(e.route);return o.provide(D,s),o.component("Content",B),o.component("ClientOnly",N),Object.defineProperties(o.config.globalProperties,{$frontmatter:{get(){return s.frontmatter.value}},$params:{get(){return s.page.value.params}}}),i.enhanceApp&&await i.enhanceApp({app:o,router:e,siteData:j}),{app:o,router:e,data:s}}function Se(){return z(Pe)}function Te(){let e=m,o;return M(s=>{let a=U(s);return e&&(o=a),(e||o===a)&&(a=a.replace(/\.js$/,".lean.js")),m&&(e=!1),G(()=>import(a),[])},i.NotFound)}m&&xe().then(({app:e,router:o,data:s})=>{o.go().then(()=>{R(o.route,s.site),e.mount("#app")})});export{xe as createApp};
