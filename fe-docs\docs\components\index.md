# 组件库

本章节介绍公司内部前端组件库，包括基础组件、业务组件、表单组件和图表组件。组件库旨在提高开发效率，保持UI一致性，并确保代码的可维护性。

## 组件分类

我们的组件库主要分为以下几类：

### 业务组件

针对特定业务场景开发的复合组件，如字典组件、弹窗组件、地图可视化等。这些组件封装了特定业务逻辑，可以快速应用于相关业务场景。

[查看业务组件 →](/components/business)

### 表单组件

专门用于表单场景的组件，包括表单布局、验证、提交等功能。这些组件简化了表单开发流程，提高了表单的易用性和一致性。

[查看表单组件 →](/components/form)

### 图表组件

基于ECharts封装的各类图表组件，支持柱状图、折线图、饼图等多种图表类型，并提供了统一的数据格式和配置项。

[查看图表组件 →](/components/charts)

### 全局指令

提供一系列实用的Vue自定义指令，包括表格高度自适应、权限控制、防抖节流等功能，帮助简化开发流程，提高代码复用性。

[查看全局指令 →](/components/directives/)

### 大屏开发

提供大屏可视化开发的完整解决方案，包括屏幕自适应、布局模板和图表集成方案，帮助快速构建专业的数据可视化大屏。

[查看大屏开发 →](/components/screen)


