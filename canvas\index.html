<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    * {
      margin: 0;
      padding: 0;

    }

    html {
      height: 100%;
      width: 100%;
    }

    body {
      margin: 0;
      padding: 0;
      /* background: #ddd; */
      height: 100%;
      width: 100%;
    }

    #bkg {
      height: 100%;
      width: 100%;
      line-height: 1;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      /* background: rgb(3, 5, 4); */
    }

    #canvas {
      display: block;
      margin: 0 auto;
      /* border: thin inset #aaa; */
    }
  </style>
</head>

<body>
  <div id="bkg">
    <select id="testselect" onchange="binchange();">
      <option value='source-over'>source-over</option>
      <option value='destination-over'>destination-over</option>
      <option value='destination-atop'>destination-atop</option>
      <option value='source-in'>source-in</option>
      <option value='destination-in'>destination-in</option>
      <option value='source-out'>source-out</option>
      <option value='destination-out'>destination-out</option>
      <option value='lighter'>lighter</option>
      <option value='copy'>copy</option>
      <option value='xor'>xor</option>
    </select>

    <canvas id="canvas" width="1000" height="1000">
      canvas not supported
    </canvas>
  </div>
  <!-- <script src="../common.js"></script> -->

  <script>

    var canvas = document.getElementById("canvas");
    var context = canvas.getContext('2d');

    var testselect = document.getElementById('testselect');

    sourceatop();
    function binchange () {
      var index = testselect.selectedIndex;
      var value = testselect.options[index].value;
      context.clearRect(0, 0, context.canvas.width, context.canvas.height);
      switch (value) {
        case 'source-over':
          sourceover();
          break;
        case 'destination-over':
          destinationover();
          break;
        case 'source-atop':
          sourceatop();
          break;
        case 'destination-atop':
          destinationatop();
          break;
        case 'source-in':
          sourcein();
          break;
        case 'destination-in':
          destinationin();
          break;
        case 'source-out':
          sourceout();
          break;
        case 'destination-out':
          destinationout();
          break;
        case 'lighter':
          lighter();
          break;
        case 'copy':
          copy();
          break;
        case 'xor':
          xor();
          break;
        default:
          break;
      }
    }


    function sourceover () {
      //矩形一蓝色,目标
      context.save();
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'source-over'
      //矩形二粉色,源
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 粉色覆盖蓝色
    }


    function destinationover () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'destination-over'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 蓝色覆盖粉色
    }

    function sourceatop () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'source-atop'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 留下蓝色,以及粉色和蓝色公共部分的粉色
    }

    function destinationatop () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'destination-atop'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 留下粉色,以及粉色和蓝色公共部分的蓝色
    }

    function sourcein () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'source-in'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 留下粉色和蓝色公共部分的粉色
    }

    function destinationin () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'destination-in'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 留下粉色和蓝色公共部分的蓝色
    }

    function sourceout () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'source-out'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 粉色去除留下粉色和蓝色公共部分,剩下的粉色
    }

    function destinationout () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'destination-out'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 蓝色去除留下粉色和蓝色公共部分,剩下的蓝色
    }


    function lighter () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'lighter'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 重叠部分颜色相加
    }

    function copy () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'copy'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      // 只绘制源,覆盖目标
    }


    function xor () {
      //矩形一蓝色
      context.save();
      console.log(123213);
      context.fillStyle = 'rgba(63,169,245)';
      context.fillRect(50, 50, 100, 100);

      context.globalCompositeOperation = 'xor'
      //矩形二粉色
      context.fillStyle = 'rgba(255,123,172)';
      context.fillRect(100, 100, 100, 100);
      context.restore();
      //重叠部分变透明
    }

  </script>
</body>

</html>