<template>
    <Layout>
        <!-- 在导航栏右侧添加色彩选择器 -->
        <template #nav-bar-content-after>
            <div class="nav-color-picker">
                <ColorPicker />
            </div>
        </template>
    </Layout>
</template>

<script>
    import DefaultTheme from 'vitepress/theme'
    import ColorPicker from './components/ColorPicker.vue'

    const { Layout } = DefaultTheme

    export default {
        name: 'CustomLayout',
        components: {
            Layout,
            ColorPicker,
        },
    }
</script>

<style scoped>
    .nav-color-picker {
        display: flex;
        align-items: center;
        margin-left: 12px;
    }

    @media (max-width: 768px) {
        .nav-color-picker {
            margin-left: 8px;
        }
    }
</style>
