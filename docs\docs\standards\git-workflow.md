# Git工作流

本文档详细介绍了我们团队在项目开发中采用的Git工作流程，包括分支策略、合并流程和常见操作指南，旨在规范开发过程，提高协作效率。

## Git Flow工作流

我们采用基于 [Git Flow](https://nvie.com/posts/a-successful-git-branching-model/) 的工作流模型，该模型定义了严格的分支结构和发布流程。

### 核心分支

- **master/main**: 主分支，永远保持可部署状态，只接受来自`release`和`hotfix`分支的合并
- **develop**: 开发分支，包含最新的开发代码，作为功能分支的集成点

### 辅助分支

- **feature/xxx**: 功能分支，用于开发新功能
- **release/xxx**: 发布分支，用于版本发布前的准备工作
- **hotfix/xxx**: 热修复分支，用于修复生产环境的紧急问题
- **bugfix/xxx**: 修复分支，用于修复开发中的非紧急问题

## 详细工作流程

### 1. 功能开发流程

```
      功能开发
        ↓
develop → feature/xxx → 开发完成 → 提交PR → 代码审查 → 合并到develop
```

1. 从`develop`分支创建功能分支：
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/user-avatar
   ```

2. 在功能分支上进行开发，定期提交：
   ```bash
   git add .
   git commit -m "feat(user): 添加用户头像上传功能"
   ```

3. 保持与`develop`分支同步：
   ```bash
   git fetch origin
   git merge origin/develop
   # 解决冲突（如有）
   ```

4. 功能开发完成后，推送到远程仓库：
   ```bash
   git push origin feature/user-avatar
   ```

5. 创建Pull Request到`develop`分支，等待代码审查
6. 代码审查通过后，合并到`develop`分支
7. 删除功能分支：
   ```bash
   git branch -d feature/user-avatar
   ```

### 2. 版本发布流程

```
develop → release/vX.Y.Z → 修复 → 合并到master → 打标签 → 合并到develop
```

1. 从`develop`分支创建发布分支：
   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.2.0
   ```

2. 在发布分支上进行版本准备工作（修改版本号、文档等）：
   ```bash
   # 修改版本号
   git add .
   git commit -m "chore(release): 准备v1.2.0发布"
   ```

3. 进行最终修复，修复发现的问题：
   ```bash
   git commit -m "fix(xxx): 修复xxx问题"
   ```

4. 完成后，合并到`master`分支：
   ```bash
   git checkout master
   git merge --no-ff release/v1.2.0
   git tag -a v1.2.0 -m "v1.2.0"
   git push origin master --tags
   ```

5. 同时合并到`develop`分支：
   ```bash
   git checkout develop
   git merge --no-ff release/v1.2.0
   git push origin develop
   ```

6. 删除发布分支：
   ```bash
   git branch -d release/v1.2.0
   ```

### 3. 热修复流程

```
master → hotfix/xxx → 修复完成 → 合并到master → 打标签 → 合并到develop
```

1. 从`master`分支创建热修复分支：
   ```bash
   git checkout master
   git pull
   git checkout -b hotfix/critical-auth-issue
   ```

2. 修复问题：
   ```bash
   git commit -m "fix(auth): 修复认证失败问题"
   ```

3. 修复完成后，合并到`master`分支：
   ```bash
   git checkout master
   git merge --no-ff hotfix/critical-auth-issue
   git tag -a v1.2.1 -m "v1.2.1"
   git push origin master --tags
   ```

4. 同时合并到`develop`分支：
   ```bash
   git checkout develop
   git merge --no-ff hotfix/critical-auth-issue
   git push origin develop
   ```

5. 删除热修复分支：
   ```bash
   git branch -d hotfix/critical-auth-issue
   ```

## 常见Git操作指南

### 撤销本地修改

```bash
# 撤销工作区修改
git checkout -- <file>

# 撤销暂存区修改
git reset HEAD <file>
git checkout -- <file>

# 撤销最近一次提交
git reset --soft HEAD~1
```

### 处理冲突

1. 当合并或变基操作遇到冲突时：
   ```bash
   # 查看冲突文件
   git status
   
   # 手动编辑解决冲突
   # 冲突标记：
   # <<<<<<< HEAD (当前更改)
   # 你的代码
   # =======
   # 其他分支的代码
   # >>>>>>> branch-name (传入的更改)
   ```

2. 解决冲突后：
   ```bash
   git add <resolved-file>
   git commit # 或继续合并/变基操作
   ```

### 临时保存工作

```bash
# 保存当前工作进度
git stash save "正在开发登录功能"

# 查看保存的工作进度
git stash list

# 恢复最近的工作进度
git stash pop

# 恢复指定的工作进度
git stash apply stash@{1}

# 清理所有工作进度
git stash clear
```

## 最佳实践

1. **频繁提交**：每完成一个小功能点就提交一次，保持提交粒度适中
2. **定期同步**：每天至少同步一次主分支的更新
3. **有意义的提交信息**：遵循提交信息规范，清晰描述变更内容
4. **不提交生成的文件**：确保`.gitignore`配置正确
5. **保护主分支**：`master`和`develop`分支应设置保护，只能通过PR合并
6. **使用SSH密钥**：避免频繁输入密码，提高安全性

## 常见问题与解决方案

### Q: 如何处理已经提交但需要修改的代码？

A: 使用`git commit --amend`修改最近一次提交，或使用`git rebase -i HEAD~n`修改多个提交。注意：不要修改已经推送到远程仓库的提交。

### Q: 合并时出现大量冲突怎么办？

A: 考虑使用以下策略：
- 小步合并：将大的功能分支拆分成多个小分支逐步合并
- 使用工具：如VS Code、IntelliJ等IDE提供的冲突解决工具
- 与代码作者协商：对于复杂冲突，与相关代码的作者一起解决

### Q: 如何回滚到之前的版本？

A: 使用`git revert`创建新提交来撤销之前的更改，或在紧急情况下使用`git reset --hard <commit-id>`（谨慎使用，会丢失历史）。

## 工具推荐

- **SourceTree**：直观的Git图形界面工具
- **GitLens**：VS Code插件，增强Git功能
- **Git Graph**：可视化提交历史和分支结构
- **GitKraken**：功能强大的Git客户端

## 总结

良好的Git工作流可以显著提高团队协作效率，减少集成冲突，保持代码库的健康状态。每位团队成员都应熟悉并严格遵循本文档中的工作流程和最佳实践。如有任何问题或改进建议，请在团队会议中提出讨论。 