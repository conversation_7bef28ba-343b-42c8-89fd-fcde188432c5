import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"智慧水务监控系统","description":"","frontmatter":{},"headers":[],"relativePath":"cesium/examples/water-monitor.md","filePath":"cesium/examples/water-monitor.md"}'),p={name:"cesium/examples/water-monitor.md"},o=l("",14),e=[o];function t(c,F,r,D,y,i){return n(),a("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
