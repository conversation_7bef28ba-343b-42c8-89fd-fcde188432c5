# v-loading

为元素添加加载状态。

## 使用场景

- 数据加载过程中的状态展示
- 按钮点击后的加载状态

## 基本用法

```vue
<template>
  <div v-loading="isLoading" class="loading-container">
    <!-- 内容 -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false
    }
  },
  methods: {
    fetchData() {
      this.isLoading = true;
      // 异步请求
      setTimeout(() => {
        this.isLoading = false;
      }, 2000);
    }
  }
}
</script>
```

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directives/loading.js
export default {
  bind(el, binding) {
    // 创建加载遮罩元素
    const loadingEl = document.createElement('div');
    loadingEl.className = 'v-loading-mask';
    
    // 设置遮罩样式
    loadingEl.style.position = 'absolute';
    loadingEl.style.top = '0';
    loadingEl.style.left = '0';
    loadingEl.style.right = '0';
    loadingEl.style.bottom = '0';
    loadingEl.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
    loadingEl.style.display = 'flex';
    loadingEl.style.justifyContent = 'center';
    loadingEl.style.alignItems = 'center';
    loadingEl.style.zIndex = '1000';
    
    // 创建加载图标
    const spinner = document.createElement('div');
    spinner.className = 'v-loading-spinner';
    spinner.innerHTML = `
      <svg viewBox="0 0 50 50" class="circular">
        <circle cx="25" cy="25" r="20" fill="none" stroke="#409EFF" stroke-width="3" stroke-linecap="round" class="path"></circle>
      </svg>
    `;
    
    // 设置加载图标样式
    spinner.style.width = '32px';
    spinner.style.height = '32px';
    
    // 添加动画样式
    const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = `
      .v-loading-spinner .circular {
        width: 100%;
        height: 100%;
        animation: v-loading-rotate 2s linear infinite;
      }
      .v-loading-spinner .path {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: 0;
        stroke-linecap: round;
        animation: v-loading-dash 1.5s ease-in-out infinite;
      }
      @keyframes v-loading-rotate {
        to {
          transform: rotate(360deg);
        }
      }
      @keyframes v-loading-dash {
        0% {
          stroke-dasharray: 1, 150;
          stroke-dashoffset: 0;
        }
        50% {
          stroke-dasharray: 90, 150;
          stroke-dashoffset: -35;
        }
        100% {
          stroke-dasharray: 90, 150;
          stroke-dashoffset: -124;
        }
      }
    `;
    
    // 将样式添加到文档头
    document.head.appendChild(style);
    
    // 将加载图标添加到遮罩
    loadingEl.appendChild(spinner);
    
    // 保存遮罩元素，用于后续更新
    el._loadingElement = loadingEl;
    
    // 设置元素定位，如果不是absolute或relative，则设为relative
    const position = getComputedStyle(el).position;
    if (position !== 'absolute' && position !== 'relative' && position !== 'fixed') {
      el.style.position = 'relative';
    }
    
    // 根据初始值显示或隐藏加载状态
    if (binding.value) {
      el.appendChild(loadingEl);
    }
  },
  
  update(el, binding) {
    // 当值变化时，更新加载状态
    if (binding.value !== binding.oldValue) {
      if (binding.value) {
        // 显示加载状态
        el.appendChild(el._loadingElement);
      } else {
        // 隐藏加载状态
        if (el.contains(el._loadingElement)) {
          el.removeChild(el._loadingElement);
        }
      }
    }
  },
  
  unbind(el) {
    // 清理工作
    if (el._loadingElement && el.contains(el._loadingElement)) {
      el.removeChild(el._loadingElement);
    }
    delete el._loadingElement;
  }
};
```
</details>
