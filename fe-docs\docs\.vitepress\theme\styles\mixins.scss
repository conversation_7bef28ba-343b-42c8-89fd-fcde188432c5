// ===== SCSS Mixins =====
@use 'sass:map';
@use './variables' as vars;

// 响应式断点 Mixin
@mixin respond-to($breakpoint) {
  @if map.has-key(vars.$breakpoints, $breakpoint) {
    @media (max-width: map.get(vars.$breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 阴影 Mixin
@mixin shadow($size: md, $dark: false) {
  $shadow-map: if($dark, vars.$dark-shadows, vars.$shadows);
  @if map.has-key($shadow-map, $size) {
    box-shadow: map.get($shadow-map, $size);
  } @else {
    @warn "Unknown shadow size: #{$size}.";
  }
}

// 渐变背景 Mixin
@mixin gradient-bg($color1, $color2, $direction: 135deg) {
  background: linear-gradient(#{$direction}, #{$color1}, #{$color2});
}

// 品牌色渐变 Mixin
@mixin brand-gradient($direction: 135deg) {
  @include gradient-bg(
    map.get(vars.$brand-colors, primary),
    map.get(vars.$brand-colors, light),
    $direction
  );
}

// 渐变文字 Mixin
@mixin gradient-text($color1, $color2, $direction: 135deg) {
  background: linear-gradient(#{$direction}, #{$color1}, #{$color2});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 品牌色渐变文字 Mixin
@mixin brand-gradient-text($direction: 135deg) {
  @include gradient-text(
    var(--vp-c-text-1),
    var(--vp-c-brand),
    $direction
  );
}

// 悬停效果 Mixin
@mixin hover-lift($distance: 4px) {
  transition: transform map.get(vars.$transitions, normal);

  &:hover {
    transform: translateY(-#{$distance});
  }
}

// 按钮样式 Mixin
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: map.get(vars.$border-radius, md);
  font-weight: map.get(vars.$font-weights, semibold);
  cursor: pointer;
  transition: all map.get(vars.$transitions, normal);
  text-decoration: none;

  &:focus {
    outline: none;
  }
}

@mixin button-primary {
  @include button-base;
  @include brand-gradient;
  color: white;
  @include shadow(md);
  
  &:hover {
    @include hover-lift(2px);
    @include shadow(lg);
  }
}

@mixin button-secondary {
  @include button-base;
  background: transparent;
  border: 2px solid var(--vp-c-brand);
  color: var(--vp-c-brand);
  
  &:hover {
    background: var(--vp-c-brand);
    color: white;
    @include hover-lift(2px);
  }
}

// 卡片样式 Mixin
@mixin card-base {
  border-radius: map.get(vars.$border-radius, xl);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  @include shadow(sm);
  transition: all map.get(vars.$transitions, normal);

  .dark & {
    background: rgba(30, 41, 59, 0.6);
    border: 1px solid rgba(71, 85, 105, 0.3);
  }
}

@mixin card-hover {
  @include hover-lift;
  @include shadow(lg);
  background: rgba(255, 255, 255, 0.95);
  
  .dark & {
    background: rgba(30, 41, 59, 0.8);
  }
}

// 装饰性边框 Mixin
@mixin decorative-border($position: left, $width: 3px) {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    #{$position}: 0;
    top: 50%;
    transform: translateY(-50%);
    width: if($position == left or $position == right, $width, 100%);
    height: if($position == top or $position == bottom, $width, 20px);
    background: var(--vp-c-brand);
    border-radius: map.get(vars.$border-radius, sm);
  }
}

// 毛玻璃效果 Mixin
@mixin glass-effect($opacity: 0.8) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  
  .dark & {
    background: rgba(30, 41, 59, $opacity);
  }
}

// 文字截断 Mixin
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 居中对齐 Mixin
@mixin center($direction: both) {
  display: flex;
  
  @if $direction == both {
    align-items: center;
    justify-content: center;
  } @else if $direction == horizontal {
    justify-content: center;
  } @else if $direction == vertical {
    align-items: center;
  }
}

// 绝对居中 Mixin
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 清除浮动 Mixin
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}
