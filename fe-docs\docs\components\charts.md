# 图表组件

  基础图表的组件，基于ECharts封装，具有以下特性

- 统一的组件接口和使用方式
- 响应式设计，自适应容器尺寸
- 主题定制，符合项目整体设计风格
- 交互增强，支持点击、缩放等高级交互
- 性能优化，适合大数据量展示

## 基础图表组件

### BaseChart 基础图表

`BaseChart`是所有图表组件的基础，提供了图表渲染、事件处理、尺寸调整等通用功能。

#### 属性

| 属性名         | 类型             | 默认值   | 说明                                     |
|--------------|-----------------|---------|----------------------------------------|
| options      | Object          | {}      | ECharts配置项，与ECharts官方配置项保持一致   |
| width        | String, Number  | '100%'  | 图表宽度，支持CSS尺寸和数字（px）           |
| height       | String, Number  | '300px' | 图表高度，支持CSS尺寸和数字（px）           |
| theme        | String          | 'default' | 使用的主题，可选值：'default'/'dark'       |
| loading      | Boolean         | false   | 是否显示加载状态                           |
| autoResize   | Boolean         | true    | 是否自动适应容器大小                       |
| notMerge     | Boolean         | false   | 更新图表时是否不与之前的配置合并              |
| renderer     | String          | 'canvas' | 渲染模式，可选值：'canvas'/'svg'           |

#### 事件

| 事件名称         | 参数                           | 说明                              |
|---------------|-------------------------------|----------------------------------|
| chart-click   | ECharts的点击事件参数             | 图表点击事件                       |
| chart-dblclick| ECharts的双击事件参数             | 图表双击事件                       |
| chart-ready   | ECharts实例                    | 图表实例已准备好可供操作               |
| chart-resize  | width, height                 | 图表尺寸发生变化                     |

#### 方法

通过`ref`可以调用以下方法：

| 方法名         | 参数              | 返回值            | 说明                       |
|--------------|-------------------|------------------|---------------------------|
| getChart     | -                 | ECharts实例       | 获取ECharts实例            |
| resize       | -                 | -                | 重新调整图表大小            |
| showLoading  | -                 | -                | 显示加载动画                |
| hideLoading  | -                 | -                | 隐藏加载动画                |
| setOption    | options, notMerge | -                | 设置图表配置项              |

#### 使用示例

```vue
<template>
  <base-chart 
    :options="chartOptions" 
    :height="300" 
    :loading="loading"
    @chart-click="handleChartClick"
  />
</template>

<script>
import { BaseChart } from '@/components/charts'

export default {
  components: {
    BaseChart
  },
  data() {
    return {
      loading: false,
      chartOptions: {
        title: {
          text: '基础图表示例'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '销量',
            type: 'line',
            data: [150, 230, 224, 218, 135, 147, 260]
          }
        ]
      }
    }
  },
  methods: {
    handleChartClick(params) {
      console.log('点击了图表：', params)
    }
  }
}
</script>
```  



#### 注册和使用

全局注册：

```js
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import BaseChart from '@/components/charts/BaseChart.vue'

const app = createApp(App)
app.component('BaseChart', BaseChart)
app.mount('#app')
```

按需导入：

```js
// 在组件中按需导入
import BaseChart from '@/components/charts/BaseChart.vue'

export default {
  components: {
    BaseChart
  }
  // ...
}
```

## 完整示例

### 折线图示例

```vue
<template>
  <div class="chart-demo">
    <h3>销售趋势分析</h3>
    <base-chart 
      :options="lineChartOptions" 
      height="350"
      @chart-click="handleChartClick"
    />
  </div>
</template>

<script>
import { BaseChart } from '@/components/charts'

export default {
  components: {
    BaseChart
  },
  data() {
    return {
      lineChartOptions: {
        color: ['#5470c6', '#91cc75', '#ee6666'],
        title: {
          text: '月度销售趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['电子产品', '服装', '食品'],
          bottom: '0%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: {
          type: 'value',
          name: '销售额(万元)'
        },
        series: [
          {
            name: '电子产品',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101, 134, 90, 230]
          },
          {
            name: '服装',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191, 234, 290, 330]
          },
          {
            name: '食品',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [150, 232, 201, 154, 190, 330]
          }
        ]
      }
    }
  },
  methods: {
    handleChartClick(params) {
      console.log('点击了图表项：', params)
      this.$emit('data-selected', params)
    }
  }
}
</script>

<style scoped>
.chart-demo {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
```

## 图表交互

### 图表事件处理

```vue
<template>
  <div>
    <base-chart 
      ref="chart"
      :options="options"
      @chart-click="handleChartClick"
      @chart-dblclick="handleChartDblClick"
      @chart-ready="handleChartReady"
    />
    
    <div class="event-log">
      <h4>事件日志：</h4>
      <ul>
        <li v-for="(log, index) in eventLogs" :key="index">
          {{ log }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { BaseChart } from '@/components/charts'

export default {
  components: {
    BaseChart
  },
  data() {
    return {
      options: {
        // 图表配置...
      },
      eventLogs: [],
      chartInstance: null
    }
  },
  methods: {
    handleChartClick(params) {
      this.eventLogs.push(`点击事件：${params.name || ''}，值：${params.value || ''}`)
      // 自定义交互逻辑
    },
    
    handleChartDblClick(params) {
      this.eventLogs.push(`双击事件：${params.name || ''}`)
      // 自定义交互逻辑
    },
    
    handleChartReady(chart) {
      this.chartInstance = chart
      this.eventLogs.push('图表已加载完成')
      
      // 可以在这里对图表实例进行进一步配置
      chart.on('mouseover', params => {
        this.eventLogs.push(`鼠标悬停：${params.name || ''}`)
      })
    }
  }
}
</script>

<style scoped>
.event-log {
  margin-top: 20px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 10px;
}
</style>
```

### 动态数据更新

```vue
<template>
  <div>
    <base-chart 
      ref="dynamicChart"
      :options="dynamicOptions"
      height="350"
    />
    
    <div class="controls">
      <button @click="addDataPoint">添加数据点</button>
      <button @click="removeDataPoint">移除数据点</button>
      <button @click="startAutoUpdate">{{ isUpdating ? '停止' : '开始' }}自动更新</button>
    </div>
  </div>
</template>

<script>
import { BaseChart } from '@/components/charts'

export default {
  components: {
    BaseChart
  },
  data() {
    return {
      dynamicOptions: {
        title: {
          text: '动态数据更新示例'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [],
          type: 'line',
          smooth: true,
          areaStyle: {}
        }]
      },
      xData: [],
      yData: [],
      updateInterval: null,
      isUpdating: false,
      counter: 0
    }
  },
  mounted() {
    // 初始化一些数据点
    for (let i = 0; i < 5; i++) {
      this.addDataPoint()
    }
  },
  beforeUnmount() {
    this.stopAutoUpdate()
  },
  methods: {
    addDataPoint() {
      this.counter++
      this.xData.push('Point ' + this.counter)
      this.yData.push(Math.floor(Math.random() * 100))
      
      this.updateChart()
    },
    
    removeDataPoint() {
      if (this.xData.length > 0) {
        this.xData.shift()
        this.yData.shift()
        this.updateChart()
      }
    },
    
    updateChart() {
      const chart = this.$refs.dynamicChart.getChart()
      if (chart) {
        chart.setOption({
          xAxis: {
            data: this.xData
          },
          series: [{
            data: this.yData
          }]
        })
      }
    },
    
    startAutoUpdate() {
      if (this.isUpdating) {
        this.stopAutoUpdate()
      } else {
        this.isUpdating = true
        this.updateInterval = setInterval(() => {
          this.addDataPoint()
          // 保持最多10个数据点
          if (this.xData.length > 10) {
            this.removeDataPoint()
          }
        }, 2000)
      }
    },
    
    stopAutoUpdate() {
      clearInterval(this.updateInterval)
      this.isUpdating = false
    }
  }
}
</script>

<style scoped>
.controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

button {
  padding: 6px 12px;
  background-color: #4c9afa;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #3182f6;
}
</style>
``` 

## 源码实现

<details>
<summary>源码实现</summary>

```js
// components/charts/BaseChart.vue
<template>
  <div 
    class="base-chart-container"
    :style="containerStyle"
    ref="chartContainer"
  >
    <div class="chart-loading" v-if="loading">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  VisualMapComponent
} from 'echarts/components'
import {
  LineChart,
  BarChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart,
  MapChart
} from 'echarts/charts'
import { CanvasRenderer, SVGRenderer } from 'echarts/renderers'
import { debounce } from 'lodash-es'

// 注册全局组件和渲染器
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  VisualMapComponent,
  LineChart,
  BarChart,
  PieChart,
  ScatterChart,
  RadarChart,
  GaugeChart,
  MapChart,
  CanvasRenderer,
  SVGRenderer
])

export default {
  name: 'BaseChart',
  
  props: {
    options: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '300px'
    },
    theme: {
      type: String,
      default: 'default'
    },
    loading: {
      type: Boolean,
      default: false
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    notMerge: {
      type: Boolean,
      default: false
    },
    renderer: {
      type: String,
      default: 'canvas',
      validator: value => ['canvas', 'svg'].includes(value)
    }
  },
  
  data() {
    return {
      chart: null,
      resizeObserver: null,
      _resizeHandler: null
    }
  },
  
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height,
        position: 'relative'
      }
    }
  },
  
  watch: {
    options: {
      handler(newOptions) {
        if (this.chart) {
          this.chart.setOption(newOptions, this.notMerge)
        }
      },
      deep: true
    },
    loading(val) {
      if (this.chart) {
        val ? this.chart.showLoading() : this.chart.hideLoading()
      }
    }
  },
  
  mounted() {
    this.initChart()
    
    if (this.autoResize) {
      this._resizeHandler = debounce(() => {
        if (this.chart) {
          this.chart.resize()
          this.$emit('chart-resize', 
            this.$refs.chartContainer.clientWidth, 
            this.$refs.chartContainer.clientHeight
          )
        }
      }, 100)
      
      // 使用ResizeObserver监听容器大小变化
      if (typeof ResizeObserver !== 'undefined') {
        this.resizeObserver = new ResizeObserver(this._resizeHandler)
        this.resizeObserver.observe(this.$refs.chartContainer)
      }
      
      window.addEventListener('resize', this._resizeHandler)
    }
  },
  
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    
    if (this._resizeHandler) {
      window.removeEventListener('resize', this._resizeHandler)
    }
    
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }
  },
  
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      // 销毁旧实例
      if (this.chart) {
        this.chart.dispose()
      }
      
      // 创建新实例
      this.chart = echarts.init(
        this.$refs.chartContainer,
        this.theme,
        { renderer: this.renderer }
      )
      
      // 绑定事件
      this.chart.on('click', params => this.$emit('chart-click', params))
      this.chart.on('dblclick', params => this.$emit('chart-dblclick', params))
      
      // 设置图表配置
      if (this.options) {
        this.chart.setOption(this.options)
      }
      
      // 设置加载状态
      if (this.loading) {
        this.chart.showLoading()
      }
      
      // 通知图表已准备好
      this.$emit('chart-ready', this.chart)
    },
    
    getChart() {
      return this.chart
    },
    
    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    
    showLoading() {
      if (this.chart) {
        this.chart.showLoading()
      }
    },
    
    hideLoading() {
      if (this.chart) {
        this.chart.hideLoading()
      }
    },
    
    setOption(options, notMerge = false) {
      if (this.chart) {
        this.chart.setOption(options, notMerge)
      }
    }
  }
}
</script>

<style scoped>
.base-chart-container {
  width: 100%;
  height: 100%;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 100;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
```
</details>