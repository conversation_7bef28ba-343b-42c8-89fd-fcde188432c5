import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"Vue.js模块化开发最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/modular-development.md","filePath":"best-practices/modular-development.md"}'),p={name:"best-practices/modular-development.md"},o=l("",75),e=[o];function t(c,r,D,y,F,i){return n(),a("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
