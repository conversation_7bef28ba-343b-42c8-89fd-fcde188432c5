<template>
	<div id="app">
		<el-button type="primary" @click="sendData">给基座传递数据</el-button>
		<container class="content"></container>
		<!-- <toolbar class="toobar"></toolbar> -->
	</div>
</template>

<script>
import toolbar from "./components/toolbar.vue";
import container from "./components/container.vue";
export default {
	name: "App",
	components: {
		toolbar,
		container,
	},
	methods: {
		sendData() {
			window.microApp.dispatch({ title: "子应用发送的数据" });
		},
	},
	mounted() {
		if (!window.microApp) return;
		//接受来自基座的数据
		window.microApp.addDataListener((data) => {
			console.log("来自基座的数据", data);
		}, true);
	},
};
</script>

<style lang="less">
* {
	padding: 0;
	margin: 0;
}
#app {
	overflow: hidden;
	height: 100vh;
}
</style>
