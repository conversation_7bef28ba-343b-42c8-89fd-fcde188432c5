# v-tableHeight

该指令用于Element UI的Table组件，使表格高度能够自适应浏览器视窗高度，根据表格在页面中的位置动态计算并设置表格高度，避免出现多余的滚动条或空白区域。

## 使用场景

- 需要表格自动填充剩余视窗高度的场景
- 动态内容区域中的表格展示
- 需要在不同屏幕尺寸下保持表格合适高度的场景
- 搜索表单展开收起时表格高度需要重新调整的场景

## 基本用法

```vue
<template>
  <div class="container" v-tableHeight>
    <el-table
      height="100%"
      :data="tableData"
      border
    >
      <el-table-column prop="date" label="日期" width="180"></el-table-column>
      <el-table-column prop="name" label="姓名" width="180"></el-table-column>
      <el-table-column prop="address" label="地址"></el-table-column>
    </el-table>
  </div>
</template>
```

## 带参数用法

```vue
<template>
  <div class="container"  v-tableHeight="{ customHeight: 100 }">
    <el-table
      height="100%"
      :data="tableData"
      border
    >
      <el-table-column prop="date" label="日期" width="180"></el-table-column>
      <el-table-column prop="name" label="姓名" width="180"></el-table-column>
      <el-table-column prop="address" label="地址"></el-table-column>
    </el-table>
  </div>
</template>
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| customHeight | Number | 76 | 表格底部预留高度（单位：px），通常用于分页组件等元素占用的空间 |

## 实现原理

该指令通过以下步骤实现表格高度自适应：

1. **获取表格位置**：使用 `getBoundingClientRect().top` 获取表格距离视窗顶部的距离
2. **计算可用高度**：`window.innerHeight - 表格top位置 - 预留高度`
3. **设置表格高度**：直接设置表格元素的 `style.height`
4. **监听变化**：使用 Element UI 提供的 resize-event 工具监听页面尺寸变化

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directive/tableHeight/index.js
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event";

// 设置表格高度
const doResize = (el, binding) => {
  // 获取调用传递过来的数据
  const { value } = binding;
  // 获取距底部距离（用于展示页码等信息）
  const customHeight = (value && value.customHeight) || 76;
  // 计算列表高度
  const height =
    window.innerHeight - el.getBoundingClientRect().top - customHeight;
  // 设置高度
  el.style.height = height + "px";
};

export default {
  // 初始化设置
  bind(el, binding) {
    // 设置resize监听方法
    el.resizeListener = () => {
      doResize(el, binding);
    };
    // 绑定监听方法到addResizeListener
    addResizeListener(window.document.body, el.resizeListener);
  },
  // 所在组件的 VNode 更新时设置
  // 页面上搜索表单是可以展开收起的，当展开更多表单搜索时，表格高度没变
  update(el, binding) {
    doResize(el, binding);
  },
  // 销毁时设置
  unbind(el) {
    // 移除resize监听
    removeResizeListener(window.document.body, el.resizeListener);
  },
};
```
</details>

## 注意事项

1. **依赖 Element UI**：该指令使用了 Element UI 提供的 `resize-event` 工具，需要确保项目中已安装 Element UI
2. **高度计算方式**：表格高度 = 视窗高度 - 表格顶部位置 - 预留高度
3. **自动更新**：当页面内容发生变化（如搜索表单展开收起）时，指令会自动重新计算表格高度
4. **内存清理**：组件销毁时会自动移除事件监听，避免内存泄漏

## 与其他组件配合使用

该指令特别适合与以下场景配合使用：

- **分页组件**：设置 `customHeight` 为分页组件的高度
- **搜索表单**：表单展开收起时会自动调整表格高度
- **操作按钮区域**：预留操作按钮所需的空间
