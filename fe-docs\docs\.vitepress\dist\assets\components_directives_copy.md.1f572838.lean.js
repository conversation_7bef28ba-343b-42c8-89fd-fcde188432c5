import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"v-copy","description":"","frontmatter":{},"headers":[],"relativePath":"components/directives/copy.md","filePath":"components/directives/copy.md"}'),p={name:"components/directives/copy.md"},o=l("",8),e=[o];function t(c,r,F,y,D,i){return n(),a("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
