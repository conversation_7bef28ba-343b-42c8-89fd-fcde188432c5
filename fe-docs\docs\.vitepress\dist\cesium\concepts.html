<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>核心概念 | 前端技术开发文档</title>
    <meta name="description" content="A VitePress site">
    <link rel="preload stylesheet" href="/assets/style.5ccb9172.css" as="style">
    <script type="module" src="/assets/app.473eac78.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.2ed14f66.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="modulepreload" href="/assets/chunks/framework.3d729ebc.js">
  <link rel="modulepreload" href="/assets/chunks/theme.533e1ce4.js">
  <link rel="modulepreload" href="/assets/cesium_concepts.md.608db15b.lean.js">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="apple-touch-icon" href="/logo.png">
  <meta name="theme-color" content="#0ea5e9">
  <script id="check-dark-light">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-21678b25 data-v-b2cf3e0b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-c8616af1></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-c8616af1> Skip to content </a><!--]--><!----><header class="VPNav" data-v-b2cf3e0b data-v-7e5bc4a5><div class="VPNavBar has-sidebar" data-v-7e5bc4a5 data-v-94c81dcc><div class="container" data-v-94c81dcc><div class="title" data-v-94c81dcc><div class="VPNavBarTitle has-sidebar" data-v-94c81dcc data-v-f4ef19a3><a class="title" href="/" data-v-f4ef19a3><!--[--><!--]--><!--[--><img class="VPImage logo" src="/logo.jpeg" alt data-v-6db2186b><!--]--><!--[-->前端技术开发文档<!--]--><!--[--><!--]--></a></div></div><div class="content" data-v-94c81dcc><div class="curtain" data-v-94c81dcc></div><div class="content-body" data-v-94c81dcc><!--[--><!--]--><div class="VPNavBarSearch search" style="--vp-meta-key:&#39;Meta&#39;;" data-v-94c81dcc><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><svg class="DocSearch-Search-Icon" width="20" height="20" viewBox="0 0 20 20" aria-label="search icon"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-94c81dcc data-v-7f418b0f><span id="main-nav-aria-label" class="visually-hidden" data-v-7f418b0f>Main Navigation</span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->首页<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/guide/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->指南<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/components/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->组件<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/best-practices/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->最佳实践<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/standards/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->规范标准<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/tools/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->工具配置<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/cesium/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->Cesium<!--]--><!----></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-94c81dcc data-v-f6a63727><label title="toggle dark mode" data-v-f6a63727 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div><!----><div class="VPFlyout VPNavBarExtra extra" data-v-94c81dcc data-v-40855f84 data-v-764effdf><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-764effdf><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="icon" data-v-764effdf><circle cx="12" cy="12" r="2"></circle><circle cx="19" cy="12" r="2"></circle><circle cx="5" cy="12" r="2"></circle></svg></button><div class="menu" data-v-764effdf><div class="VPMenu" data-v-764effdf data-v-e7ea1737><!----><!--[--><!--[--><!----><div class="group" data-v-40855f84><div class="item appearance" data-v-40855f84><p class="label" data-v-40855f84>Appearance</p><div class="appearance-action" data-v-40855f84><label title="toggle dark mode" data-v-40855f84 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div></div></div><!----><!--]--><!--]--></div></div></div><!--[--><!--[--><!--[--><div class="nav-color-picker" data-v-21678b25><div class="color-picker" data-v-21678b25 data-v-917787cc><button class="color-picker-trigger" title="打开色彩选择器" data-v-917787cc><div class="palette-icon" data-v-917787cc><div class="palette-circle" data-v-917787cc></div><div class="palette-colors" data-v-917787cc><div class="color-dot color-1" data-v-917787cc></div><div class="color-dot color-2" data-v-917787cc></div><div class="color-dot color-3" data-v-917787cc></div><div class="color-dot color-4" data-v-917787cc></div></div></div></button><!----><!----></div></div><!--]--><!--]--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-94c81dcc data-v-e5dd9c1c><span class="container" data-v-e5dd9c1c><span class="top" data-v-e5dd9c1c></span><span class="middle" data-v-e5dd9c1c></span><span class="bottom" data-v-e5dd9c1c></span></span></button></div></div></div></div><!----></header><div class="VPLocalNav" data-v-b2cf3e0b data-v-392e1bf8><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-392e1bf8><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="menu-icon" data-v-392e1bf8><path d="M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"></path><path d="M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"></path><path d="M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"></path><path d="M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"></path></svg><span class="menu-text" data-v-392e1bf8>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-392e1bf8 data-v-079b16a8><button data-v-079b16a8>Return to top</button><!----></div></div><aside class="VPSidebar" data-v-b2cf3e0b data-v-af16598e><div class="curtain" data-v-af16598e></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-af16598e><span class="visually-hidden" id="sidebar-aria-label" data-v-af16598e> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 has-active" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>Cesium 3D地图引擎</h2><!----></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>简介</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/basics.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>基础配置</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link is-active has-active" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/concepts.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>核心概念</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/operations.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>常用操作</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 collapsible" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>实战案例</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-c4656e6d><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="caret-icon" data-v-c4656e6d><path d="M9,19c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l5.3-5.3L8.3,6.7c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l6,6c0.4,0.4,0.4,1,0,1.4l-6,6C9.5,18.9,9.3,19,9,19z"></path></svg></div></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/examples/water-monitor.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>智慧水务监控</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-b2cf3e0b data-v-a494bd1d><div class="VPDoc has-sidebar has-aside" data-v-a494bd1d data-v-c4b0d3cf><!--[--><!--]--><div class="container" data-v-c4b0d3cf><div class="aside" data-v-c4b0d3cf><div class="aside-curtain" data-v-c4b0d3cf></div><div class="aside-container" data-v-c4b0d3cf><div class="aside-content" data-v-c4b0d3cf><div class="VPDocAside" data-v-c4b0d3cf data-v-3f215769><!--[--><!--]--><!--[--><!--]--><div class="VPDocAsideOutline" data-v-3f215769 data-v-ff0f39c8><div class="content" data-v-ff0f39c8><div class="outline-marker" data-v-ff0f39c8></div><div class="outline-title" data-v-ff0f39c8>On this page</div><nav aria-labelledby="doc-outline-aria-label" data-v-ff0f39c8><span class="visually-hidden" id="doc-outline-aria-label" data-v-ff0f39c8> Table of Contents for current page </span><ul class="root" data-v-ff0f39c8 data-v-9a431c33><!--[--><!--]--></ul></nav></div></div><!--[--><!--]--><div class="spacer" data-v-3f215769></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-c4b0d3cf><div class="content-container" data-v-c4b0d3cf><!--[--><!--]--><!----><main class="main" data-v-c4b0d3cf><div style="position:relative;" class="vp-doc _cesium_concepts" data-v-c4b0d3cf><div><h1 id="核心概念" tabindex="-1">核心概念 <a class="header-anchor" href="#核心概念" aria-label="Permalink to &quot;核心概念&quot;">​</a></h1><p>理解Cesium的核心概念是掌握3D地图开发的基础。本章将介绍Cesium的主要组件、坐标系统、数据类型等核心知识。</p><h2 id="主要组件" tabindex="-1">主要组件 <a class="header-anchor" href="#主要组件" aria-label="Permalink to &quot;主要组件&quot;">​</a></h2><h3 id="viewer-查看器" tabindex="-1">Viewer（查看器） <a class="header-anchor" href="#viewer-查看器" aria-label="Permalink to &quot;Viewer（查看器）&quot;">​</a></h3><p>Viewer是Cesium应用的主入口，包含了完整的3D地球场景和用户界面控件。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><p><strong>主要属性：</strong></p><ul><li><code>scene</code> - 3D场景</li><li><code>camera</code> - 相机控制</li><li><code>canvas</code> - HTML5 Canvas元素</li><li><code>clock</code> - 时钟控制</li><li><code>entities</code> - 实体集合</li><li><code>dataSources</code> - 数据源集合</li></ul><h3 id="scene-场景" tabindex="-1">Scene（场景） <a class="header-anchor" href="#scene-场景" aria-label="Permalink to &quot;Scene（场景）&quot;">​</a></h3><p>Scene管理3D场景的渲染，包含地球、天空盒、光照等。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> scene </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 场景相关配置</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">              </span><span style="color:#676E95;font-style:italic;">// 显示地球</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">skyBox</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 显示天空盒</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">sun</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">                </span><span style="color:#676E95;font-style:italic;">// 显示太阳</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">moon</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">               </span><span style="color:#676E95;font-style:italic;">// 显示月亮</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">fog</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enabled </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 启用雾效</span></span></code></pre></div><h3 id="camera-相机" tabindex="-1">Camera（相机） <a class="header-anchor" href="#camera-相机" aria-label="Permalink to &quot;Camera（相机）&quot;">​</a></h3><p>Camera控制用户的视角和导航。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> camera </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机位置和方向</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 相机位置（笛卡尔坐标）</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">direction</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">       </span><span style="color:#676E95;font-style:italic;">// 观看方向</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">up</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 向上方向</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">right</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">          </span><span style="color:#676E95;font-style:italic;">// 向右方向</span></span></code></pre></div><h3 id="globe-地球" tabindex="-1">Globe（地球） <a class="header-anchor" href="#globe-地球" aria-label="Permalink to &quot;Globe（地球）&quot;">​</a></h3><p>Globe表示地球几何体，包含地形和影像数据。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> globe </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 地球配置</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">                    </span><span style="color:#676E95;font-style:italic;">// 显示地球</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enableLighting </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">         </span><span style="color:#676E95;font-style:italic;">// 启用光照</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">showWaterEffect </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">         </span><span style="color:#676E95;font-style:italic;">// 显示水体效果</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">shadows </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ShadowMode</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RECEIVE_ONLY</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 阴影模式</span></span></code></pre></div><h2 id="坐标系统" tabindex="-1">坐标系统 <a class="header-anchor" href="#坐标系统" aria-label="Permalink to &quot;坐标系统&quot;">​</a></h2><h3 id="wgs84坐标系" tabindex="-1">WGS84坐标系 <a class="header-anchor" href="#wgs84坐标系" aria-label="Permalink to &quot;WGS84坐标系&quot;">​</a></h3><p>Cesium使用WGS84大地坐标系作为标准参考坐标系。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 经纬度坐标（度）</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> longitude </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 经度</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> latitude </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 纬度</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> height </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 高度（米）</span></span></code></pre></div><h3 id="笛卡尔坐标系" tabindex="-1">笛卡尔坐标系 <a class="header-anchor" href="#笛卡尔坐标系" aria-label="Permalink to &quot;笛卡尔坐标系&quot;">​</a></h3><p>3D空间中的直角坐标系，原点位于地球中心。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 从经纬度转换为笛卡尔坐标</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> cartesian </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(longitude</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> latitude</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> height)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 从笛卡尔坐标转换为经纬度</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> cartographic </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromCartesian</span><span style="color:#BABED8;">(cartesian)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> lon </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDegrees</span><span style="color:#BABED8;">(cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">longitude)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> lat </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDegrees</span><span style="color:#BABED8;">(cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">latitude)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> alt </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">height</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="屏幕坐标系" tabindex="-1">屏幕坐标系 <a class="header-anchor" href="#屏幕坐标系" aria-label="Permalink to &quot;屏幕坐标系&quot;">​</a></h3><p>屏幕上的2D坐标系，原点在左上角。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 世界坐标转屏幕坐标</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> screenPosition </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">SceneTransforms</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">wgs84ToWindowCoordinates</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span></span>
<span class="line"><span style="color:#BABED8;">  cartesian</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 屏幕坐标转世界坐标</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> worldPosition </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pickEllipsoid</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  screenPosition</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span></span>
<span class="line"><span style="color:#BABED8;">  viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ellipsoid</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="实体系统-entity-api" tabindex="-1">实体系统（Entity API） <a class="header-anchor" href="#实体系统-entity-api" aria-label="Permalink to &quot;实体系统（Entity API）&quot;">​</a></h2><h3 id="entity-实体" tabindex="-1">Entity（实体） <a class="header-anchor" href="#entity-实体" aria-label="Permalink to &quot;Entity（实体）&quot;">​</a></h3><p>Entity是Cesium中表示对象的高级API，提供了声明式的图形描述。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建点实体</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> pointEntity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">id</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">myPoint</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">示例点</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="图形类型" tabindex="-1">图形类型 <a class="header-anchor" href="#图形类型" aria-label="Permalink to &quot;图形类型&quot;">​</a></h3><p>Cesium支持多种图形类型：</p><h4 id="点-point" tabindex="-1">点（Point） <a class="header-anchor" href="#点-point" aria-label="Permalink to &quot;点（Point）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">heightReference</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">HeightReference</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CLAMP_TO_GROUND</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="标签-label" tabindex="-1">标签（Label） <a class="header-anchor" href="#标签-label" aria-label="Permalink to &quot;标签（Label）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">label</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">text</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">标签文本</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">14pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="广告牌-billboard" tabindex="-1">广告牌（Billboard） <a class="header-anchor" href="#广告牌-billboard" aria-label="Permalink to &quot;广告牌（Billboard）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">billboard</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">image</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path/to/image.png</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">scale</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">50</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">eyeOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian3</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.0</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">horizontalOrigin</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">HorizontalOrigin</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CENTER</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">verticalOrigin</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">VerticalOrigin</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BOTTOM</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="线-polyline" tabindex="-1">线（Polyline） <a class="header-anchor" href="#线-polyline" aria-label="Permalink to &quot;线（Polyline）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">polyline</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">positions</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegreesArray</span><span style="color:#F07178;">([</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">118.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">41.0</span></span>
<span class="line"><span style="color:#F07178;">  ])</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">width</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">clampToGround</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="多边形-polygon" tabindex="-1">多边形（Polygon） <a class="header-anchor" href="#多边形-polygon" aria-label="Permalink to &quot;多边形（Polygon）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">polygon</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">hierarchy</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegreesArray</span><span style="color:#F07178;">([</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40.0</span></span>
<span class="line"><span style="color:#F07178;">  ])</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLUE</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">withAlpha</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0.5</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outline</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="_3d模型-model" tabindex="-1">3D模型（Model） <a class="header-anchor" href="#_3d模型-model" aria-label="Permalink to &quot;3D模型（Model）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">model</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">uri</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path/to/model.glb</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">scale</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">minimumPixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">128</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">maximumScale</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">20000</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="材质系统" tabindex="-1">材质系统 <a class="header-anchor" href="#材质系统" aria-label="Permalink to &quot;材质系统&quot;">​</a></h2><h3 id="基础材质" tabindex="-1">基础材质 <a class="header-anchor" href="#基础材质" aria-label="Permalink to &quot;基础材质&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 纯色材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 图片材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">ImageMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">image</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path/to/texture.jpg</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">repeat</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">4.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">4.0</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 条纹材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">StripeMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">evenColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">oddColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">repeat</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5.0</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span></code></pre></div><h3 id="动画材质" tabindex="-1">动画材质 <a class="header-anchor" href="#动画材质" aria-label="Permalink to &quot;动画材质&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 发光线材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">PolylineGlowMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">glowPower</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLUE</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 轮廓线材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">PolylineOutlineMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ORANGE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span></code></pre></div><h2 id="数据源系统" tabindex="-1">数据源系统 <a class="header-anchor" href="#数据源系统" aria-label="Permalink to &quot;数据源系统&quot;">​</a></h2><h3 id="datasource-数据源" tabindex="-1">DataSource（数据源） <a class="header-anchor" href="#datasource-数据源" aria-label="Permalink to &quot;DataSource（数据源）&quot;">​</a></h3><p>DataSource是Entity的容器，用于管理大量实体。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建自定义数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> dataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">CustomDataSource</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">myDataSource</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 添加实体到数据源</span></span>
<span class="line"><span style="color:#BABED8;">dataSource</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 将数据源添加到viewer</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(dataSource)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="内置数据源" tabindex="-1">内置数据源 <a class="header-anchor" href="#内置数据源" aria-label="Permalink to &quot;内置数据源&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// GeoJSON数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> geoJsonDataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">GeoJsonDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data.geojson</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(geoJsonDataSource)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// KML数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> kmlDataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">KmlDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data.kml</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(kmlDataSource)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// CZML数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> czmlDataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CzmlDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data.czml</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(czmlDataSource)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="时间系统" tabindex="-1">时间系统 <a class="header-anchor" href="#时间系统" aria-label="Permalink to &quot;时间系统&quot;">​</a></h2><h3 id="juliandate-儒略日" tabindex="-1">JulianDate（儒略日） <a class="header-anchor" href="#juliandate-儒略日" aria-label="Permalink to &quot;JulianDate（儒略日）&quot;">​</a></h3><p>Cesium使用儒略日表示时间。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建当前时间</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> now </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">now</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 从ISO8601字符串创建时间</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> time </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T12:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 时间计算</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> tomorrow </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addDays</span><span style="color:#BABED8;">(now</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">JulianDate</span><span style="color:#BABED8;">())</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="clock-时钟" tabindex="-1">Clock（时钟） <a class="header-anchor" href="#clock-时钟" aria-label="Permalink to &quot;Clock（时钟）&quot;">​</a></h3><p>Clock控制时间的流逝和动画。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> clock </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 设置时间范围</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">startTime </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T00:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">stopTime </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-02T00:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">currentTime </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">startTime</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 设置时钟模式</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">clockRange </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ClockRange</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LOOP_STOP</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 循环播放</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">multiplier </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">3600</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 时间倍速（1小时/秒）</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 启动时钟</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">shouldAnimate </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="时间动态属性" tabindex="-1">时间动态属性 <a class="header-anchor" href="#时间动态属性" aria-label="Permalink to &quot;时间动态属性&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建时间相关的位置属性</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> property </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">SampledPositionProperty</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 添加时间样本</span></span>
<span class="line"><span style="color:#BABED8;">property</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addSample</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T00:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#BABED8;">property</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addSample</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T01:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">40.0</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 将属性应用到实体</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> entity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> property</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="图层系统" tabindex="-1">图层系统 <a class="header-anchor" href="#图层系统" aria-label="Permalink to &quot;图层系统&quot;">​</a></h2><h3 id="imagerylayer-影像图层" tabindex="-1">ImageryLayer（影像图层） <a class="header-anchor" href="#imagerylayer-影像图层" aria-label="Permalink to &quot;ImageryLayer（影像图层）&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 添加影像图层</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> imageryLayer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addImageryProvider</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">BingMapsImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://dev.virtualearth.net</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">key</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-bing-maps-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">mapStyle</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BingMapsStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">AERIAL</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 调整图层属性</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">alpha </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 透明度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">brightness </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.2</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 亮度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">contrast </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 对比度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">saturation </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 饱和度</span></span></code></pre></div><h3 id="terrainprovider-地形提供者" tabindex="-1">TerrainProvider（地形提供者） <a class="header-anchor" href="#terrainprovider-地形提供者" aria-label="Permalink to &quot;TerrainProvider（地形提供者）&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 设置地形提供者</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">terrainProvider </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">CesiumTerrainProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">IonResource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromAssetId</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">1</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">requestWaterMask</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">requestVertexNormals</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="事件系统" tabindex="-1">事件系统 <a class="header-anchor" href="#事件系统" aria-label="Permalink to &quot;事件系统&quot;">​</a></h2><h3 id="鼠标事件" tabindex="-1">鼠标事件 <a class="header-anchor" href="#鼠标事件" aria-label="Permalink to &quot;鼠标事件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> handler </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">ScreenSpaceEventHandler</span><span style="color:#BABED8;">(viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 左键点击</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">左键点击:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LEFT_CLICK)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 鼠标移动</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">鼠标移动:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">endPosition</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">MOUSE_MOVE)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 滚轮缩放</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">滚轮事件:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">event</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHEEL)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="相机事件" tabindex="-1">相机事件 <a class="header-anchor" href="#相机事件" aria-label="Permalink to &quot;相机事件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 相机移动开始</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">moveStart</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">相机开始移动</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机移动结束</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">moveEnd</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">相机移动结束</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机位置改变</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">changed</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">percentage</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">相机位置改变:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">percentage</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="场景事件" tabindex="-1">场景事件 <a class="header-anchor" href="#场景事件" aria-label="Permalink to &quot;场景事件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 渲染前事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">preRender</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">scene</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">time</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 在每帧渲染前执行</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 渲染后事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">postRender</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">scene</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">time</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 在每帧渲染后执行</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 地形加载事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">tileLoadProgressEvent</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">queuedTileCount</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">待加载瓦片数量:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">queuedTileCount</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="性能考虑" tabindex="-1">性能考虑 <a class="header-anchor" href="#性能考虑" aria-label="Permalink to &quot;性能考虑&quot;">​</a></h2><h3 id="实体管理" tabindex="-1">实体管理 <a class="header-anchor" href="#实体管理" aria-label="Permalink to &quot;实体管理&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 批量添加实体时使用 suspendEvents</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">suspendEvents</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">for</span><span style="color:#BABED8;"> (</span><span style="color:#C792EA;">let</span><span style="color:#BABED8;"> i </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> i </span><span style="color:#89DDFF;">&lt;</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> i</span><span style="color:#89DDFF;">++</span><span style="color:#BABED8;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#F78C6C;">116</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#F78C6C;">39</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">    )</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    point</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">5</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">resumeEvents</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="距离显示条件" tabindex="-1">距离显示条件 <a class="header-anchor" href="#距离显示条件" aria-label="Permalink to &quot;距离显示条件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 根据距离显示不同细节级别</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> entity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 只在特定距离范围内显示</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">distanceDisplayCondition</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">DistanceDisplayCondition</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10000</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="下一步" tabindex="-1">下一步 <a class="header-anchor" href="#下一步" aria-label="Permalink to &quot;下一步&quot;">​</a></h2><ul><li><a href="/cesium/operations.html">常用操作</a> - 学习具体的地图操作和交互功能</li></ul></div></div></main><footer class="VPDocFooter" data-v-c4b0d3cf data-v-face870a><!--[--><!--]--><!----><div class="prev-next" data-v-face870a><div class="pager" data-v-face870a><a class="pager-link prev" href="/cesium/basics.html" data-v-face870a><span class="desc" data-v-face870a>Previous page</span><span class="title" data-v-face870a>基础配置</span></a></div><div class="has-prev pager" data-v-face870a><a class="pager-link next" href="/cesium/operations.html" data-v-face870a><span class="desc" data-v-face870a>Next page</span><span class="title" data-v-face870a>常用操作</span></a></div></div></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-b2cf3e0b data-v-2f86ebd2><div class="container" data-v-2f86ebd2><!----><p class="copyright" data-v-2f86ebd2>Copyright © 2025 智洋上水</p></div></footer><!--[--><!--]--></div></div>
    <script>__VP_HASH_MAP__ = JSON.parse("{\"best-practices_index.md\":\"38aee9d5\",\"components_directives_permission.md\":\"72d28b46\",\"best-practices_component-design.md\":\"d570a27f\",\"index.md\":\"3e96beb2\",\"cesium_index.md\":\"440ac056\",\"standards_code-review.md\":\"04c78cff\",\"tools_vscode.md\":\"71a1eb8e\",\"tools_husky.md\":\"586bda1d\",\"components_charts.md\":\"1b67c1bd\",\"tools_debugging.md\":\"506e7f52\",\"components_directives_table-height.md\":\"1acd3698\",\"components_business_common-dialog-box.md\":\"605964f5\",\"cesium_concepts.md\":\"608db15b\",\"best-practices_vuex-best-practices.md\":\"42c910e9\",\"cesium_operations.md\":\"4af21b3f\",\"tools_index.md\":\"6738f00c\",\"components_directives_index.md\":\"e6ec9d5d\",\"best-practices_project-structure.md\":\"1dfc19f7\",\"guide_index.md\":\"a218fcb9\",\"best-practices_performance.md\":\"495f9c35\",\"best-practices_async-data.md\":\"d6f4dbd8\",\"standards_vue-standard.md\":\"204374d5\",\"guide_project-structure.md\":\"83547606\",\"standards_css-standard.md\":\"43da9895\",\"standards_html-standard.md\":\"87257590\",\"components_directives_throttle.md\":\"b30fc5ee\",\"components_directives_loading.md\":\"0bd89f42\",\"cesium_examples_water-monitor.md\":\"432751c3\",\"standards_js-standard.md\":\"e5f97f25\",\"tools_eslint.md\":\"eea2bb46\",\"components_directives_copy.md\":\"1f572838\",\"standards_documentation.md\":\"4ebedf27\",\"components_directives_debounce.md\":\"6c3b5296\",\"standards_git-workflow.md\":\"a7004f93\",\"best-practices_modular-development.md\":\"ce72d502\",\"components_index.md\":\"06a93849\",\"components_business_dict-select.md\":\"5f3747e9\",\"components_business_custom-file-upload.md\":\"40d498f0\",\"components_business_map-visualization.md\":\"11e90cc6\",\"tools_prettier.md\":\"dc0ae721\",\"best-practices_charts.md\":\"3ff7afe8\",\"components_form.md\":\"a7aefeb8\",\"components_business_dict-tag.md\":\"f87374af\",\"components_business_input-number.md\":\"186c78fb\",\"best-practices_state-management.md\":\"6cde5f2d\",\"best-practices_api-request.md\":\"32a5c3df\",\"components_business_input-word.md\":\"28d03cd2\",\"best-practices_utils.md\":\"08da2e9d\",\"guide_development-process.md\":\"7d0d3459\",\"tools_package-manager.md\":\"7c6faea2\",\"components_screen.md\":\"a126a925\",\"components_business_file-preview.md\":\"f077827f\",\"components_directives_drag-dialog.md\":\"fc009f97\",\"cesium_basics.md\":\"18bfba98\",\"components_business.md\":\"********\",\"standards_index.md\":\"e5fe1d60\",\"standards_git-commit.md\":\"4953e1ed\",\"best-practices_error-handling.md\":\"37d5b8d1\",\"best-practices_i18n.md\":\"83c49ca7\",\"components_table.md\":\"9742235f\",\"best-practices_reuse.md\":\"d4c27921\",\"best-practices_component-communication.md\":\"da381d67\",\"best-practices_routing.md\":\"a83fde5f\",\"guide_admin-development.md\":\"b638fd3e\",\"components_business_image-upload.md\":\"f2d49e5f\"}")
__VP_SITE_DATA__ = JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"前端技术开发文档\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"appearance\":true,\"themeConfig\":{\"logo\":\"/logo.jpeg\",\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"指南\",\"link\":\"/guide/\"},{\"text\":\"组件\",\"link\":\"/components/\"},{\"text\":\"最佳实践\",\"link\":\"/best-practices/\"},{\"text\":\"规范标准\",\"link\":\"/standards/\"},{\"text\":\"工具配置\",\"link\":\"/tools/\"},{\"text\":\"Cesium\",\"link\":\"/cesium/\"}],\"search\":{\"provider\":\"local\",\"options\":{\"translations\":{\"button\":{\"buttonText\":\"搜索文档\",\"buttonAriaLabel\":\"搜索文档\"},\"modal\":{\"noResultsText\":\"无法找到相关结果\",\"resetButtonTitle\":\"清除查询条件\",\"footer\":{\"selectText\":\"选择\",\"navigateText\":\"切换\",\"closeText\":\"关闭\"}}}}},\"sidebar\":{\"/guide/\":[{\"text\":\"开发指南\",\"items\":[{\"text\":\"快速开始\",\"link\":\"/guide/\"},{\"text\":\"项目结构\",\"link\":\"/guide/project-structure\"},{\"text\":\"开发流程\",\"link\":\"/guide/development-process\"},{\"text\":\"后台管理开发\",\"link\":\"/guide/admin-development\"}]}],\"/components/\":[{\"text\":\"组件库\",\"items\":[{\"text\":\"组件概览\",\"link\":\"/components/\"},{\"text\":\"业务组件\",\"collapsed\":false,\"items\":[{\"text\":\"业务组件总览\",\"link\":\"/components/business\"},{\"text\":\"字典标签组件\",\"link\":\"/components/business/dict-tag\"},{\"text\":\"字典选择器\",\"link\":\"/components/business/dict-select\"},{\"text\":\"自定义文件上传\",\"link\":\"/components/business/custom-file-upload\"},{\"text\":\"图片上传组件\",\"link\":\"/components/business/image-upload\"},{\"text\":\"自定义数字输入框\",\"link\":\"/components/business/input-number\"},{\"text\":\"自定义文本输入框\",\"link\":\"/components/business/input-word\"},{\"text\":\"文件预览组件\",\"link\":\"/components/business/file-preview\"},{\"text\":\"地图可视化组件\",\"link\":\"/components/business/map-visualization\"}]},{\"text\":\"表单组件\",\"link\":\"/components/form\"},{\"text\":\"表格组件\",\"link\":\"/components/table\"},{\"text\":\"图表组件\",\"link\":\"/components/charts\"},{\"text\":\"全局指令\",\"collapsed\":false,\"items\":[{\"text\":\"指令概览\",\"link\":\"/components/directives/index\"},{\"text\":\"表格高度\",\"link\":\"/components/directives/table-height\"},{\"text\":\"权限控制\",\"link\":\"/components/directives/permission\"},{\"text\":\"弹窗拖拽\",\"link\":\"/components/directives/drag-dialog\"},{\"text\":\"防抖处理\",\"link\":\"/components/directives/debounce\"},{\"text\":\"节流处理\",\"link\":\"/components/directives/throttle\"},{\"text\":\"一键复制\",\"link\":\"/components/directives/copy\"}]},{\"text\":\"大屏开发\",\"link\":\"/components/screen\"}]}],\"/best-practices/\":[{\"text\":\"最佳实践\",\"items\":[{\"text\":\"概述\",\"link\":\"/best-practices/\"},{\"text\":\"性能优化\",\"link\":\"/best-practices/performance\"},{\"text\":\"代码复用\",\"link\":\"/best-practices/reuse\"},{\"text\":\"状态管理\",\"link\":\"/best-practices/state-management\"},{\"text\":\"路由管理\",\"link\":\"/best-practices/routing\"},{\"text\":\"组件通信\",\"link\":\"/best-practices/component-communication\"},{\"text\":\"异步数据处理\",\"link\":\"/best-practices/async-data\"},{\"text\":\"模块化开发\",\"link\":\"/best-practices/modular-development\"},{\"text\":\"错误处理\",\"link\":\"/best-practices/error-handling\"},{\"text\":\"国际化实现\",\"link\":\"/best-practices/i18n\"},{\"text\":\"Vue组件设计\",\"link\":\"/best-practices/component-design\"},{\"text\":\"Vuex最佳实践\",\"link\":\"/best-practices/vuex-best-practices\"},{\"text\":\"Vue项目结构\",\"link\":\"/best-practices/project-structure\"},{\"text\":\"API请求封装\",\"link\":\"/best-practices/api-request\"},{\"text\":\"工具函数\",\"link\":\"/best-practices/utils\"}]}],\"/standards/\":[{\"text\":\"规范标准\",\"items\":[{\"text\":\"规范概述\",\"link\":\"/standards/\"},{\"text\":\"编码规范\",\"collapsed\":false,\"items\":[{\"text\":\"JavaScript规范\",\"link\":\"/standards/js-standard\"},{\"text\":\"CSS规范\",\"link\":\"/standards/css-standard\"},{\"text\":\"HTML规范\",\"link\":\"/standards/html-standard\"},{\"text\":\"Vue开发规范\",\"link\":\"/standards/vue-standard\"}]},{\"text\":\"Git提交规范\",\"link\":\"/standards/git-commit\"},{\"text\":\"Git工作流\",\"link\":\"/standards/git-workflow\"},{\"text\":\"代码审查\",\"link\":\"/standards/code-review\"},{\"text\":\"文档规范\",\"link\":\"/standards/documentation\"}]}],\"/tools/\":[{\"text\":\"开发工具配置\",\"items\":[{\"text\":\"工具概览\",\"link\":\"/tools/\"},{\"text\":\"VS Code配置\",\"link\":\"/tools/vscode\"},{\"text\":\"调试工具\",\"link\":\"/tools/debugging\"},{\"text\":\"包管理工具\",\"link\":\"/tools/package-manager\"}]},{\"text\":\"代码质量工具\",\"collapsed\":false,\"items\":[{\"text\":\"ESLint配置\",\"link\":\"/tools/eslint\"},{\"text\":\"Prettier配置\",\"link\":\"/tools/prettier\"},{\"text\":\"Husky配置\",\"link\":\"/tools/husky\"}]}],\"/cesium/\":[{\"text\":\"Cesium 3D地图引擎\",\"items\":[{\"text\":\"简介\",\"link\":\"/cesium/\"},{\"text\":\"基础配置\",\"link\":\"/cesium/basics\"},{\"text\":\"核心概念\",\"link\":\"/cesium/concepts\"},{\"text\":\"常用操作\",\"link\":\"/cesium/operations\"}]},{\"text\":\"实战案例\",\"collapsed\":false,\"items\":[{\"text\":\"智慧水务监控\",\"link\":\"/cesium/examples/water-monitor\"}]}]},\"footer\":{\"copyright\":\"Copyright © 2025 智洋上水\"}},\"locales\":{},\"scrollOffset\":90,\"cleanUrls\":false}")</script>
    
  </body>
</html>