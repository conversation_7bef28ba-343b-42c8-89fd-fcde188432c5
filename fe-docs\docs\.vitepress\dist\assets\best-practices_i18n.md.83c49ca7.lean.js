import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"Vue.js国际化实现最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/i18n.md","filePath":"best-practices/i18n.md"}'),p={name:"best-practices/i18n.md"},o=l("",112),e=[o];function t(c,r,F,D,y,i){return a(),n("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
