import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const A=JSON.parse('{"title":"异步数据处理","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/async-data.md","filePath":"best-practices/async-data.md"}'),p={name:"best-practices/async-data.md"},o=l("",48),e=[o];function t(c,r,F,y,D,i){return n(),a("div",null,e)}const E=s(p,[["render",t]]);export{A as __pageData,E as default};
