# 开发流程

本页面介绍现代化水库管理矩阵前端项目的标准开发流程、分支管理和代码提交规范。规范化的开发流程有助于提高团队协作效率，保证代码质量。

## 项目概述

Matrix-UI是一个基于Vue 2.x技术栈的现代化水库管理系统前端项目，集成了以下核心功能：
- 🌊 **四全管理**：全覆盖、全要素、全天候、全周期
- ⚖️ **四制体系**：体制、机制、法制、责任制  
- 🔮 **四预系统**：预报、预警、预演、预案
- 🛡️ **四管功能**：安全、除险、体检、维护
- 🗺️ **3D可视化**：Cesium三维地图、数字孪生
- 📊 **数据大屏**：ECharts图表、实时监控

## Git工作流

我们采用基于Git Flow的分支管理策略，主要包含以下分支：

- **master/main**: 主分支，用于生产环境，只能从release或hotfix分支合并
- **develop**: 开发分支，所有功能开发完成后合并到此分支
- **feature/xxx**: 功能分支，从develop分支创建，开发完成后合并回develop分支
- **release/vx.x.x**: 发布分支，从develop分支创建，准备发布版本
- **hotfix/xxx**: 热修复分支，从master分支创建，修复生产环境bug


## 开发流程

### 1. 功能开发

1. 从最新的develop分支创建功能分支
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/screen-siyuan-yubao
   ```

2. 在功能分支上进行开发
   - 遵循Vue 2.x组合式API开发规范
   - 使用Element UI组件库
   - 集成Cesium/ECharts等第三方库

3. 提交代码，遵循提交规范
4. 创建Pull Request到develop分支
5. 代码审查通过后合并到develop分支

### 2. 版本发布

1. 从develop分支创建release分支
   ```bash
   git checkout develop
   git pull
   git checkout -b release/v3.7.0
   ```

2. 在release分支上进行集成和问题修复
3. 完成后合并到master分支和develop分支
4. 在master分支上打版本标签
   ```bash
   git tag -a v3.7.0 -m "版本3.7.0发布 - 新增四预系统功能"
   git push origin v3.7.0
   ```

### 3. 生产问题修复

1. 从master分支创建hotfix分支
   ```bash
   git checkout master
   git pull
   git checkout -b hotfix/screen-memory-leak
   ```

2. 修复问题
3. 合并到master分支和develop分支
4. 在master分支上打补丁版本标签

## 代码提交规范

我们采用Angular提交规范，针对水库管理系统增加特定的scope：

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type类型

- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码风格调整，不影响功能
- **refactor**: 重构代码，不新增功能或修复bug
- **perf**: 性能优化
- **chore**: 构建过程或辅助工具的变动

### Scope范围（业务模块）

- **screen**: 大屏相关功能
- **monitor**: 监测模块
- **dispatch**: 调度模块
- **digital-twin**: 数字孪生
- **map**: 地图功能
- **chart**: 图表组件
- **auth**: 认证授权
- **api**: 接口相关
- **utils**: 工具函数

### 提交示例

```
feat(screen): 新增四预系统预报大屏

- 添加水文预报数据展示组件
Closes: #JIRA-456
```

```
fix(cesium): 修复三维地图加载异常
- 解决Cesium地形数据加载失败问题
- 优化Cesium Worker配置

Fixes: #BUG-123
```

## 代码审查(Code Review)

所有代码必须经过至少一位团队成员的代码审查后才能合并到develop分支

### 1. 功能完整性
- ✅ 是否实现了需求的所有功能点
- ✅ 大屏适配是否正确（1920x1080）
- ✅ 数据展示是否准确

### 2. 性能问题
- ✅ Cesium场景渲染性能
- ✅ ECharts图表加载速度
- ✅ 大数据量处理优化
- ✅ 内存泄漏检查

### 3. 兼容性问题
- ✅ 浏览器兼容性（Chrome、Edge、Firefox）
- ✅ 分辨率适配
- ✅ 第三方库版本兼容

### 4. 安全问题
- ✅ XSS防护
- ✅ API接口鉴权
- ✅ 敏感数据加密


### 环境变量配置

创建环境变量文件：

```bash
# .env.development
VUE_APP_TITLE=现代化水库管理矩阵
VUE_APP_BASE_API=/dev-api
VUE_APP_CESIUM_TOKEN=your_cesium_token
VUE_APP_MAP_KEY=your_map_key

# .env.production  
VUE_APP_TITLE=现代化水库管理矩阵
VUE_APP_BASE_API=/prod-api
VUE_APP_CESIUM_TOKEN=your_cesium_token
VUE_APP_MAP_KEY=your_map_key
```
