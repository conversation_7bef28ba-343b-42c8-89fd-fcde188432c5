# v-permission

用于根据用户权限控制元素的显示/隐藏。

## 使用场景

- 按钮、菜单等UI元素的权限控制
- 页面功能区域的权限控制

## 基本用法

```vue
<template>
  <!-- 单个权限控制 -->
  <el-button v-permission="'user:create'">创建用户</el-button>
  
  <!-- 多个权限控制（满足任一权限即可显示） -->
  <el-button v-permission="['user:update', 'user:edit']">编辑用户</el-button>
</template>
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| permission | String / Array | 权限标识或权限标识数组 |

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directives/permission.js
import store from '@/store';

export default {
  inserted(el, binding) {
    const { value } = binding;
    if (!value) return;
    
    // 检查用户是否有权限
    const hasPermission = checkPermission(value);
    
    if (!hasPermission) {
      // 如果没有权限，从DOM中移除元素
      el.parentNode && el.parentNode.removeChild(el);
    }
  }
};

// 检查权限的辅助函数
function checkPermission(permission) {
  // 获取用户权限列表
  const userPermissions = store.getters.permissions || [];
  
  if (typeof permission === 'string') {
    // 单个权限检查
    return userPermissions.includes(permission);
  } else if (Array.isArray(permission)) {
    // 多个权限检查（满足任一权限即可）
    return permission.some(p => userPermissions.includes(p));
  }
  
  return false;
}
```
</details>
