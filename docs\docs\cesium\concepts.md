# 核心概念

理解Cesium的核心概念是掌握3D地图开发的基础。本章将介绍Cesium的主要组件、坐标系统、数据类型等核心知识。

## 主要组件

### Viewer（查看器）

Viewer是Cesium应用的主入口，包含了完整的3D地球场景和用户界面控件。

```javascript
const viewer = new Cesium.Viewer('cesiumContainer');
```

**主要属性：**
- `scene` - 3D场景
- `camera` - 相机控制
- `canvas` - HTML5 Canvas元素
- `clock` - 时钟控制
- `entities` - 实体集合
- `dataSources` - 数据源集合

### Scene（场景）

Scene管理3D场景的渲染，包含地球、天空盒、光照等。

```javascript
const scene = viewer.scene;

// 场景相关配置
scene.globe.show = true;              // 显示地球
scene.skyBox.show = true;             // 显示天空盒
scene.sun.show = true;                // 显示太阳
scene.moon.show = true;               // 显示月亮
scene.fog.enabled = true;             // 启用雾效
```

### Camera（相机）

Camera控制用户的视角和导航。

```javascript
const camera = viewer.camera;

// 相机位置和方向
camera.position;        // 相机位置（笛卡尔坐标）
camera.direction;       // 观看方向
camera.up;             // 向上方向
camera.right;          // 向右方向
```

### Globe（地球）

Globe表示地球几何体，包含地形和影像数据。

```javascript
const globe = viewer.scene.globe;

// 地球配置
globe.show = true;                    // 显示地球
globe.enableLighting = false;         // 启用光照
globe.showWaterEffect = true;         // 显示水体效果
globe.shadows = Cesium.ShadowMode.RECEIVE_ONLY; // 阴影模式
```

## 坐标系统

### WGS84坐标系

Cesium使用WGS84大地坐标系作为标准参考坐标系。

```javascript
// 经纬度坐标（度）
const longitude = 116.391;  // 经度
const latitude = 39.904;    // 纬度
const height = 1000;        // 高度（米）
```

### 笛卡尔坐标系

3D空间中的直角坐标系，原点位于地球中心。

```javascript
// 从经纬度转换为笛卡尔坐标
const cartesian = Cesium.Cartesian3.fromDegrees(longitude, latitude, height);

// 从笛卡尔坐标转换为经纬度
const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
const lon = Cesium.Math.toDegrees(cartographic.longitude);
const lat = Cesium.Math.toDegrees(cartographic.latitude);
const alt = cartographic.height;
```

### 屏幕坐标系

屏幕上的2D坐标系，原点在左上角。

```javascript
// 世界坐标转屏幕坐标
const screenPosition = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
  viewer.scene, 
  cartesian
);

// 屏幕坐标转世界坐标
const worldPosition = viewer.camera.pickEllipsoid(
  screenPosition, 
  viewer.scene.globe.ellipsoid
);
```

## 实体系统（Entity API）

### Entity（实体）

Entity是Cesium中表示对象的高级API，提供了声明式的图形描述。

```javascript
// 创建点实体
const pointEntity = viewer.entities.add({
  id: 'myPoint',
  name: '示例点',
  position: Cesium.Cartesian3.fromDegrees(116.391, 39.904),
  point: {
    pixelSize: 10,
    color: Cesium.Color.YELLOW,
    outlineColor: Cesium.Color.BLACK,
    outlineWidth: 2
  }
});
```

### 图形类型

Cesium支持多种图形类型：

#### 点（Point）
```javascript
point: {
  pixelSize: 10,
  color: Cesium.Color.YELLOW,
  outlineColor: Cesium.Color.BLACK,
  outlineWidth: 2,
  heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
}
```

#### 标签（Label）
```javascript
label: {
  text: '标签文本',
  font: '14pt sans-serif',
  fillColor: Cesium.Color.WHITE,
  outlineColor: Cesium.Color.BLACK,
  outlineWidth: 2,
  style: Cesium.LabelStyle.FILL_AND_OUTLINE
}
```

#### 广告牌（Billboard）
```javascript
billboard: {
  image: 'path/to/image.png',
  scale: 1.0,
  pixelOffset: new Cesium.Cartesian2(0, -50),
  eyeOffset: new Cesium.Cartesian3(0.0, 0.0, 0.0),
  horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
  verticalOrigin: Cesium.VerticalOrigin.BOTTOM
}
```

#### 线（Polyline）
```javascript
polyline: {
  positions: Cesium.Cartesian3.fromDegreesArray([
    116.0, 39.0,
    117.0, 40.0,
    118.0, 41.0
  ]),
  width: 2,
  material: Cesium.Color.RED,
  clampToGround: true
}
```

#### 多边形（Polygon）
```javascript
polygon: {
  hierarchy: Cesium.Cartesian3.fromDegreesArray([
    116.0, 39.0,
    117.0, 39.0,
    117.0, 40.0,
    116.0, 40.0
  ]),
  material: Cesium.Color.BLUE.withAlpha(0.5),
  outline: true,
  outlineColor: Cesium.Color.BLACK
}
```

#### 3D模型（Model）
```javascript
model: {
  uri: 'path/to/model.glb',
  scale: 1.0,
  minimumPixelSize: 128,
  maximumScale: 20000
}
```

## 材质系统

### 基础材质

```javascript
// 纯色材质
material: Cesium.Color.RED

// 图片材质
material: new Cesium.ImageMaterialProperty({
  image: 'path/to/texture.jpg',
  repeat: new Cesium.Cartesian2(4.0, 4.0)
})

// 条纹材质
material: new Cesium.StripeMaterialProperty({
  evenColor: Cesium.Color.WHITE,
  oddColor: Cesium.Color.BLACK,
  repeat: 5.0
})
```

### 动画材质

```javascript
// 发光线材质
material: new Cesium.PolylineGlowMaterialProperty({
  glowPower: 0.2,
  color: Cesium.Color.BLUE
})

// 轮廓线材质
material: new Cesium.PolylineOutlineMaterialProperty({
  color: Cesium.Color.ORANGE,
  outlineWidth: 2,
  outlineColor: Cesium.Color.BLACK
})
```

## 数据源系统

### DataSource（数据源）

DataSource是Entity的容器，用于管理大量实体。

```javascript
// 创建自定义数据源
const dataSource = new Cesium.CustomDataSource('myDataSource');

// 添加实体到数据源
dataSource.entities.add({
  position: Cesium.Cartesian3.fromDegrees(116.391, 39.904),
  point: {
    pixelSize: 10,
    color: Cesium.Color.YELLOW
  }
});

// 将数据源添加到viewer
viewer.dataSources.add(dataSource);
```

### 内置数据源

```javascript
// GeoJSON数据源
const geoJsonDataSource = Cesium.GeoJsonDataSource.load('data.geojson');
viewer.dataSources.add(geoJsonDataSource);

// KML数据源
const kmlDataSource = Cesium.KmlDataSource.load('data.kml');
viewer.dataSources.add(kmlDataSource);

// CZML数据源
const czmlDataSource = Cesium.CzmlDataSource.load('data.czml');
viewer.dataSources.add(czmlDataSource);
```

## 时间系统

### JulianDate（儒略日）

Cesium使用儒略日表示时间。

```javascript
// 创建当前时间
const now = Cesium.JulianDate.now();

// 从ISO8601字符串创建时间
const time = Cesium.JulianDate.fromIso8601('2023-01-01T12:00:00Z');

// 时间计算
const tomorrow = Cesium.JulianDate.addDays(now, 1, new Cesium.JulianDate());
```

### Clock（时钟）

Clock控制时间的流逝和动画。

```javascript
const clock = viewer.clock;

// 设置时间范围
clock.startTime = Cesium.JulianDate.fromIso8601('2023-01-01T00:00:00Z');
clock.stopTime = Cesium.JulianDate.fromIso8601('2023-01-02T00:00:00Z');
clock.currentTime = clock.startTime;

// 设置时钟模式
clock.clockRange = Cesium.ClockRange.LOOP_STOP;  // 循环播放
clock.multiplier = 3600;  // 时间倍速（1小时/秒）

// 启动时钟
clock.shouldAnimate = true;
```

### 时间动态属性

```javascript
// 创建时间相关的位置属性
const property = new Cesium.SampledPositionProperty();

// 添加时间样本
property.addSample(
  Cesium.JulianDate.fromIso8601('2023-01-01T00:00:00Z'),
  Cesium.Cartesian3.fromDegrees(116.0, 39.0)
);

property.addSample(
  Cesium.JulianDate.fromIso8601('2023-01-01T01:00:00Z'),
  Cesium.Cartesian3.fromDegrees(117.0, 40.0)
);

// 将属性应用到实体
const entity = viewer.entities.add({
  position: property,
  point: {
    pixelSize: 10,
    color: Cesium.Color.YELLOW
  }
});
```

## 图层系统

### ImageryLayer（影像图层）

```javascript
// 添加影像图层
const imageryLayer = viewer.imageryLayers.addImageryProvider(
  new Cesium.BingMapsImageryProvider({
    url: 'https://dev.virtualearth.net',
    key: 'your-bing-maps-key',
    mapStyle: Cesium.BingMapsStyle.AERIAL
  })
);

// 调整图层属性
imageryLayer.alpha = 0.8;  // 透明度
imageryLayer.brightness = 1.2;  // 亮度
imageryLayer.contrast = 1.0;  // 对比度
imageryLayer.saturation = 1.0;  // 饱和度
```

### TerrainProvider（地形提供者）

```javascript
// 设置地形提供者
viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
  url: Cesium.IonResource.fromAssetId(1),
  requestWaterMask: true,
  requestVertexNormals: true
});
```

## 事件系统

### 鼠标事件

```javascript
const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

// 左键点击
handler.setInputAction(function(event) {
  console.log('左键点击:', event.position);
}, Cesium.ScreenSpaceEventType.LEFT_CLICK);

// 鼠标移动
handler.setInputAction(function(event) {
  console.log('鼠标移动:', event.endPosition);
}, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

// 滚轮缩放
handler.setInputAction(function(event) {
  console.log('滚轮事件:', event);
}, Cesium.ScreenSpaceEventType.WHEEL);
```

### 相机事件

```javascript
// 相机移动开始
viewer.camera.moveStart.addEventListener(function() {
  console.log('相机开始移动');
});

// 相机移动结束
viewer.camera.moveEnd.addEventListener(function() {
  console.log('相机移动结束');
});

// 相机位置改变
viewer.camera.changed.addEventListener(function(percentage) {
  console.log('相机位置改变:', percentage);
});
```

### 场景事件

```javascript
// 渲染前事件
viewer.scene.preRender.addEventListener(function(scene, time) {
  // 在每帧渲染前执行
});

// 渲染后事件
viewer.scene.postRender.addEventListener(function(scene, time) {
  // 在每帧渲染后执行
});

// 地形加载事件
viewer.scene.globe.tileLoadProgressEvent.addEventListener(function(queuedTileCount) {
  console.log('待加载瓦片数量:', queuedTileCount);
});
```

## 性能考虑

### 实体管理

```javascript
// 批量添加实体时使用 suspendEvents
viewer.entities.suspendEvents();
for (let i = 0; i < 1000; i++) {
  viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(
      116 + Math.random(), 
      39 + Math.random()
    ),
    point: {
      pixelSize: 5,
      color: Cesium.Color.YELLOW
    }
  });
}
viewer.entities.resumeEvents();
```

### 距离显示条件

```javascript
// 根据距离显示不同细节级别
const entity = viewer.entities.add({
  position: Cesium.Cartesian3.fromDegrees(116.391, 39.904),
  point: {
    pixelSize: 10,
    color: Cesium.Color.YELLOW,
    // 只在特定距离范围内显示
    distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 10000)
  }
});
```

## 下一步

- [常用操作](/cesium/operations) - 学习具体的地图操作和交互功能