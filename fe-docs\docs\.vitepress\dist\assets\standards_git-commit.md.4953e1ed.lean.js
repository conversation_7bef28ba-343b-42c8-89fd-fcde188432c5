import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const g=JSON.parse('{"title":"Git提交规范","description":"","frontmatter":{},"headers":[],"relativePath":"standards/git-commit.md","filePath":"standards/git-commit.md"}'),o={name:"standards/git-commit.md"},e=l("",52),t=[e];function p(c,i,r,d,y,h){return a(),n("div",null,t)}const b=s(o,[["render",p]]);export{g as __pageData,b as default};
