# dictSelect 字典选择器组件

基于字典数据的下拉选择器组件，支持字典数据的增删改查操作，是ElementUI Select组件的增强版本。

## 功能特性

- 🔽 基于ElementUI Select组件，完全兼容其所有属性
- 📊 支持字典类型和字典数据两种数据源
- ✏️ 内置字典数据的增删改查功能
- 🔧 支持权限控制的操作按钮
- 🔄 自动数据刷新和状态管理
- 🎨 美观的操作界面设计

## 基础用法

### 通过字典类型加载数据

```vue
<template>
  <div>
    <dict-select 
      v-model="status" 
      dict-text="sys_normal_disable"
      placeholder="请选择状态"
      @change="handleStatusChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      status: ''
    }
  },
  methods: {
    handleStatusChange(value) {
      console.log('状态改变:', value)
    }
  }
}
</script>
```

### 通过字典数据加载

```vue
<template>
  <div>
    <dict-select 
      v-model="type" 
      :dict-data="typeOptions"
      :dict-add="false"
      placeholder="请选择类型"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      type: '',
      typeOptions: [
        { 
          dictValue: '1', 
          dictLabel: '类型A', 
          raw: { 
            dictType: 'business_type',
            dictCode: 1,
            listClass: 'primary'
          } 
        },
        { 
          dictValue: '2', 
          dictLabel: '类型B', 
          raw: { 
            dictType: 'business_type',
            dictCode: 2,
            listClass: 'success'
          } 
        }
      ]
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| dictData | 字典数据数组 | Array | — | [] | 否 |
| dictText | 字典类型（从后端接口获取数据时使用） | String | — | '' | 否 |
| value / v-model | 绑定值 | String/Number/Boolean | — | '' | 否 |
| placeholder | 输入框占位文本 | String | — | '请选择' | 否 |
| dictAdd | 是否显示新增字典按钮 | Boolean | true/false | true | 否 |

:::tip 继承属性
该组件完全继承ElementUI Select组件的所有属性，可以使用 `v-bind="$attrs"` 传递任何Select支持的属性。
:::

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 绑定值变化时触发 | (value: any) |
| change | 选中值发生变化时触发 | (value: any) |

### dictData 数据格式

```typescript
interface DictDataItem {
  dictValue: string | number;    // 字典值
  dictLabel: string;            // 字典标签
  raw: {                        // 原始数据对象
    dictType: string;           // 字典类型
    dictCode?: number;          // 字典编码
    dictSort?: number;          // 排序
    listClass?: string;         // 列表样式类
    cssClass?: string;          // CSS样式类
    status?: string;            // 状态
    remark?: string;            // 备注
  }
}
```

## 字典管理功能

### 新增字典

组件内置新增字典功能，点击下拉框底部的"新增"按钮可以打开新增弹窗。

**新增表单字段：**
- 字典类型（只读）
- 数据标签（必填）
- 数据键值（必填）
- 样式属性
- 显示排序
- 回显样式
- 状态
- 备注

### 编辑字典

鼠标悬停在选项上时，会显示编辑和删除按钮，点击编辑按钮可以修改字典数据。

### 删除字典

点击删除按钮会弹出确认对话框，确认后删除对应的字典数据。

## 权限控制

组件内置权限指令支持：

- `v-has-permi="['system:dict:add']"` - 控制新增按钮显示
- `v-has-permi="['system:dict:edit']"` - 控制编辑按钮显示  
- `v-has-permi="['system:dict:remove']"` - 控制删除按钮显示

## 使用示例

### 用户状态选择

```vue
<template>
  <div>
    <el-form :model="form" label-width="100px">
      <el-form-item label="用户状态">
        <dict-select 
          v-model="form.status"
          dict-text="sys_normal_disable"
          placeholder="请选择用户状态"
          style="width: 200px"
          @change="handleStatusChange"
        />
      </el-form-item>
      
      <el-form-item label="用户类型">
        <dict-select 
          v-model="form.userType"
          :dict-data="userTypeOptions"
          placeholder="请选择用户类型"
          style="width: 200px"
          clearable
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        status: '',
        userType: ''
      },
      userTypeOptions: [
        { 
          dictValue: '1', 
          dictLabel: '管理员', 
          raw: { dictType: 'sys_user_type', listClass: 'primary' } 
        },
        { 
          dictValue: '2', 
          dictLabel: '普通用户', 
          raw: { dictType: 'sys_user_type', listClass: 'success' } 
        }
      ]
    }
  },
  methods: {
    handleStatusChange(value) {
      console.log('用户状态改变为:', value)
      // 可以在这里执行其他逻辑
    }
  }
}
</script>
```

### 业务类型选择

```vue
<template>
  <div>
    <dict-select 
      v-model="selectedType"
      dict-text="business_type"
      placeholder="请选择业务类型"
      :dict-add="hasAddPermission"
      @change="handleTypeChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedType: '',
      hasAddPermission: true
    }
  },
  methods: {
    handleTypeChange(value) {
      // 根据选择的类型加载相关数据
      this.loadRelatedData(value)
    },
    
    loadRelatedData(type) {
      // 加载相关数据的逻辑
      console.log('加载类型为', type, '的相关数据')
    }
  }
}
</script>
```

## 注意事项

1. **数据源选择**：`dictText` 和 `dictData` 二选一使用，优先使用 `dictData`
2. **权限配置**：确保项目中已正确配置权限指令 `v-has-permi`
3. **数据格式**：使用 `dictData` 时，确保数据格式符合接口规范
4. **状态管理**：组件会自动管理字典数据的状态，无需手动刷新
5. **样式定制**：可以通过传递 ElementUI Select 的样式属性来定制外观

## 常见问题

### Q: 如何禁用字典管理功能？

A: 设置 `:dict-add="false"` 可以隐藏新增按钮，编辑和删除按钮会根据权限自动控制。

### Q: 字典数据不显示怎么办？

A: 检查以下几点：
- `dictText` 是否正确且后端接口正常
- `dictData` 数据格式是否符合要求
- 网络请求是否成功

### Q: 如何自定义字典管理弹窗？

A: 组件内置了完整的字典管理功能，如需深度定制，可以参考源码进行修改。

### Q: 支持多选吗？

A: 当前组件为单选组件，如需多选功能，可以结合 ElementUI 的 `multiple` 属性使用，或考虑使用专门的多选字典组件。

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<template>
  <div style="width: 100%">
    <el-select
      style="width: 100%"
      v-model="selectValue"
      v-bind="$attrs"
      :placeholder="placeholder"
      @change="changeValue"
    >
      <el-option
        v-for="dict in options"
        :key="dict.value"
        :value="dict.value"
        :label="dict.label"
      >
        <span style="float: left">{{ dict.label }}</span>
        <span class="control-btn">
          <i class="el-icon-edit" @click.stop="handleUpdate(dict)" v-has-permi="['system:dict:edit']"></i>
          <i class="el-icon-delete" @click.stop="handleDel(dict)" v-has-permi="['system:dict:remove']"></i>
        </span>
      </el-option>
      <div class="select-foot" v-show="dictAdd"><el-button type="text" @click.stop="handleAdd" icon="el-icon-circle-plus-outline" v-has-permi="['system:dict:add']">新增</el-button></div>
    </el-select>
    <el-dialog :close-on-click-modal="false" v-if="open" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典类型">
          <el-input v-model="form.dictType" :disabled="true"/>
        </el-form-item>
        <el-form-item label="数据标签" prop="dictLabel">
          <el-input v-model.trim="form.dictLabel" placeholder="请输入数据标签"/>
        </el-form-item>
        <el-form-item label="数据键值" prop="dictValue">
          <el-input v-model.trim="form.dictValue" placeholder="请输入数据键值"/>
        </el-form-item>
        <el-form-item label="样式属性" prop="cssClass">
          <el-input v-model.trim="form.cssClass" placeholder="请输入样式属性"/>
        </el-form-item>
        <el-form-item label="显示排序" prop="dictSort">
          <el-input-number v-model="form.dictSort" controls-position="right" :min="0"/>
        </el-form-item>
        <el-form-item label="回显样式" prop="listClass">
          <el-select v-model="form.listClass">
            <el-option
              v-for="item in listClassOptions"
              :key="item.value"
              :label="item.label + '(' + item.value + ')'"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import {addData, delData, getData, listData, updateData} from "@/api/system/dict/data";

export default {
  name: "dictSelect",
  dicts: ['sys_normal_disable'],
  props: {
    dictData: {
      type: Array,
      default: () => []
    },
    dictText: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number, Boolean],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    dictAdd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [],
      selectValue: null,
      initialValue: this.value,
      title: "新增字典值",
      open: false,
      form: {},
      // 数据标签回显样式
      listClassOptions: [
        {
          value: "default",
          label: "默认"
        },
        {
          value: "primary",
          label: "主要"
        },
        {
          value: "success",
          label: "成功"
        },
        {
          value: "info",
          label: "信息"
        },
        {
          value: "warning",
          label: "警告"
        },
        {
          value: "danger",
          label: "危险"
        }
      ],
      // 表单校验
      rules: {
        dictLabel: [
          {required: true, message: "数据标签不能为空", trigger: "blur"}
        ],
        dictValue: [
          {required: true, message: "数据键值不能为空", trigger: "blur"}
        ],
        dictSort: [
          {required: true, message: "数据顺序不能为空", trigger: "blur"}
        ]
      },
      // 字典类型
      dictType: ''
    }
  },
  mounted() {
    if(this.dictText) {
      this.dictType = this.dictText
      this.getList()
    }
  },
  watch:{
    value: {
      handler(val) {
        this.selectValue = val
      },
      immediate: true
    },
    dictData: {
      handler(val) {
        if(val.length > 0 && !this.dictText) {
          this.options = val
          this.dictType = val[0].raw.dictType
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async getList() {
      let response = await this.getDicts(this.dictType)
      this.options = response.data.map((item) =>{
        return {
          ...item,
          value: item.dictValue,
          label: item.dictLabel
        }
      })
      this.selectValue = this.initialValue
    },
    changeValue(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    },
    // 表单重置
    reset() {
      this.form = {
        dictCode: undefined,
        dictLabel: undefined,
        dictValue: undefined,
        cssClass: undefined,
        listClass: 'default',
        dictSort: 0,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    handleAdd() {
      this.reset()
      this.title = '新增字典值'
      this.open = true
      this.form.dictType = this.dictType;
    },
    handleUpdate(dict) {
      let dictCode = null
      if(dict.raw) {
        dictCode = dict.raw.dictCode
      } else {
        dictCode = dict.dictCode
      }
      this.reset();
      getData(dictCode).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改字典数据";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dictCode != undefined) {
            updateData(this.form).then(response => {
              this.$store.dispatch('dict/removeDict', this.dictType);
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addData(this.form).then(response => {
              this.$store.dispatch('dict/removeDict', this.dictType);
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    handleDel(dict) {
      let dictCode = null
      if(dict.raw) {
        dictCode = dict.raw.dictCode
      } else {
        dictCode = dict.dictCode
      }
      this.$modal.confirm('是否确认删除所选字典值？').then(function() {
        return delData(dictCode);
      }).then(() => {
        this.getList().then(() =>{
          this.changeValue(this.initialValue)
        })
        this.$store.dispatch('dict/removeDict', this.dictType);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {})
    },
  }
}
</script>
<style scoped lang="scss">
.select-foot {
  line-height: 30px;
  border-top: 1px solid #dcdfe6;
  text-align: center;
}
.control-btn {
  float: right;
  color: #8492a6;
  min-width: 50px;
  display: inline-block;
  i {
    display: none;
    font-size: 16px;
    cursor: pointer;
    color: #409EFF;
  }
  i:nth-of-type(1) {
    margin-right: 10px;
  }
}
.el-select-dropdown__item:hover {
  .control-btn i{
    display: inline;
  }
}
</style>
```

</details>
