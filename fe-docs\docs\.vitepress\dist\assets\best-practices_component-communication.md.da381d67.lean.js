import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"组件通信","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/component-communication.md","filePath":"best-practices/component-communication.md"}'),p={name:"best-practices/component-communication.md"},o=l("",56),t=[o];function e(c,r,F,D,y,i){return n(),a("div",null,t)}const A=s(p,[["render",e]]);export{E as __pageData,A as default};
