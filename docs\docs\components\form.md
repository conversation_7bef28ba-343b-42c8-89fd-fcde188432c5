# 动态表单组件

动态表单组件是基于Element UI开发的高级表单解决方案，用于快速构建复杂的数据录入界面。它支持表单配置化生成、动态渲染、数据校验等功能，能够大幅提升表单开发效率。

## 基础用法

通过JSON配置快速生成表单，无需手动编写大量重复的表单代码。

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    label-width="100px"
    @submit="handleSubmit"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        age: 18,
        gender: '',
        interests: []
      },
      formSchema: {
        fields: [
          {
            type: 'input',
            field: 'name',
            label: '姓名',
            props: {
              placeholder: '请输入姓名'
            },
            rules: [
              { required: true, message: '请输入姓名', trigger: 'blur' }
            ]
          },
          {
            type: 'input-number',
            field: 'age',
            label: '年龄',
            props: {
              min: 0,
              max: 100
            },
            rules: [
              { required: true, message: '请输入年龄', trigger: 'blur' }
            ]
          },
          {
            type: 'select',
            field: 'gender',
            label: '性别',
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' }
            ],
            props: {
              placeholder: '请选择性别'
            }
          },
          {
            type: 'checkbox-group',
            field: 'interests',
            label: '兴趣爱好',
            options: [
              { label: '阅读', value: 'reading' },
              { label: '游泳', value: 'swimming' },
              { label: '跑步', value: 'running' },
              { label: '音乐', value: 'music' }
            ]
          }
        ]
      }
    };
  },
  methods: {
    handleSubmit(formData) {
      console.log('表单提交数据:', formData);
      // 处理表单提交
    }
  }
}
</script>
```

## 表单布局

支持多种表单布局方式，包括默认布局、行内布局、栅格布局等。

### 栅格布局

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    label-width="100px"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formSchema: {
        layout: 'grid',
        gridConfig: { gutter: 20 },
        fields: [
          {
            type: 'input',
            field: 'name',
            label: '姓名',
            gridSpan: 12 // 占据12列，即半行
          },
          {
            type: 'input',
            field: 'phone',
            label: '电话',
            gridSpan: 12 // 占据12列，即半行
          },
          {
            type: 'input',
            field: 'address',
            label: '地址',
            gridSpan: 24 // 占据24列，即整行
          }
        ]
      }
    };
  }
}
</script>
```

### 行内表单

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    inline
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formSchema: {
        fields: [
          {
            type: 'input',
            field: 'username',
            label: '用户名'
          },
          {
            type: 'input',
            field: 'password',
            label: '密码',
            props: {
              type: 'password'
            }
          },
          {
            type: 'button',
            props: {
              type: 'primary'
            },
            text: '登录',
            action: 'submit'
          }
        ]
      }
    };
  }
}
</script>
```

## 表单工具

### 自动表单生成器 (FormGenerator)

自动表单生成器组件根据配置自动生成表单，简化表单开发。

```vue
<template>
  <cp-form-generator
    :schema="schema"
    v-model="formData"
    label-width="100px"
    @submit="handleSubmit"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      schema: {
        fields: [
          {
            type: 'input',
            prop: 'name',
            label: '姓名',
            rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
            attrs: {
              placeholder: '请输入姓名'
            }
          },
          {
            type: 'select',
            prop: 'gender',
            label: '性别',
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' }
            ],
            attrs: {
              placeholder: '请选择性别'
            }
          },
          {
            type: 'datepicker',
            prop: 'birthday',
            label: '出生日期',
            attrs: {
              type: 'date',
              placeholder: '请选择日期'
            }
          },
          {
            type: 'switch',
            prop: 'status',
            label: '状态',
            attrs: {
              activeText: '启用',
              inactiveText: '禁用'
            }
          },
          {
            type: 'textarea',
            prop: 'remark',
            label: '备注',
            span: 24,
            attrs: {
              rows: 3,
              placeholder: '请输入备注'
            }
          }
        ],
        buttons: [
          { type: 'primary', text: '提交', action: 'submit' },
          { text: '重置', action: 'reset' }
        ]
      }
    }
  },
  methods: {
    handleSubmit(formData) {
      console.log('表单提交', formData);
      // 提交表单
    }
  }
}
</script>
```

#### FormGenerator Props

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|-----|
| value / v-model | Object | {} | 表单数据对象 |
| schema | Object | - | 表单配置对象 |
| label-width | String | '80px' | 表单域标签的宽度 |
| size | String | 'default' | 表单尺寸，可选值: 'large', 'default', 'small' |
| disabled | Boolean | false | 是否禁用表单 |
| show-buttons | Boolean | true | 是否显示按钮 |

#### Schema 配置

```js
{
  fields: [
    {
      type: 'input', // 组件类型
      prop: 'name',  // 字段名
      label: '姓名',  // 标签
      rules: [],     // 验证规则
      span: 12,      // 栅格宽度
      show: true,    // 是否显示
      disabled: false, // 是否禁用
      attrs: {},     // 组件属性
      events: {},    // 组件事件
      slots: {}      // 组件插槽
    }
  ],
  buttons: [
    {
      type: 'primary', // 按钮类型
      text: '提交',    // 按钮文本
      action: 'submit', // 按钮动作
      show: true,     // 是否显示
      disabled: false  // 是否禁用
    }
  ]
}
```

#### FormGenerator Events

| 事件名 | 参数 | 说明 |
|-------|------|-----|
| submit | formData | 点击提交按钮时触发 |
| reset | - | 点击重置按钮时触发 |
| field-change | prop, value | 表单项值变化时触发 |
```

## 高级功能

### 表单项联动

实现表单项之间的联动效果，根据某个字段的值显示或隐藏其他字段。

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    label-width="100px"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {
        hasChildren: false,
        childrenCount: 0
      },
      formSchema: {
        fields: [
          {
            type: 'switch',
            field: 'hasChildren',
            label: '有子女'
          },
          {
            type: 'input-number',
            field: 'childrenCount',
            label: '子女数量',
            props: {
              min: 0
            },
            visible: (formData) => formData.hasChildren // 根据hasChildren字段的值决定是否显示
          }
        ]
      }
    };
  }
}
</script>
```

### 动态表单项

通过配置可以动态添加、删除表单项，适用于不确定数量的表单项场景。

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    label-width="100px"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {
        contacts: [
          { name: '', phone: '', relationship: '' }
        ]
      },
      formSchema: {
        fields: [
          {
            type: 'dynamic-list',
            field: 'contacts',
            label: '联系人',
            itemLabel: '联系人{index}',
            min: 1,
            max: 5,
            itemTemplate: { name: '', phone: '', relationship: '' },
            itemFields: [
              {
                type: 'input',
                field: 'name',
                label: '姓名',
                rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
              },
              {
                type: 'input',
                field: 'phone',
                label: '电话',
                rules: [{ required: true, message: '请输入电话', trigger: 'blur' }]
              },
              {
                type: 'select',
                field: 'relationship',
                label: '关系',
                options: [
                  { label: '父母', value: 'parent' },
                  { label: '兄弟姐妹', value: 'sibling' },
                  { label: '朋友', value: 'friend' },
                  { label: '其他', value: 'other' }
                ]
              }
            ]
          }
        ]
      }
    };
  }
}
</script>
```

### 自定义表单项

支持自定义组件，可以集成任何Vue组件作为表单项。

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    label-width="100px"
  >
    <template #custom-address="{ model, field }">
      <div class="custom-address">
        <el-select v-model="model.province" placeholder="省份">
          <el-option v-for="item in provinces" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="model.city" placeholder="城市">
          <el-option v-for="item in cities[model.province]" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </template>
  </dynamic-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        province: '',
        city: ''
      },
      provinces: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' }
      ],
      cities: {
        beijing: [
          { label: '朝阳区', value: 'chaoyang' },
          { label: '海淀区', value: 'haidian' }
        ],
        shanghai: [
          { label: '浦东新区', value: 'pudong' },
          { label: '黄浦区', value: 'huangpu' }
        ]
      },
      formSchema: {
        fields: [
          {
            type: 'custom',
            field: 'address',
            label: '地址',
            component: 'custom-address'
          }
        ]
      }
    };
  }
}
</script>
```

### 表单验证

支持多种验证规则，包括内置验证规则和自定义验证规则。

```vue
<template>
  <dynamic-form
    :schema="formSchema"
    :model="formData"
    label-width="100px"
    ref="dynamicForm"
    @submit="handleSubmit"
  />
</template>

<script>
export default {
  data() {
    // 自定义验证器
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else if (value.length < 6) {
        callback(new Error('密码长度不能小于6位'));
      } else {
        callback();
      }
    };
    
    return {
      formData: {
        email: '',
        password: '',
        confirmPassword: ''
      },
      formSchema: {
        fields: [
          {
            type: 'input',
            field: 'email',
            label: '邮箱',
            rules: [
              { required: true, message: '请输入邮箱', trigger: 'blur' },
              { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
            ]
          },
          {
            type: 'input',
            field: 'password',
            label: '密码',
            props: {
              type: 'password',
              showPassword: true
            },
            rules: [
              { validator: validatePass, trigger: 'blur' }
            ]
          },
          {
            type: 'input',
            field: 'confirmPassword',
            label: '确认密码',
            props: {
              type: 'password',
              showPassword: true
            },
            rules: [
              { 
                validator: (rule, value, callback) => {
                  if (value === '') {
                    callback(new Error('请再次输入密码'));
                  } else if (value !== this.formData.password) {
                    callback(new Error('两次输入密码不一致'));
                  } else {
                    callback();
                  }
                }, 
                trigger: 'blur' 
              }
            ]
          }
        ]
      }
    };
  },
  methods: {
    handleSubmit() {
      this.$refs.dynamicForm.validate((valid) => {
        if (valid) {
          console.log('表单验证通过');
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    }
  }
}
</script>
``` 

## API 文档

### DynamicForm Props

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|-----|
| schema | Object | - | 表单配置对象 |
| model | Object | {} | 表单数据对象 |
| rules | Object | - | 表单验证规则（可选，也可在schema中配置） |
| inline | Boolean | false | 是否使用行内表单 |
| label-width | String | '80px' | 表单项标签宽度 |
| label-position | String | 'right' | 表单项标签位置，可选值: 'right', 'left', 'top' |
| size | String | - | 表单尺寸，可选值: 'large', 'default', 'small' |
| disabled | Boolean | false | 是否禁用整个表单 |
| show-message | Boolean | true | 是否显示校验错误信息 |
| status-icon | Boolean | false | 是否在输入框中显示校验结果反馈图标 |
| hide-required-asterisk | Boolean | false | 是否隐藏必填字段的标签旁边的红色星号 |

### Schema 配置

#### Schema 基本结构

```js
{
  layout: 'default', // 布局方式，可选值: 'default', 'grid', 'inline'
  gridConfig: { gutter: 20 }, // 栅格布局配置
  fields: [ // 表单项配置数组
    {
      type: 'input', // 组件类型
      field: 'name', // 字段名，对应model中的属性
      label: '姓名', // 标签名
      value: '', // 默认值
      props: {}, // 传递给组件的属性
      events: {}, // 组件事件
      rules: [], // 验证规则
      gridSpan: 12, // 栅格布局时的列数
      visible: true, // 是否可见，可以是布尔值或函数
      disabled: false // 是否禁用，可以是布尔值或函数
    }
  ],
  buttons: [ // 按钮配置
    {
      text: '提交',
      type: 'primary',
      action: 'submit',
      props: {},
      visible: true,
      disabled: false
    }
  ]
}
```

#### 支持的表单项类型

| 类型 | 说明 | 对应Element组件 |
|-----|------|--------------|
| input | 输入框 | el-input |
| input-number | 数字输入框 | el-input-number |
| select | 选择器 | el-select |
| radio | 单选框 | el-radio |
| radio-group | 单选框组 | el-radio-group |
| checkbox | 复选框 | el-checkbox |
| checkbox-group | 复选框组 | el-checkbox-group |
| switch | 开关 | el-switch |
| slider | 滑块 | el-slider |
| time-picker | 时间选择器 | el-time-picker |
| date-picker | 日期选择器 | el-date-picker |
| rate | 评分 | el-rate |
| color-picker | 颜色选择器 | el-color-picker |
| cascader | 级联选择器 | el-cascader |
| upload | 上传 | el-upload |
| editor | 富文本编辑器 | 自定义组件 |
| dynamic-list | 动态列表 | 自定义组件 |
| custom | 自定义组件 | - |

### DynamicForm Events

| 事件名 | 参数 | 说明 |
|-------|------|-----|
| submit | formData | 表单提交时触发 |
| validate | prop, valid, message | 表单验证时触发 |
| change | field, value | 表单项值改变时触发 |
| field-change | field, value | 同change |

### DynamicForm Methods

| 方法名 | 参数 | 说明 |
|-------|------|-----|
| validate | [callback] | 对整个表单进行校验 |
| validateField | prop, [callback] | 对表单部分字段进行校验 |
| resetFields | - | 重置表单 |
| clearValidate | [props] | 清除表单项的校验结果 |
| setFieldValue | field, value | 设置表单项的值 |
| getFieldValue | field | 获取表单项的值 |

## 源码实现

<details>
<summary>点击查看源码实现</summary>

### DynamicForm.vue

```vue
<template>
  <el-form
    :model="innerModel"
    :rules="mergedRules"
    :inline="inline"
    :label-width="labelWidth"
    :label-position="labelPosition"
    :size="size"
    :disabled="disabled"
    :show-message="showMessage"
    :status-icon="statusIcon"
    :hide-required-asterisk="hideRequiredAsterisk"
    ref="form"
    @submit.native.prevent="handleSubmit"
  >
    <!-- 栅格布局 -->
    <el-row v-if="schema.layout === 'grid'" :gutter="gridConfig.gutter">
      <el-col
        v-for="(field, index) in renderFields"
        :key="field.field + index"
        :span="field.gridSpan || 24"
        :offset="field.gridOffset || 0"
      >
        <form-field
          :field="field"
          :model="innerModel"
          :form-ref="$refs.form"
          @field-change="handleFieldChange"
        >
          <!-- 传递自定义组件插槽 -->
          <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
            <slot :name="slotName" v-bind="scope"></slot>
          </template>
        </form-field>
      </el-col>
    </el-row>

    <!-- 默认布局 -->
    <template v-else>
      <form-field
        v-for="(field, index) in renderFields"
        :key="field.field + index"
        :field="field"
        :model="innerModel"
        :form-ref="$refs.form"
        @field-change="handleFieldChange"
      >
        <!-- 传递自定义组件插槽 -->
        <template v-for="(_, slotName) in $scopedSlots" #[slotName]="scope">
          <slot :name="slotName" v-bind="scope"></slot>
        </template>
      </form-field>
    </template>

    <!-- 按钮区域 -->
    <el-form-item v-if="showButtons">
      <template v-for="(button, index) in renderButtons">
        <el-button
          :key="index"
          :type="button.type"
          :size="button.size || size"
          :disabled="resolveDisabled(button.disabled)"
          v-bind="button.props || {}"
          @click="handleButtonClick(button)"
        >
          {{ button.text }}
        </el-button>
      </template>
    </el-form-item>
  </el-form>
</template>

<script>
import FormField from './FormField.vue';
import { cloneDeep, merge } from 'lodash-es';

export default {
  name: 'DynamicForm',
  
  components: {
    FormField
  },
  
  props: {
    schema: {
      type: Object,
      required: true,
      default: () => ({
        fields: [],
        buttons: []
      })
    },
    model: {
      type: Object,
      default: () => ({})
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    inline: {
      type: Boolean,
      default: false
    },
    labelWidth: {
      type: String,
      default: '80px'
    },
    labelPosition: {
      type: String,
      default: 'right'
    },
    size: String,
    disabled: Boolean,
    showMessage: {
      type: Boolean,
      default: true
    },
    statusIcon: Boolean,
    hideRequiredAsterisk: Boolean,
    showButtons: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      innerModel: {},
      defaultButtons: [
        { text: '提交', type: 'primary', action: 'submit' },
        { text: '重置', action: 'reset' }
      ]
    };
  },
  
  computed: {
    // 合并规则
    mergedRules() {
      const schemaRules = {};
      
      (this.schema.fields || []).forEach(field => {
        if (field.rules) {
          schemaRules[field.field] = field.rules;
        }
      });
      
      return merge({}, schemaRules, this.rules);
    },
    
    // 获取需要渲染的字段
    renderFields() {
      return (this.schema.fields || []).filter(field => {
        if (typeof field.visible === 'function') {
          return field.visible(this.innerModel);
        }
        return field.visible !== false;
      });
    },
    
    // 获取需要渲染的按钮
    renderButtons() {
      const buttons = this.schema.buttons || this.defaultButtons;
      
      return buttons.filter(button => {
        if (typeof button.visible === 'function') {
          return button.visible(this.innerModel);
        }
        return button.visible !== false;
      });
    },
    
    // 栅格配置
    gridConfig() {
      return {
        gutter: 20,
        ...(this.schema.gridConfig || {})
      };
    }
  },
  
  watch: {
    model: {
      handler(val) {
        this.innerModel = cloneDeep(val);
      },
      deep: true,
      immediate: true
    },
    
    innerModel: {
      handler(val) {
        this.$emit('input', val);
        this.$emit('update:model', val);
      },
      deep: true
    }
  },
  
  methods: {
    // 处理表单项变化
    handleFieldChange(field, value) {
      this.$emit('change', field, value);
      this.$emit('field-change', field, value);
    },
    
    // 处理按钮点击
    handleButtonClick(button) {
      const action = button.action;
      
      if (action === 'submit') {
        this.handleSubmit();
      } else if (action === 'reset') {
        this.resetFields();
      } else if (typeof action === 'function') {
        action(this.innerModel, this);
      }
      
      this.$emit('button-click', button, this.innerModel);
    },
    
    // 处理表单提交
    handleSubmit() {
      this.validate(valid => {
        if (valid) {
          this.$emit('submit', this.innerModel);
        }
      });
    },
    
    // 判断是否禁用
    resolveDisabled(disabled) {
      if (typeof disabled === 'function') {
        return disabled(this.innerModel);
      }
      return disabled === true;
    },
    
    // 表单验证
    validate(callback) {
      if (this.$refs.form) {
        return this.$refs.form.validate(callback);
      }
    },
    
    // 验证表单字段
    validateField(prop, callback) {
      if (this.$refs.form) {
        this.$refs.form.validateField(prop, callback);
      }
    },
    
    // 重置表单
    resetFields() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    
    // 清除验证
    clearValidate(props) {
      if (this.$refs.form) {
        this.$refs.form.clearValidate(props);
      }
    },
    
    // 设置字段值
    setFieldValue(field, value) {
      if (field && this.innerModel) {
        this.$set(this.innerModel, field, value);
      }
    },
    
    // 获取字段值
    getFieldValue(field) {
      if (field && this.innerModel) {
        return this.innerModel[field];
      }
      return undefined;
    }
  }
};
</script>
```

### FormField.vue

```vue
<template>
  <el-form-item
    v-if="renderField"
    :label="field.label"
    :prop="field.field"
    :rules="field.rules"
    :error="field.error"
    :required="field.required"
  >
    <!-- 输入框 -->
    <el-input
      v-if="field.type === 'input'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-input>
    
    <!-- 数字输入框 -->
    <el-input-number
      v-else-if="field.type === 'input-number'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-input-number>
    
    <!-- 选择器 -->
    <el-select
      v-else-if="field.type === 'select'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    >
      <el-option
        v-for="option in field.options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      ></el-option>
    </el-select>
    
    <!-- 单选框组 -->
    <el-radio-group
      v-else-if="field.type === 'radio-group'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    >
      <el-radio
        v-for="option in field.options"
        :key="option.value"
        :label="option.value"
        :disabled="option.disabled"
      >{{ option.label }}</el-radio>
    </el-radio-group>
    
    <!-- 复选框组 -->
    <el-checkbox-group
      v-else-if="field.type === 'checkbox-group'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    >
      <el-checkbox
        v-for="option in field.options"
        :key="option.value"
        :label="option.value"
        :disabled="option.disabled"
      >{{ option.label }}</el-checkbox>
    </el-checkbox-group>
    
    <!-- 开关 -->
    <el-switch
      v-else-if="field.type === 'switch'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-switch>
    
    <!-- 滑块 -->
    <el-slider
      v-else-if="field.type === 'slider'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-slider>
    
    <!-- 时间选择器 -->
    <el-time-picker
      v-else-if="field.type === 'time-picker'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-time-picker>
    
    <!-- 日期选择器 -->
    <el-date-picker
      v-else-if="field.type === 'date-picker'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-date-picker>
    
    <!-- 评分 -->
    <el-rate
      v-else-if="field.type === 'rate'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-rate>
    
    <!-- 颜色选择器 -->
    <el-color-picker
      v-else-if="field.type === 'color-picker'"
      v-model="model[field.field]"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-color-picker>
    
    <!-- 级联选择器 -->
    <el-cascader
      v-else-if="field.type === 'cascader'"
      v-model="model[field.field]"
      :options="field.options"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></el-cascader>
    
    <!-- 上传 -->
    <el-upload
      v-else-if="field.type === 'upload'"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip" v-if="field.tips">{{ field.tips }}</div>
    </el-upload>
    
    <!-- 动态列表 -->
    <dynamic-list
      v-else-if="field.type === 'dynamic-list'"
      v-model="model[field.field]"
      :config="field"
      v-bind="field.props || {}"
      v-on="getEvents(field)"
    ></dynamic-list>
    
    <!-- 自定义组件 -->
    <slot
      v-else-if="field.type === 'custom'"
      :name="field.component"
      :model="model"
      :field="field.field"
    ></slot>
  </el-form-item>
</template>

<script>
export default {
  name: 'FormField',
  
  props: {
    field: {
      type: Object,
      required: true
    },
    model: {
      type: Object,
      required: true
    },
    formRef: Object
  },
  
  computed: {
    // 判断当前字段是否需要渲染
    renderField() {
      const { visible, disabled } = this.field;
      
      // 处理可见性
      if (typeof visible === 'function') {
        return visible(this.model);
      } else if (visible === false) {
        return false;
      }
      
      return true;
    }
  },
  
  methods: {
    // 获取组件事件
    getEvents(field) {
      // 默认事件
      const defaultEvents = {
        change: value => this.handleFieldChange(field.field, value)
      };
      
      // 合并自定义事件
      return { ...defaultEvents, ...(field.events || {}) };
    },
    
    // 处理字段值变化
    handleFieldChange(field, value) {
      // 触发字段改变事件
      this.$emit('field-change', field, value);
      
      // 如果有需要联动的字段，处理联动逻辑
      const linkage = this.field.linkage;
      if (linkage && typeof linkage === 'function') {
        linkage(field, value, this.model, this.formRef);
      }
    }
  }
};
</script>
```

### DynamicList.vue

```vue
<template>
  <div class="dynamic-list">
    <div
      class="dynamic-list__item"
      v-for="(item, index) in internalValue"
      :key="index"
    >
      <div class="dynamic-list__item-header">
        <span class="dynamic-list__item-title">{{ getItemLabel(index) }}</span>
        <el-button
          v-if="showRemoveButton(index)"
          type="text"
          icon="el-icon-delete"
          @click="removeItem(index)"
        >删除</el-button>
      </div>
      
      <div class="dynamic-list__item-content">
        <template v-for="(field, fieldIndex) in config.itemFields">
          <form-field
            :key="fieldIndex"
            :field="{ ...field, field: `${index}.${field.field}` }"
            :model="getItemModel(item, index)"
            @field-change="handleFieldChange"
          ></form-field>
        </template>
      </div>
    </div>
    
    <div class="dynamic-list__actions" v-if="showAddButton">
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="addItem"
      >{{ config.addText || '添加' }}</el-button>
    </div>
  </div>
</template>

<script>
import FormField from './FormField.vue';
import { cloneDeep } from 'lodash-es';

export default {
  name: 'DynamicList',
  
  components: {
    FormField
  },
  
  props: {
    value: {
      type: Array,
      default: () => []
    },
    config: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      internalValue: []
    };
  },
  
  computed: {
    // 是否显示添加按钮
    showAddButton() {
      const max = this.config.max || Infinity;
      return this.internalValue.length < max;
    }
  },
  
  watch: {
    value: {
      handler(val) {
        this.internalValue = cloneDeep(val || []);
      },
      immediate: true,
      deep: true
    },
    
    internalValue: {
      handler(val) {
        this.$emit('input', val);
        this.$emit('change', val);
      },
      deep: true
    }
  },
  
  methods: {
    // 获取项目标签
    getItemLabel(index) {
      const label = this.config.itemLabel || '项目{index}';
      return label.replace('{index}', index + 1);
    },
    
    // 是否显示删除按钮
    showRemoveButton(index) {
      const min = this.config.min || 0;
      return this.internalValue.length > min;
    },
    
    // 添加项目
    addItem() {
      const template = cloneDeep(this.config.itemTemplate || {});
      this.internalValue.push(template);
      this.$emit('add', this.internalValue.length - 1, template);
    },
    
    // 删除项目
    removeItem(index) {
      const removedItem = this.internalValue[index];
      this.internalValue.splice(index, 1);
      this.$emit('remove', index, removedItem);
    },
    
    // 获取项目模型
    getItemModel(item, index) {
      const model = {};
      
      // 将数组项转换为对象模型
      this.config.itemFields.forEach(field => {
        const key = `${index}.${field.field}`;
        model[key] = item[field.field];
      });
      
      return {
        ...item,
        ...model
      };
    },
    
    // 处理字段变更
    handleFieldChange(fieldPath, value) {
      // 解析字段路径，例如：0.name
      const [index, field] = fieldPath.split('.');
      
      if (this.internalValue[index]) {
        this.$set(this.internalValue[index], field, value);
      }
    }
  }
};
</script>

<style scoped>
.dynamic-list__item {
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.dynamic-list__item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.dynamic-list__item-title {
  font-weight: bold;
}

.dynamic-list__item-content {
  padding: 16px;
}

.dynamic-list__actions {
  margin-top: 10px;
}
</style>
```

### 注册和使用

```js
// index.js
import DynamicForm from './components/DynamicForm.vue';
import FormField from './components/FormField.vue';
import DynamicList from './components/DynamicList.vue';

const components = [
  DynamicForm,
  FormField,
  DynamicList
];

const install = function(Vue) {
  components.forEach(component => {
    Vue.component(component.name, component);
  });
};

// 自动安装
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  install,
  DynamicForm,
  FormField,
  DynamicList
};
```

### 使用示例

```js
// main.js
import Vue from 'vue';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import DynamicForm from './components/dynamic-form';

Vue.use(ElementUI);
Vue.use(DynamicForm);
```

</details>

## 使用指南

### 基本步骤

1. 引入组件
```js
import Vue from 'vue';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import DynamicForm from './components/dynamic-form';

Vue.use(ElementUI);
Vue.use(DynamicForm);
```

2. 定义表单配置
```js
const formSchema = {
  fields: [
    {
      type: 'input',
      field: 'name',
      label: '姓名',
      rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
    },
    // 更多字段...
  ]
};
```

3. 使用组件
```vue
<dynamic-form
  :schema="formSchema"
  :model="formData"
  @submit="handleSubmit"
/>
```

### 最佳实践

1. **将表单配置提取为独立文件**
```js
// form-schema.js
export const userFormSchema = {
  fields: [
    // 字段配置...
  ]
};
```

2. **使用计算属性处理动态配置**
```js
computed: {
  dynamicSchema() {
    return {
      ...this.baseSchema,
      fields: this.baseSchema.fields.map(field => {
        // 根据条件动态修改字段属性
        if (field.field === 'category' && this.userType === 'admin') {
          return { ...field, disabled: false };
        }
        return field;
      })
    };
  }
}
```

3. **使用自定义组件扩展功能**
```vue
<dynamic-form :schema="schema">
  <template #custom-address="{ model, field }">
    <!-- 自定义地址选择组件 -->
  </template>
</dynamic-form>
```

4. **组合使用动态表单和静态表单**
对于复杂表单，可以混合使用动态表单和手工编写的表单，以获得最大的灵活性。

5. **表单联动处理**
使用visible和disabled的函数形式，实现表单项之间的联动逻辑。

6. **存储和加载表单模板**
可以将表单配置存储在数据库中，实现表单的动态配置和加载。

## 常见问题

1. **如何处理复杂的表单联动？**
   
   使用visible函数和linkage配置，可以实现复杂的联动逻辑。

2. **如何自定义表单验证规则？**
   
   结合Element UI的表单验证规则和自定义验证函数，可以实现复杂的验证逻辑。

3. **如何实现表单项的条件渲染？**
   
   使用visible属性，可以根据条件控制表单项的显示和隐藏。

4. **如何扩展支持的组件类型？**
   
   在FormField.vue中添加新的组件类型判断和渲染逻辑。

5. **如何处理表单数据的初始化和重置？**
   
   使用resetFields方法，结合watch监听model变化，实现表单数据的初始化和重置。
</rewritten_file> 

## 源码实现

本页源码实现见文中“源码实现”折叠块，包含 `DynamicForm.vue`、`FormField.vue`、`DynamicList.vue` 及注册使用示例。