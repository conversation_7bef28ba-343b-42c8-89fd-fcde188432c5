# 前端工程化建设：5个阶段实施指南

## 核心理念
前端工程化 = **效率提升** + **质量保障** + **团队协作** + **可维护性**

## 5个阶段路线图

### 🎯 阶段1：代码规范化 (1-2周) - **重点：统一标准**
**核心目标**：建立团队统一的代码标准和开发规范

**必做事项**：
1. **ESLint + Prettier配置** (2天)
   - 创建团队ESLint规则
   - 配置Prettier格式化
   - 集成到编辑器

2. **Git规范** (1天)
   - Conventional Commits规范
   - 配置commitlint
   - 设置Git Hooks

3. **TypeScript配置** (2天)
   - 统一tsconfig配置
   - 类型定义规范
   - 路径别名配置

**验收标准**：
- ✅ 所有代码通过ESLint检查
- ✅ 提交信息符合规范
- ✅ TypeScript无类型错误

**技术选型**：
```bash
# 核心工具
npm install -D eslint prettier typescript
npm install -D husky lint-staged @commitlint/cli
```

### 🏗️ 阶段2：构建工具链 (2-3周) - **重点：自动化构建**
**核心目标**：建立高效的构建和开发环境

**必做事项**：
1. **构建工具选择** (3天)
   - Vite（推荐）或Webpack配置
   - 开发服务器配置
   - 生产环境优化

2. **开发体验优化** (2天)
   - 热重载配置
   - 代理配置
   - 错误提示优化

3. **构建优化** (2天)
   - 代码分割
   - 资源压缩
   - 缓存策略

**验收标准**：
- ✅ 开发服务器启动时间 < 5秒
- ✅ 热重载响应时间 < 1秒
- ✅ 生产构建体积合理

**关键配置**：
```javascript
// vite.config.js 核心配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'dayjs']
        }
      }
    }
  }
});
```

### 🧪 阶段3：测试体系 (2-3周) - **重点：质量保障**
**核心目标**：建立完整的自动化测试体系

**必做事项**：
1. **单元测试** (4天)
   - Jest/Vitest配置
   - 测试工具库配置
   - 覆盖率要求设定

2. **E2E测试** (3天)
   - Cypress/Playwright配置
   - 关键流程测试
   - 自动化测试报告

**验收标准**：
- ✅ 单元测试覆盖率 > 80%
- ✅ 关键业务流程E2E测试覆盖
- ✅ 测试运行时间 < 5分钟

**测试策略**：
```javascript
// 测试金字塔
单元测试 (70%) - 组件、工具函数
集成测试 (20%) - 页面、模块
E2E测试 (10%) - 关键业务流程
```

### 🚀 阶段4：CI/CD流水线 (1-2周) - **重点：自动化部署**
**核心目标**：实现从代码提交到生产部署的全自动化

**必做事项**：
1. **CI配置** (3天)
   - 代码质量检查
   - 自动化测试
   - 构建验证

2. **CD配置** (2天)
   - 多环境部署
   - 自动化发布
   - 回滚机制

**验收标准**：
- ✅ 代码合并自动触发部署
- ✅ 部署失败自动回滚
- ✅ 部署时间 < 10分钟

**GitHub Actions核心配置**：
```yaml
# 关键流水线
name: CI/CD
on: [push, pull_request]
jobs:
  test: # 测试
  build: # 构建
  deploy: # 部署
```

### 📦 阶段5：组件库/工具库 (3-4周) - **重点：复用和标准化**
**核心目标**：建立团队共享的组件库和工具库

**必做事项**：
1. **设计系统** (1周)
   - 设计令牌定义
   - 组件API规范
   - 主题系统

2. **基础组件** (2周)
   - Button、Input、Modal等
   - 组件测试
   - Storybook文档

3. **发布和维护** (1周)
   - npm包发布
   - 版本管理
   - 使用文档

**验收标准**：
- ✅ 20+基础组件
- ✅ 组件测试覆盖率 > 90%
- ✅ 完整的使用文档

## 🎯 重点发展方向

### 第1-2个月：**工具链熟练度**
- 掌握构建工具原理和配置
- 熟练使用调试和开发工具
- 建立自动化思维

### 第3-4个月：**架构设计能力**
- 理解工程化架构设计
- 掌握性能优化策略
- 学会技术选型决策

### 第5-6个月：**团队协作能力**
- 建立团队开发规范
- 推动工程化落地
- 培养团队工程化意识

## ⚡ 快速启动清单

### 第1周必须完成
- [ ] 安装Node.js + pnpm + VS Code
- [ ] 配置ESLint + Prettier
- [ ] 设置Git Hooks
- [ ] 创建第一个标准化项目

### 第1个月必须完成
- [ ] 完成构建工具链配置
- [ ] 建立基础测试环境
- [ ] 搭建CI/CD流水线
- [ ] 创建项目模板

### 第3个月必须完成
- [ ] 完成组件库开发
- [ ] 建立完整文档系统
- [ ] 团队全面使用新工具链
- [ ] 建立性能监控体系

## 🔥 关键成功因素

1. **从痛点出发**：解决团队最迫切的问题
2. **小步快跑**：每周都要有可见的进展
3. **数据驱动**：用数据证明工程化的价值
4. **团队共识**：确保团队理解和支持

**记住**：工程化不是目的，而是手段。目标是让团队更高效地交付高质量的产品。

## 前端工程化概述

前端工程化是指将软件工程的方法和实践应用到前端开发中，通过工具、流程、规范的标准化来提升开发效率、保证代码质量、降低维护成本的系统性方法。

### 核心价值
- **提升开发效率**：统一的工具链和规范减少重复工作
- **保证代码质量**：自动化检查和测试确保代码质量
- **降低维护成本**：标准化的项目结构和工具
- **提升团队协作**：统一的开发规范和流程
- **可扩展性**：支持大型项目和团队的协作开发
- **可维护性**：清晰的架构和文档便于长期维护

### 工程化的层次
1. **工具层**：构建工具、开发工具、测试工具
2. **规范层**：代码规范、Git规范、文档规范
3. **流程层**：开发流程、测试流程、发布流程
4. **平台层**：CI/CD平台、监控平台、文档平台

## 学习路径规划

### 前置知识要求
- **基础技能**：HTML、CSS、JavaScript熟练掌握
- **框架经验**：至少熟悉一个主流框架（React/Vue/Angular）
- **Node.js基础**：了解npm/yarn包管理和基本API
- **Git版本控制**：熟练使用Git进行版本管理

### 学习阶段规划

#### 第一阶段：工具链基础 (2-3周)
**学习目标**：掌握基础工具的使用和配置

**学习内容**：
1. **包管理工具**
   - npm/yarn/pnpm的区别和使用
   - package.json配置详解
   - 依赖管理最佳实践

2. **构建工具**
   - Webpack基础概念和配置
   - Vite快速上手
   - Rollup库构建

3. **代码规范工具**
   - ESLint配置和规则
   - Prettier代码格式化
   - EditorConfig统一编辑器配置

**实践任务**：
- [ ] 手动配置一个Webpack项目
- [ ] 使用Vite创建React/Vue项目
- [ ] 配置ESLint + Prettier工作流

#### 第二阶段：自动化流程 (3-4周)
**学习目标**：建立自动化的开发和部署流程

**学习内容**：
1. **Git工作流**
   - Git Flow/GitHub Flow
   - Conventional Commits规范
   - Git Hooks使用

2. **测试体系**
   - 单元测试框架（Jest/Vitest）
   - 组件测试（Testing Library）
   - E2E测试（Cypress/Playwright）

3. **CI/CD基础**
   - GitHub Actions入门
   - 自动化测试和部署
   - 环境管理

**实践任务**：
- [ ] 搭建完整的测试环境
- [ ] 配置Git Hooks自动检查
- [ ] 创建基础CI/CD流水线

#### 第三阶段：工程化架构 (4-5周)
**学习目标**：设计和实现完整的工程化架构

**学习内容**：
1. **脚手架开发**
   - CLI工具开发（Commander.js）
   - 模板引擎使用
   - 交互式命令行

2. **Monorepo管理**
   - Lerna/Rush/Nx使用
   - 包依赖管理
   - 版本发布策略

3. **组件库开发**
   - 组件设计原则
   - 文档系统（Storybook）
   - 主题系统设计

**实践任务**：
- [ ] 开发自己的脚手架工具
- [ ] 搭建Monorepo项目
- [ ] 创建基础组件库

#### 第四阶段：高级工程化 (3-4周)
**学习目标**：掌握高级工程化技术和最佳实践

**学习内容**：
1. **性能优化**
   - 构建性能优化
   - 运行时性能监控
   - 资源加载优化

2. **微前端架构**
   - Module Federation
   - Single-SPA框架
   - 微前端最佳实践

3. **DevOps集成**
   - 容器化部署
   - 监控和日志
   - 安全性考虑

**实践任务**：
- [ ] 实现性能监控系统
- [ ] 搭建微前端架构
- [ ] 完善监控和告警

## 核心组成部分

### 1. 脚手架工具 (Scaffolding)
- **项目模板**：标准化的项目结构
- **代码生成器**：自动生成组件、页面、API等
- **配置管理**：统一的构建配置和环境变量

### 2. 构建工具链 (Build Tools)
- **打包工具**：Webpack、Vite、Rollup等
- **编译工具**：Babel、TypeScript、SWC等
- **样式处理**：PostCSS、Sass、Less等
- **资源优化**：图片压缩、代码分割、Tree Shaking

### 3. 代码质量保障 (Code Quality)
- **代码规范**：ESLint、Prettier、StyleLint
- **类型检查**：TypeScript、Flow
- **单元测试**：Jest、Vitest、Testing Library
- **E2E测试**：Cypress、Playwright、Puppeteer

### 4. 开发工具 (Development Tools)
- **本地开发服务器**：热重载、代理配置
- **调试工具**：Source Map、DevTools集成
- **Mock服务**：API Mock、数据模拟
- **文档工具**：Storybook、VuePress、Docusaurus

### 5. 部署与发布 (Deployment)
- **CI/CD流水线**：自动化构建、测试、部署
- **环境管理**：开发、测试、预发布、生产环境
- **版本管理**：语义化版本、变更日志
- **监控告警**：性能监控、错误追踪

### 6. 组件库与设计系统 (Design System)
- **UI组件库**：可复用的UI组件
- **设计规范**：颜色、字体、间距等设计token
- **图标库**：统一的图标管理
- **主题系统**：多主题支持

## 详细实施步骤

### 准备阶段：环境搭建和知识储备 (1周)

#### Day 1-2: 环境准备
**目标**：搭建完整的开发环境

**具体步骤**：
1. **安装基础工具**
   ```bash
   # 安装Node.js (推荐LTS版本)
   # 从官网下载或使用nvm管理版本
   nvm install --lts
   nvm use --lts

   # 安装包管理工具
   npm install -g pnpm
   # 或者
   npm install -g yarn
   ```

2. **配置开发环境**
   ```bash
   # 配置npm镜像源
   npm config set registry https://registry.npmmirror.com

   # 配置Git
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

3. **IDE配置**
   - 安装VS Code及必要插件
   - 配置代码格式化和语法检查
   - 设置调试环境

#### Day 3-4: 知识体系梳理
**目标**：建立工程化知识框架

**学习清单**：
- [ ] 阅读《前端工程化体系设计与实践》
- [ ] 了解现代前端工具链生态
- [ ] 研究业界最佳实践案例
- [ ] 制定团队技术栈选型

#### Day 5-7: 项目调研
**目标**：分析现有项目状况，制定改进计划

**调研内容**：
1. **现状分析**
   - 项目数量和规模
   - 技术栈分布
   - 开发痛点识别
   - 团队技能水平

2. **需求收集**
   - 开发效率提升需求
   - 代码质量保障需求
   - 部署流程优化需求
   - 团队协作改进需求

### 阶段一：基础工具链搭建 (2-3周)

#### Week 1: 代码规范体系

**Day 1-2: ESLint配置**
```bash
# 1. 创建ESLint配置包
mkdir packages/eslint-config
cd packages/eslint-config
npm init -y

# 2. 安装依赖
npm install --save-dev \
  eslint \
  @typescript-eslint/parser \
  @typescript-eslint/eslint-plugin \
  eslint-plugin-react \
  eslint-plugin-react-hooks \
  eslint-plugin-import
```

**创建配置文件**：
```javascript
// packages/eslint-config/index.js
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:import/recommended'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: [
    'react',
    '@typescript-eslint',
    'import'
  ],
  rules: {
    // 基础规则
    'no-console': 'warn',
    'no-debugger': 'error',
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': 'error',

    // React规则
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',

    // 导入规则
    'import/order': ['error', {
      'groups': [
        'builtin',
        'external',
        'internal',
        'parent',
        'sibling',
        'index'
      ],
      'newlines-between': 'always'
    }]
  },
  settings: {
    react: {
      version: 'detect'
    },
    'import/resolver': {
      typescript: {}
    }
  }
};
```

**Day 3-4: Prettier配置**
```bash
# 创建Prettier配置包
mkdir packages/prettier-config
cd packages/prettier-config
npm init -y
npm install --save-dev prettier
```

```javascript
// packages/prettier-config/index.js
module.exports = {
  // 基础格式化规则
  semi: true,                    // 使用分号
  trailingComma: 'es5',         // 尾随逗号
  singleQuote: true,            // 使用单引号
  printWidth: 100,              // 行宽
  tabWidth: 2,                  // 缩进宽度
  useTabs: false,               // 使用空格缩进

  // JSX规则
  jsxSingleQuote: true,         // JSX中使用单引号
  jsxBracketSameLine: false,    // JSX标签换行

  // 其他规则
  bracketSpacing: true,         // 对象字面量空格
  arrowParens: 'avoid',         // 箭头函数参数括号
  endOfLine: 'lf',              // 换行符

  // 文件类型特殊配置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 80
      }
    },
    {
      files: '*.md',
      options: {
        proseWrap: 'always'
      }
    }
  ]
};
```

**Day 5-7: TypeScript配置**
```bash
# 创建TypeScript配置包
mkdir packages/tsconfig
cd packages/tsconfig
npm init -y
npm install --save-dev typescript
```

```json
// packages/tsconfig/base.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["DOM", "DOM.Iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/utils/*": ["src/utils/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/types/*": ["src/types/*"]
    }
  },
  "include": [
    "src/**/*",
    "types/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "build"
  ]
}
```

#### Week 2: 构建工具配置

**Day 8-10: Vite配置封装**
```bash
# 创建构建配置包
mkdir packages/build-config
cd packages/build-config
npm init -y

# 安装Vite相关依赖
npm install --save-dev \
  vite \
  @vitejs/plugin-react \
  @vitejs/plugin-vue \
  vite-plugin-eslint \
  vite-plugin-mock \
  rollup-plugin-visualizer
```

```javascript
// packages/build-config/vite.config.js
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import eslint from 'vite-plugin-eslint';
import { viteMockServe } from 'vite-plugin-mock';
import { visualizer } from 'rollup-plugin-visualizer';

export function createViteConfig(options = {}) {
  const {
    framework = 'react',
    enableMock = true,
    enableAnalyzer = false,
    proxy = {},
    alias = {},
    plugins = []
  } = options;

  const frameworkPlugins = {
    react: [react()],
    vue: [vue()]
  };

  return defineConfig(({ command, mode }) => {
    const isDev = command === 'serve';

    return {
      plugins: [
        // 框架插件
        ...frameworkPlugins[framework],

        // 代码检查
        eslint({
          include: ['src/**/*.{ts,tsx,js,jsx,vue}'],
          exclude: ['node_modules', 'dist']
        }),

        // Mock服务
        enableMock && viteMockServe({
          mockPath: 'mock',
          localEnabled: isDev,
          prodEnabled: false,
          logger: true
        }),

        // 构建分析
        enableAnalyzer && visualizer({
          filename: 'dist/stats.html',
          open: true,
          gzipSize: true
        }),

        // 自定义插件
        ...plugins
      ].filter(Boolean),

      resolve: {
        alias: {
          '@': resolve(process.cwd(), 'src'),
          '@/components': resolve(process.cwd(), 'src/components'),
          '@/utils': resolve(process.cwd(), 'src/utils'),
          '@/hooks': resolve(process.cwd(), 'src/hooks'),
          '@/types': resolve(process.cwd(), 'src/types'),
          '@/assets': resolve(process.cwd(), 'src/assets'),
          ...alias
        }
      },

      css: {
        modules: {
          localsConvention: 'camelCase'
        },
        preprocessorOptions: {
          scss: {
            additionalData: `@import "@/styles/variables.scss";`
          }
        }
      },

      build: {
        outDir: 'dist',
        sourcemap: !isDev,
        minify: 'terser',
        terserOptions: {
          compress: {
            drop_console: !isDev,
            drop_debugger: !isDev
          }
        },
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['react', 'react-dom'],
              utils: ['lodash', 'dayjs'],
              ...options.chunks
            }
          }
        },
        chunkSizeWarningLimit: 1000
      },

      server: {
        port: 3000,
        open: true,
        cors: true,
        proxy: {
          '/api': {
            target: 'http://localhost:8080',
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '')
          },
          ...proxy
        }
      },

      optimizeDeps: {
        include: ['react', 'react-dom'],
        exclude: ['@vite/client', '@vite/env']
      }
    };
  });
}
```

**Day 11-14: 脚手架CLI开发**
```bash
# 创建CLI工具
mkdir packages/cli
cd packages/cli
npm init -y

# 安装CLI开发依赖
npm install \
  commander \
  inquirer \
  chalk \
  ora \
  fs-extra \
  mustache \
  download-git-repo
```

```javascript
// packages/cli/src/commands/create.js
const inquirer = require('inquirer');
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs-extra');
const path = require('path');
const mustache = require('mustache');

async function createProject(projectName, options) {
  console.log(chalk.blue(`🚀 创建项目: ${projectName}`));

  // 1. 收集用户配置
  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'framework',
      message: '选择前端框架:',
      choices: [
        { name: 'React', value: 'react' },
        { name: 'Vue 3', value: 'vue3' },
        { name: 'Vue 2', value: 'vue2' }
      ],
      default: 'react'
    },
    {
      type: 'list',
      name: 'language',
      message: '选择开发语言:',
      choices: [
        { name: 'TypeScript', value: 'typescript' },
        { name: 'JavaScript', value: 'javascript' }
      ],
      default: 'typescript'
    },
    {
      type: 'checkbox',
      name: 'features',
      message: '选择需要的功能:',
      choices: [
        { name: 'Router', value: 'router' },
        { name: 'State Management', value: 'state' },
        { name: 'UI Library', value: 'ui' },
        { name: 'Testing', value: 'testing' },
        { name: 'PWA', value: 'pwa' }
      ]
    },
    {
      type: 'list',
      name: 'cssPreprocessor',
      message: '选择CSS预处理器:',
      choices: [
        { name: 'Sass/SCSS', value: 'scss' },
        { name: 'Less', value: 'less' },
        { name: 'Stylus', value: 'stylus' },
        { name: 'None', value: 'none' }
      ],
      default: 'scss'
    }
  ]);

  // 2. 创建项目目录
  const targetDir = path.resolve(process.cwd(), projectName);

  if (fs.existsSync(targetDir)) {
    const { overwrite } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'overwrite',
        message: `目录 ${projectName} 已存在，是否覆盖？`,
        default: false
      }
    ]);

    if (!overwrite) {
      console.log(chalk.yellow('❌ 操作已取消'));
      return;
    }

    await fs.remove(targetDir);
  }

  // 3. 复制模板文件
  const spinner = ora('正在创建项目...').start();

  try {
    const templateDir = path.resolve(__dirname, `../../templates/${answers.framework}-${answers.language}`);
    await fs.copy(templateDir, targetDir);

    // 4. 渲染模板变量
    await renderTemplate(targetDir, {
      projectName,
      ...answers
    });

    // 5. 安装依赖
    spinner.text = '正在安装依赖...';
    await installDependencies(targetDir, answers);

    spinner.succeed(chalk.green('✅ 项目创建成功！'));

    // 6. 显示后续步骤
    console.log(chalk.cyan('\n📋 后续步骤:'));
    console.log(chalk.gray(`  cd ${projectName}`));
    console.log(chalk.gray('  npm run dev'));

  } catch (error) {
    spinner.fail(chalk.red('❌ 项目创建失败'));
    console.error(error);
  }
}

async function renderTemplate(targetDir, data) {
  const files = await fs.readdir(targetDir, { recursive: true });

  for (const file of files) {
    const filePath = path.join(targetDir, file);
    const stat = await fs.stat(filePath);

    if (stat.isFile() && (file.endsWith('.json') || file.endsWith('.js') || file.endsWith('.ts'))) {
      const content = await fs.readFile(filePath, 'utf-8');
      const rendered = mustache.render(content, data);
      await fs.writeFile(filePath, rendered);
    }
  }
}

module.exports = { createProject };
```

#### Week 2-3: 构建配置优化

**Day 15-17: 开发服务器配置**
```javascript
// packages/build-config/dev-server.js
export function createDevServer(options = {}) {
  const {
    port = 3000,
    host = 'localhost',
    https = false,
    proxy = {},
    mock = true
  } = options;

  return {
    server: {
      port,
      host,
      https,
      open: true,
      cors: true,

      // 代理配置
      proxy: {
        '/api': {
          target: process.env.API_BASE_URL || 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('Sending Request:', req.method, req.url);
            });
          }
        },
        ...proxy
      },

      // 中间件
      middlewareMode: false
    },

    // 环境变量
    define: {
      __DEV__: JSON.stringify(true),
      __VERSION__: JSON.stringify(process.env.npm_package_version)
    }
  };
}
```

**Day 18-21: 测试环境搭建**
```bash
# 创建测试配置包
mkdir packages/test-config
cd packages/test-config
npm init -y

# 安装测试依赖
npm install --save-dev \
  vitest \
  @testing-library/react \
  @testing-library/jest-dom \
  @testing-library/user-event \
  jsdom \
  c8
```

```javascript
// packages/test-config/vitest.config.js
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export function createTestConfig(options = {}) {
  return defineConfig({
    plugins: [react()],
    test: {
      environment: 'jsdom',
      setupFiles: ['./src/setupTests.ts'],
      globals: true,
      css: true,

      // 覆盖率配置
      coverage: {
        provider: 'c8',
        reporter: ['text', 'json', 'html'],
        exclude: [
          'node_modules/',
          'src/setupTests.ts',
          'src/**/*.stories.{ts,tsx}',
          'src/**/*.test.{ts,tsx}',
          'src/types/',
          '**/*.d.ts'
        ],
        thresholds: {
          global: {
            branches: 80,
            functions: 80,
            lines: 80,
            statements: 80
          }
        }
      },

      // 别名配置
      alias: {
        '@': resolve(process.cwd(), 'src'),
        '@/components': resolve(process.cwd(), 'src/components'),
        '@/utils': resolve(process.cwd(), 'src/utils'),
        ...options.alias
      }
    }
  });
}
```

### 阶段二：自动化流程建设 (2-3周)

#### Week 4: Git工作流规范

**Day 22-24: Git Hooks配置**
```bash
# 安装Git Hooks工具
npm install --save-dev husky lint-staged commitizen cz-conventional-changelog

# 初始化husky
npx husky install
npm pkg set scripts.prepare="husky install"

# 添加pre-commit钩子
npx husky add .husky/pre-commit "npx lint-staged"

# 添加commit-msg钩子
npx husky add .husky/commit-msg "npx commitlint --edit $1"
```

```json
// package.json配置
{
  "scripts": {
    "commit": "git-cz",
    "prepare": "husky install"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "*.{css,scss,less}": [
      "stylelint --fix",
      "prettier --write",
      "git add"
    ],
    "*.{json,md}": [
      "prettier --write",
      "git add"
    ]
  },
  "config": {
    "commitizen": {
      "path": "cz-conventional-changelog"
    }
  }
}
```

**Day 25-28: CI/CD流水线**
```yaml
# .github/workflows/ci.yml
name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # 代码质量检查
  quality:
    name: 🔍 Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🔍 Run ESLint
        run: pnpm lint

      - name: 🎨 Check Prettier
        run: pnpm format:check

      - name: 🔧 Type check
        run: pnpm type-check

  # 单元测试
  test:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: quality
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🧪 Run tests
        run: pnpm test:coverage

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # E2E测试
  e2e:
    name: 🎭 E2E Tests
    runs-on: ubuntu-latest
    needs: test
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🏗️ Build application
        run: pnpm build

      - name: 🎭 Run Playwright tests
        run: pnpm test:e2e

      - name: 📤 Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/

  # 构建和部署
  build-and-deploy:
    name: 🏗️ Build & Deploy
    runs-on: ubuntu-latest
    needs: [quality, test, e2e]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🏗️ Build for production
        run: pnpm build
        env:
          NODE_ENV: production

      - name: 📊 Analyze bundle
        run: pnpm analyze

      - name: 🚀 Deploy to staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "Deploying to staging environment..."
          # 部署到测试环境的脚本

      - name: 🚀 Deploy to production
        if: github.ref == 'refs/heads/main'
        run: |
          echo "Deploying to production environment..."
          # 部署到生产环境的脚本

### 阶段三：组件库和文档系统 (3-4周)

#### Week 5-6: 组件库开发

**Day 29-31: 组件库架构设计**
```bash
# 创建组件库项目
mkdir packages/ui-components
cd packages/ui-components
npm init -y

# 安装开发依赖
npm install --save-dev \
  react \
  react-dom \
  typescript \
  @types/react \
  @types/react-dom \
  rollup \
  @rollup/plugin-typescript \
  @rollup/plugin-node-resolve \
  @rollup/plugin-commonjs \
  rollup-plugin-peer-deps-external \
  rollup-plugin-postcss
```

**组件库目录结构**：
```
packages/ui-components/
├── src/
│   ├── components/           # 组件目录
│   │   ├── Button/
│   │   │   ├── index.ts     # 导出文件
│   │   │   ├── Button.tsx   # 组件实现
│   │   │   ├── Button.test.tsx
│   │   │   ├── Button.stories.tsx
│   │   │   └── Button.module.scss
│   │   ├── Input/
│   │   ├── Modal/
│   │   └── index.ts         # 统一导出
│   ├── hooks/               # 自定义Hooks
│   ├── utils/               # 工具函数
│   ├── types/               # 类型定义
│   ├── tokens/              # 设计令牌
│   │   ├── colors.ts
│   │   ├── spacing.ts
│   │   ├── typography.ts
│   │   └── index.ts
│   └── index.ts            # 主入口
├── docs/                   # 文档
├── stories/               # Storybook配置
├── tests/                 # 测试配置
├── build/                 # 构建脚本
└── package.json
```

**Day 32-35: 设计系统建立**
```typescript
// src/tokens/colors.ts
export const colors = {
  // 主色调
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',  // 主色
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e'
  },

  // 辅助色
  secondary: {
    50: '#fafafa',
    100: '#f4f4f5',
    200: '#e4e4e7',
    300: '#d4d4d8',
    400: '#a1a1aa',
    500: '#71717a',
    600: '#52525b',
    700: '#3f3f46',
    800: '#27272a',
    900: '#18181b'
  },

  // 功能色
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',

  // 文本色
  text: {
    primary: '#1f2937',
    secondary: '#6b7280',
    disabled: '#9ca3af',
    inverse: '#ffffff'
  },

  // 背景色
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6'
  }
};

// src/tokens/spacing.ts
export const spacing = {
  0: '0',
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  5: '1.25rem',   // 20px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  10: '2.5rem',   // 40px
  12: '3rem',     // 48px
  16: '4rem',     // 64px
  20: '5rem',     // 80px
  24: '6rem'      // 96px
};

// src/tokens/typography.ts
export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'Monaco', 'monospace']
  },

  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],      // 12px
    sm: ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
    base: ['1rem', { lineHeight: '1.5rem' }],     // 16px
    lg: ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
    xl: ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
    '2xl': ['1.5rem', { lineHeight: '2rem' }],    // 24px
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }]  // 36px
  },

  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  }
};
```

**Day 36-42: 基础组件开发**
```typescript
// src/components/Button/Button.tsx
import React, { forwardRef } from 'react';
import { clsx } from 'clsx';
import { colors, spacing } from '../../tokens';
import styles from './Button.module.scss';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 按钮变体 */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  /** 按钮尺寸 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** 是否为块级按钮 */
  fullWidth?: boolean;
  /** 加载状态 */
  loading?: boolean;
  /** 图标 */
  icon?: React.ReactNode;
  /** 图标位置 */
  iconPosition?: 'left' | 'right';
  /** 子元素 */
  children?: React.ReactNode;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      disabled = false,
      icon,
      iconPosition = 'left',
      className,
      children,
      ...props
    },
    ref
  ) => {
    const buttonClasses = clsx(
      styles.button,
      styles[`button--${variant}`],
      styles[`button--${size}`],
      {
        [styles['button--full-width']]: fullWidth,
        [styles['button--loading']]: loading,
        [styles['button--disabled']]: disabled || loading,
        [styles['button--icon-only']]: icon && !children
      },
      className
    );

    const renderIcon = () => {
      if (loading) {
        return <span className={styles.spinner} />;
      }
      return icon;
    };

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        {...props}
      >
        {icon && iconPosition === 'left' && (
          <span className={styles.icon}>{renderIcon()}</span>
        )}

        {children && <span className={styles.content}>{children}</span>}

        {icon && iconPosition === 'right' && (
          <span className={styles.icon}>{renderIcon()}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';
```

```scss
// src/components/Button/Button.module.scss
@import '../../tokens/index.scss';

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-2;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  user-select: none;

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px $color-primary-500;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  // 尺寸变体
  &--xs {
    padding: $spacing-1 $spacing-2;
    font-size: 0.75rem;
    line-height: 1rem;
  }

  &--sm {
    padding: $spacing-2 $spacing-3;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &--md {
    padding: $spacing-2 $spacing-4;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &--lg {
    padding: $spacing-3 $spacing-6;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  &--xl {
    padding: $spacing-4 $spacing-8;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  // 样式变体
  &--primary {
    background-color: $color-primary-500;
    color: white;

    &:hover:not(:disabled) {
      background-color: $color-primary-600;
    }

    &:active:not(:disabled) {
      background-color: $color-primary-700;
    }
  }

  &--secondary {
    background-color: $color-secondary-100;
    color: $color-secondary-900;

    &:hover:not(:disabled) {
      background-color: $color-secondary-200;
    }
  }

  &--outline {
    background-color: transparent;
    border-color: $color-primary-500;
    color: $color-primary-500;

    &:hover:not(:disabled) {
      background-color: $color-primary-50;
    }
  }

  &--ghost {
    background-color: transparent;
    color: $color-primary-500;

    &:hover:not(:disabled) {
      background-color: $color-primary-50;
    }
  }

  &--link {
    background-color: transparent;
    color: $color-primary-500;
    text-decoration: underline;

    &:hover:not(:disabled) {
      color: $color-primary-600;
    }
  }

  // 状态样式
  &--full-width {
    width: 100%;
  }

  &--loading {
    cursor: wait;
  }

  &--icon-only {
    padding: $spacing-2;
  }
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  display: flex;
  align-items: center;
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
```

#### Week 7-8: 文档和测试系统

**Day 43-45: Storybook配置**
```bash
# 安装Storybook
npx storybook@latest init

# 安装额外插件
npm install --save-dev \
  @storybook/addon-docs \
  @storybook/addon-controls \
  @storybook/addon-actions \
  @storybook/addon-viewport \
  @storybook/addon-a11y
```

```javascript
// .storybook/main.js
module.exports = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx|mdx)'],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-docs',
    '@storybook/addon-controls',
    '@storybook/addon-actions',
    '@storybook/addon-viewport',
    '@storybook/addon-a11y'
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {}
  },
  features: {
    buildStoriesJson: true
  },
  typescript: {
    check: false,
    reactDocgen: 'react-docgen-typescript',
    reactDocgenTypescriptOptions: {
      shouldExtractLiteralValuesFromEnum: true,
      propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true)
    }
  }
};
```

```typescript
// src/components/Button/Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: '按钮组件用于触发操作和导航。支持多种样式变体和尺寸。'
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'link'],
      description: '按钮的视觉样式变体'
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
      description: '按钮的尺寸大小'
    },
    disabled: {
      control: 'boolean',
      description: '是否禁用按钮'
    },
    loading: {
      control: 'boolean',
      description: '是否显示加载状态'
    },
    fullWidth: {
      control: 'boolean',
      description: '是否占满容器宽度'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基础示例
export const Default: Story = {
  args: {
    children: '按钮'
  }
};

// 所有变体
export const Variants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
    </div>
  )
};

// 所有尺寸
export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
      <Button size="xs">Extra Small</Button>
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
  )
};

// 状态示例
export const States: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
      <Button>Normal</Button>
      <Button disabled>Disabled</Button>
      <Button loading>Loading</Button>
    </div>
  )
};

// 带图标
export const WithIcon: Story = {
  args: {
    children: '下载',
    icon: '📥'
  }
};
```

**Day 46-49: 组件测试**
```typescript
// src/components/Button/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from './Button';

describe('Button Component', () => {
  // 基础渲染测试
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  // 变体测试
  it('applies correct variant classes', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    expect(screen.getByRole('button')).toHaveClass('button--primary');

    rerender(<Button variant="secondary">Secondary</Button>);
    expect(screen.getByRole('button')).toHaveClass('button--secondary');
  });

  // 尺寸测试
  it('applies correct size classes', () => {
    const { rerender } = render(<Button size="sm">Small</Button>);
    expect(screen.getByRole('button')).toHaveClass('button--sm');

    rerender(<Button size="lg">Large</Button>);
    expect(screen.getByRole('button')).toHaveClass('button--lg');
  });

  // 禁用状态测试
  it('handles disabled state correctly', () => {
    render(<Button disabled>Disabled</Button>);
    const button = screen.getByRole('button');

    expect(button).toBeDisabled();
    expect(button).toHaveClass('button--disabled');
  });

  // 加载状态测试
  it('shows loading state correctly', () => {
    render(<Button loading>Loading</Button>);
    const button = screen.getByRole('button');

    expect(button).toBeDisabled();
    expect(button).toHaveClass('button--loading');
    expect(button.querySelector('.spinner')).toBeInTheDocument();
  });

  // 点击事件测试
  it('handles click events', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();

    render(<Button onClick={handleClick}>Click me</Button>);

    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  // 禁用时不触发点击
  it('does not trigger click when disabled', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();

    render(<Button disabled onClick={handleClick}>Disabled</Button>);

    await user.click(screen.getByRole('button'));
    expect(handleClick).not.toHaveBeenCalled();
  });

  // 加载时不触发点击
  it('does not trigger click when loading', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();

    render(<Button loading onClick={handleClick}>Loading</Button>);

    await user.click(screen.getByRole('button'));
    expect(handleClick).not.toHaveBeenCalled();
  });

  // 全宽测试
  it('applies full width correctly', () => {
    render(<Button fullWidth>Full Width</Button>);
    expect(screen.getByRole('button')).toHaveClass('button--full-width');
  });

  // 图标测试
  it('renders icon correctly', () => {
    render(<Button icon={<span data-testid="icon">🚀</span>}>With Icon</Button>);
    expect(screen.getByTestId('icon')).toBeInTheDocument();
  });

  // 可访问性测试
  it('has correct accessibility attributes', () => {
    render(<Button aria-label="Custom label">Button</Button>);
    expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Custom label');
  });
});
```

### 阶段四：高级工程化特性 (2-3周)

#### Week 9-10: 性能优化和监控

**Day 50-52: 构建性能优化**
```javascript
// build/performance.js - 构建性能分析工具
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');

class BuildPerformanceAnalyzer {
  constructor(options = {}) {
    this.options = {
      enableAnalyzer: false,
      enableSpeedMeasure: false,
      outputPath: 'build-analysis',
      ...options
    };
  }

  getPlugins() {
    const plugins = [];

    // 包大小分析
    if (this.options.enableAnalyzer) {
      plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: `${this.options.outputPath}/bundle-report.html`,
          generateStatsFile: true,
          statsFilename: `${this.options.outputPath}/bundle-stats.json`
        })
      );
    }

    return plugins;
  }

  wrapConfig(config) {
    if (this.options.enableSpeedMeasure) {
      const smp = new SpeedMeasurePlugin({
        outputFormat: 'humanVerbose',
        outputTarget: `${this.options.outputPath}/speed-measure.txt`
      });
      return smp.wrap(config);
    }
    return config;
  }

  // 分析构建结果
  async analyzeBundle(statsPath) {
    const stats = require(statsPath);

    const analysis = {
      totalSize: 0,
      chunks: [],
      modules: [],
      duplicates: []
    };

    // 分析chunk大小
    stats.chunks.forEach(chunk => {
      analysis.totalSize += chunk.size;
      analysis.chunks.push({
        name: chunk.names[0],
        size: chunk.size,
        modules: chunk.modules.length
      });
    });

    // 查找重复模块
    const moduleMap = new Map();
    stats.modules.forEach(module => {
      if (moduleMap.has(module.name)) {
        analysis.duplicates.push(module.name);
      } else {
        moduleMap.set(module.name, module);
      }
    });

    return analysis;
  }
}

module.exports = { BuildPerformanceAnalyzer };
```

**Day 53-56: 运行时性能监控**
```typescript
// src/utils/performance.ts
interface PerformanceMetrics {
  // 页面加载性能
  pageLoad: {
    dns: number;
    tcp: number;
    request: number;
    response: number;
    dom: number;
    load: number;
    total: number;
  };

  // 资源加载性能
  resources: Array<{
    name: string;
    type: string;
    size: number;
    duration: number;
  }>;

  // 用户交互性能
  interactions: Array<{
    type: string;
    target: string;
    duration: number;
    timestamp: number;
  }>;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.init();
  }

  private init() {
    // 监听页面加载性能
    this.observeNavigation();

    // 监听资源加载性能
    this.observeResources();

    // 监听用户交互性能
    this.observeInteractions();

    // 监听Core Web Vitals
    this.observeWebVitals();
  }

  private observeNavigation() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

      this.metrics.pageLoad = {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        dom: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        load: navigation.loadEventEnd - navigation.loadEventStart,
        total: navigation.loadEventEnd - navigation.fetchStart
      };

      this.reportMetrics('pageLoad', this.metrics.pageLoad);
    });
  }

  private observeResources() {
    const observer = new PerformanceObserver((list) => {
      const resources = list.getEntries().map(entry => ({
        name: entry.name,
        type: entry.initiatorType,
        size: (entry as any).transferSize || 0,
        duration: entry.duration
      }));

      this.metrics.resources = [...(this.metrics.resources || []), ...resources];
      this.reportMetrics('resources', resources);
    });

    observer.observe({ entryTypes: ['resource'] });
    this.observers.push(observer);
  }

  private observeInteractions() {
    const observer = new PerformanceObserver((list) => {
      const interactions = list.getEntries().map(entry => ({
        type: entry.name,
        target: (entry as any).target || 'unknown',
        duration: entry.duration,
        timestamp: entry.startTime
      }));

      this.metrics.interactions = [...(this.metrics.interactions || []), ...interactions];
      this.reportMetrics('interactions', interactions);
    });

    observer.observe({ entryTypes: ['event'] });
    this.observers.push(observer);
  }

  private observeWebVitals() {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        if (entry.name === 'first-contentful-paint') {
          this.reportMetrics('FCP', entry.startTime);
        }
      });
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.reportMetrics('LCP', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      this.reportMetrics('CLS', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }

  private reportMetrics(type: string, data: any) {
    // 发送到监控服务
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'performance_metric', {
        metric_type: type,
        metric_value: data,
        custom_parameter: JSON.stringify(data)
      });
    }

    // 控制台输出（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.group(`📊 Performance Metric: ${type}`);
      console.log(data);
      console.groupEnd();
    }
  }

  // 获取性能报告
  getReport(): PerformanceMetrics {
    return this.metrics as PerformanceMetrics;
  }

  // 清理观察者
  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 使用示例
export const performanceMonitor = new PerformanceMonitor();

// React Hook封装
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = React.useState<Partial<PerformanceMetrics>>({});

  React.useEffect(() => {
    const monitor = new PerformanceMonitor();

    // 定期更新指标
    const interval = setInterval(() => {
      setMetrics(monitor.getReport());
    }, 5000);

    return () => {
      clearInterval(interval);
      monitor.disconnect();
    };
  }, []);

  return metrics;
}
```

#### Week 11: 部署和发布流程

**Day 57-59: 多环境部署配置**
```javascript
// build/deploy.js - 部署脚本
const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');

class DeployManager {
  constructor(config) {
    this.config = {
      environments: {
        development: {
          buildCommand: 'npm run build:dev',
          outputDir: 'dist',
          deployTarget: 'dev-server'
        },
        staging: {
          buildCommand: 'npm run build:staging',
          outputDir: 'dist',
          deployTarget: 'staging-server'
        },
        production: {
          buildCommand: 'npm run build:prod',
          outputDir: 'dist',
          deployTarget: 'prod-server'
        }
      },
      ...config
    };
  }

  async deploy(environment) {
    console.log(chalk.blue(`🚀 开始部署到 ${environment} 环境`));

    const envConfig = this.config.environments[environment];
    if (!envConfig) {
      throw new Error(`未找到环境配置: ${environment}`);
    }

    try {
      // 1. 构建项目
      await this.build(envConfig);

      // 2. 运行测试
      await this.runTests();

      // 3. 部署文件
      await this.deployFiles(envConfig);

      // 4. 健康检查
      await this.healthCheck(envConfig);

      console.log(chalk.green(`✅ ${environment} 环境部署成功`));

    } catch (error) {
      console.error(chalk.red(`❌ 部署失败: ${error.message}`));
      throw error;
    }
  }

  async build(envConfig) {
    console.log(chalk.yellow('📦 正在构建...'));

    // 清理输出目录
    await fs.remove(envConfig.outputDir);

    // 执行构建命令
    execSync(envConfig.buildCommand, { stdio: 'inherit' });

    // 验证构建结果
    const buildExists = await fs.pathExists(envConfig.outputDir);
    if (!buildExists) {
      throw new Error('构建失败：输出目录不存在');
    }
  }

  async runTests() {
    console.log(chalk.yellow('🧪 运行测试...'));

    try {
      execSync('npm run test:ci', { stdio: 'inherit' });
    } catch (error) {
      throw new Error('测试失败，停止部署');
    }
  }

  async deployFiles(envConfig) {
    console.log(chalk.yellow('📤 部署文件...'));

    // 这里实现具体的部署逻辑
    // 可能是上传到CDN、复制到服务器等
    switch (envConfig.deployTarget) {
      case 'aws-s3':
        await this.deployToS3(envConfig);
        break;
      case 'nginx':
        await this.deployToNginx(envConfig);
        break;
      default:
        console.log('使用默认部署方式');
    }
  }

  async healthCheck(envConfig) {
    console.log(chalk.yellow('🏥 健康检查...'));

    // 实现健康检查逻辑
    // 检查部署的应用是否正常运行
  }
}

module.exports = { DeployManager };
```

**Day 60-63: 版本管理和发布**
```javascript
// scripts/release.js - 版本发布脚本
const semver = require('semver');
const { execSync } = require('child_process');
const fs = require('fs-extra');
const chalk = require('chalk');
const inquirer = require('inquirer');

class ReleaseManager {
  constructor() {
    this.packageJson = require('../package.json');
    this.currentVersion = this.packageJson.version;
  }

  async release() {
    console.log(chalk.blue('🚀 开始发布流程'));

    // 1. 检查工作区状态
    await this.checkWorkingDirectory();

    // 2. 运行测试
    await this.runTests();

    // 3. 选择版本类型
    const versionType = await this.selectVersionType();

    // 4. 更新版本号
    const newVersion = await this.updateVersion(versionType);

    // 5. 生成变更日志
    await this.generateChangelog(newVersion);

    // 6. 构建项目
    await this.build();

    // 7. 创建Git标签
    await this.createGitTag(newVersion);

    // 8. 发布到npm
    await this.publishToNpm();

    // 9. 推送到远程仓库
    await this.pushToRemote(newVersion);

    console.log(chalk.green(`✅ 版本 ${newVersion} 发布成功！`));
  }

  async checkWorkingDirectory() {
    try {
      const status = execSync('git status --porcelain', { encoding: 'utf8' });
      if (status.trim()) {
        throw new Error('工作区有未提交的更改，请先提交或暂存');
      }
    } catch (error) {
      throw new Error(`Git状态检查失败: ${error.message}`);
    }
  }

  async runTests() {
    console.log(chalk.yellow('🧪 运行测试...'));
    try {
      execSync('npm run test:ci', { stdio: 'inherit' });
    } catch (error) {
      throw new Error('测试失败，停止发布');
    }
  }

  async selectVersionType() {
    const { versionType } = await inquirer.prompt([
      {
        type: 'list',
        name: 'versionType',
        message: '选择版本类型:',
        choices: [
          {
            name: `Patch (${this.currentVersion} → ${semver.inc(this.currentVersion, 'patch')}) - 修复bug`,
            value: 'patch'
          },
          {
            name: `Minor (${this.currentVersion} → ${semver.inc(this.currentVersion, 'minor')}) - 新功能`,
            value: 'minor'
          },
          {
            name: `Major (${this.currentVersion} → ${semver.inc(this.currentVersion, 'major')}) - 破坏性更改`,
            value: 'major'
          },
          {
            name: 'Custom - 自定义版本号',
            value: 'custom'
          }
        ]
      }
    ]);

    if (versionType === 'custom') {
      const { customVersion } = await inquirer.prompt([
        {
          type: 'input',
          name: 'customVersion',
          message: '输入自定义版本号:',
          validate: (input) => {
            if (!semver.valid(input)) {
              return '请输入有效的语义化版本号';
            }
            if (!semver.gt(input, this.currentVersion)) {
              return '新版本号必须大于当前版本';
            }
            return true;
          }
        }
      ]);
      return customVersion;
    }

    return versionType;
  }

  async updateVersion(versionType) {
    const newVersion = semver.valid(versionType) ? versionType : semver.inc(this.currentVersion, versionType);

    console.log(chalk.yellow(`📝 更新版本号: ${this.currentVersion} → ${newVersion}`));

    // 更新package.json
    this.packageJson.version = newVersion;
    await fs.writeJson('./package.json', this.packageJson, { spaces: 2 });

    return newVersion;
  }

  async generateChangelog(version) {
    console.log(chalk.yellow('📋 生成变更日志...'));

    try {
      // 使用conventional-changelog生成变更日志
      execSync(`npx conventional-changelog -p angular -i CHANGELOG.md -s -r 0`, { stdio: 'inherit' });
    } catch (error) {
      console.warn(chalk.yellow('⚠️  变更日志生成失败，跳过此步骤'));
    }
  }

  async build() {
    console.log(chalk.yellow('🏗️  构建项目...'));
    try {
      execSync('npm run build', { stdio: 'inherit' });
    } catch (error) {
      throw new Error('构建失败，停止发布');
    }
  }

  async createGitTag(version) {
    console.log(chalk.yellow(`🏷️  创建Git标签: v${version}`));

    try {
      execSync(`git add .`);
      execSync(`git commit -m "chore: release v${version}"`);
      execSync(`git tag v${version}`);
    } catch (error) {
      throw new Error(`Git标签创建失败: ${error.message}`);
    }
  }

  async publishToNpm() {
    const { shouldPublish } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'shouldPublish',
        message: '是否发布到npm?',
        default: true
      }
    ]);

    if (shouldPublish) {
      console.log(chalk.yellow('📦 发布到npm...'));
      try {
        execSync('npm publish', { stdio: 'inherit' });
      } catch (error) {
        throw new Error(`npm发布失败: ${error.message}`);
      }
    }
  }

  async pushToRemote(version) {
    console.log(chalk.yellow('⬆️  推送到远程仓库...'));

    try {
      execSync('git push origin main');
      execSync(`git push origin v${version}`);
    } catch (error) {
      throw new Error(`推送失败: ${error.message}`);
    }
  }
}

// 使用
if (require.main === module) {
  const releaseManager = new ReleaseManager();
  releaseManager.release().catch(error => {
    console.error(chalk.red('❌ 发布失败:'), error.message);
    process.exit(1);
  });
}

module.exports = { ReleaseManager };
```

### 阶段五：团队协作和规范 (1-2周)

#### Week 12: 团队规范建立

**Day 64-66: 代码审查规范**
```markdown
# 代码审查清单

## 📋 审查前准备
- [ ] PR标题清晰描述变更内容
- [ ] PR描述包含变更原因和影响范围
- [ ] 关联相关Issue或需求文档
- [ ] 自测通过，包括功能测试和回归测试

## 🔍 代码质量检查
- [ ] 代码符合团队编码规范
- [ ] 没有明显的性能问题
- [ ] 错误处理完善
- [ ] 日志记录合理
- [ ] 注释清晰有用

## 🧪 测试覆盖
- [ ] 新功能有对应的单元测试
- [ ] 测试用例覆盖主要场景
- [ ] 测试代码质量良好
- [ ] 集成测试通过

## 🔒 安全性检查
- [ ] 没有硬编码的敏感信息
- [ ] 输入验证充分
- [ ] XSS和CSRF防护
- [ ] 依赖安全性检查

## 📚 文档更新
- [ ] API文档已更新
- [ ] README文件已更新
- [ ] 变更日志已记录
- [ ] 组件文档已更新

## 🎯 业务逻辑
- [ ] 实现符合需求规格
- [ ] 边界条件处理正确
- [ ] 用户体验良好
- [ ] 兼容性考虑充分
```

**Day 67-70: 文档规范建立**
```markdown
# 📖 文档编写规范

## 项目文档结构
```
docs/
├── README.md              # 项目概述
├── CONTRIBUTING.md        # 贡献指南
├── CHANGELOG.md          # 变更日志
├── api/                  # API文档
├── components/           # 组件文档
├── guides/              # 使用指南
│   ├── getting-started.md
│   ├── development.md
│   └── deployment.md
└── architecture/        # 架构文档
    ├── overview.md
    ├── frontend.md
    └── backend.md
```

## 组件文档模板
```typescript
/**
 * Button组件 - 用于触发操作的可点击元素
 *
 * @example
 * ```tsx
 * // 基础用法
 * <Button onClick={handleClick}>点击我</Button>
 *
 * // 不同变体
 * <Button variant="primary">主要按钮</Button>
 * <Button variant="secondary">次要按钮</Button>
 *
 * // 不同尺寸
 * <Button size="sm">小按钮</Button>
 * <Button size="lg">大按钮</Button>
 *
 * // 加载状态
 * <Button loading>加载中...</Button>
 * ```
 *
 * @since 1.0.0
 */
export interface ButtonProps {
  /** 按钮变体，控制按钮的视觉样式 */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';

  /** 按钮尺寸 */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';

  /** 是否禁用按钮 */
  disabled?: boolean;

  /** 是否显示加载状态 */
  loading?: boolean;

  /** 点击事件处理函数 */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}
```

## 每个阶段的具体任务清单

### 📅 第1周：环境准备和基础配置
**目标**：搭建完整的开发环境和基础工具链

#### Day 1: 环境搭建
- [ ] 安装Node.js LTS版本
- [ ] 配置包管理工具（推荐pnpm）
- [ ] 安装VS Code和必要插件
- [ ] 配置Git用户信息和SSH密钥

#### Day 2: 项目初始化
- [ ] 创建Monorepo项目结构
- [ ] 初始化根目录package.json
- [ ] 配置workspace管理
- [ ] 创建基础目录结构

#### Day 3-4: 代码规范配置
- [ ] 创建ESLint配置包
- [ ] 创建Prettier配置包
- [ ] 创建TypeScript配置包
- [ ] 测试配置是否正常工作

#### Day 5-7: 构建工具配置
- [ ] 选择构建工具（Vite/Webpack）
- [ ] 创建构建配置包
- [ ] 配置开发服务器
- [ ] 配置生产环境构建

### 📅 第2周：自动化工具链
**目标**：建立自动化的代码检查和测试流程

#### Day 8-10: Git工作流
- [ ] 配置Git Hooks（husky）
- [ ] 设置lint-staged
- [ ] 配置commitlint
- [ ] 建立分支管理规范

#### Day 11-14: 测试框架
- [ ] 选择测试框架（Jest/Vitest）
- [ ] 配置单元测试环境
- [ ] 配置组件测试
- [ ] 配置E2E测试（Cypress/Playwright）

### 📅 第3周：CI/CD流水线
**目标**：建立自动化的集成和部署流程

#### Day 15-17: CI配置
- [ ] 选择CI平台（GitHub Actions/GitLab CI）
- [ ] 配置代码质量检查流水线
- [ ] 配置自动化测试流水线
- [ ] 配置构建流水线

#### Day 18-21: CD配置
- [ ] 配置多环境部署
- [ ] 设置部署脚本
- [ ] 配置环境变量管理
- [ ] 建立回滚机制

### 📅 第4-5周：脚手架开发
**目标**：开发团队专用的脚手架工具

#### Day 22-25: CLI工具开发
- [ ] 设计CLI命令结构
- [ ] 实现项目创建功能
- [ ] 实现代码生成功能
- [ ] 添加交互式配置

#### Day 26-28: 项目模板
- [ ] 创建React项目模板
- [ ] 创建Vue项目模板
- [ ] 创建组件库模板
- [ ] 创建Node.js服务模板

#### Day 29-35: 模板优化
- [ ] 添加条件渲染逻辑
- [ ] 优化模板变量系统
- [ ] 添加插件扩展机制
- [ ] 完善错误处理

### 📅 第6-8周：组件库开发
**目标**：建立完整的UI组件库和设计系统

#### Day 36-42: 设计系统
- [ ] 定义设计令牌（颜色、字体、间距）
- [ ] 建立组件设计原则
- [ ] 创建主题系统
- [ ] 实现响应式设计规范

#### Day 43-49: 基础组件
- [ ] 开发Button组件
- [ ] 开发Input组件
- [ ] 开发Modal组件
- [ ] 开发Form组件

#### Day 50-56: 高级组件
- [ ] 开发Table组件
- [ ] 开发DatePicker组件
- [ ] 开发Upload组件
- [ ] 开发Chart组件

### 📅 第9-10周：文档和测试
**目标**：完善文档系统和测试覆盖

#### Day 57-63: 文档系统
- [ ] 配置Storybook
- [ ] 编写组件文档
- [ ] 创建使用指南
- [ ] 建立API文档

#### Day 64-70: 测试完善
- [ ] 提升测试覆盖率到80%+
- [ ] 添加视觉回归测试
- [ ] 配置性能测试
- [ ] 建立测试报告系统

## 实战演练项目

### 项目1：个人博客系统 (入门级)
**技术栈**：React + TypeScript + Vite + TailwindCSS

**实施步骤**：
1. **项目初始化** (1天)
   ```bash
   # 使用自己的脚手架创建项目
   npx your-cli create my-blog --template=react-ts
   cd my-blog

   # 安装额外依赖
   npm install react-router-dom @tanstack/react-query
   ```

2. **基础配置** (1天)
   - 配置路由系统
   - 配置状态管理
   - 配置API请求
   - 配置样式系统

3. **功能开发** (3-5天)
   - 文章列表页面
   - 文章详情页面
   - 文章编辑功能
   - 用户认证系统

4. **测试和部署** (1-2天)
   - 编写组件测试
   - 配置E2E测试
   - 部署到Vercel/Netlify

### 项目2：管理后台系统 (中级)
**技术栈**：React + TypeScript + Ant Design + Umi

**实施步骤**：
1. **架构设计** (2天)
   - 设计页面结构
   - 规划路由系统
   - 设计状态管理
   - 规划API接口

2. **基础框架** (3天)
   - 搭建项目骨架
   - 配置权限系统
   - 实现布局组件
   - 配置主题系统

3. **业务功能** (5-7天)
   - 用户管理模块
   - 权限管理模块
   - 数据统计模块
   - 系统设置模块

4. **优化和部署** (2-3天)
   - 性能优化
   - 安全加固
   - 部署配置
   - 监控接入

### 项目3：微前端架构 (高级)
**技术栈**：Module Federation + React + Vue + Angular

**实施步骤**：
1. **架构设计** (3天)
   - 设计微前端架构
   - 规划应用拆分
   - 设计通信机制
   - 规划共享资源

2. **基座应用** (3天)
   - 开发主应用框架
   - 实现路由管理
   - 实现应用注册
   - 实现状态共享

3. **子应用开发** (5-7天)
   - React子应用
   - Vue子应用
   - Angular子应用
   - 应用间通信

4. **集成和优化** (3-4天)
   - 应用集成测试
   - 性能优化
   - 部署配置
   - 监控和日志

## 工程化学习资源

### 📚 必读书籍
1. **《前端工程化体系设计与实践》** - 周俊鹏
   - 系统性介绍前端工程化理念
   - 实际案例和最佳实践
   - 适合初学者建立整体认知

2. **《现代前端技术解析》** - 张成文
   - 深入解析现代前端技术栈
   - 工程化工具原理分析
   - 适合进阶学习

3. **《JavaScript工程师修炼之道》** - Nicholas C. Zakas
   - 代码质量和团队协作
   - 大型项目管理经验
   - 适合团队负责人

### 🎥 视频教程
1. **B站推荐**
   - 「前端工程化实战」系列
   - 「Webpack深入浅出」
   - 「Vite原理解析」

2. **YouTube推荐**
   - "Frontend Build Tools Explained"
   - "Modern JavaScript Tooling"
   - "React Testing Best Practices"

### 🌐 在线资源
1. **官方文档**
   - [Webpack官方文档](https://webpack.js.org/)
   - [Vite官方文档](https://vitejs.dev/)
   - [ESLint官方文档](https://eslint.org/)

2. **社区资源**
   - [前端工程化指南](https://github.com/woai3c/Front-end-engineering)
   - [现代前端工具链](https://github.com/modernweb-dev/web)
   - [最佳实践集合](https://github.com/thedaviddias/Front-End-Checklist)

### 🛠️ 实践项目
1. **开源项目贡献**
   - 参与知名开源项目
   - 学习优秀的工程化实践
   - 提升代码质量意识

2. **个人项目实践**
   - 从零搭建完整项目
   - 尝试不同技术栈
   - 总结经验和教训

## 学习进度跟踪

### 第1个月目标
- [ ] 掌握基础工具链使用
- [ ] 能够配置完整的开发环境
- [ ] 理解工程化的核心概念
- [ ] 完成一个简单的实战项目

### 第2个月目标
- [ ] 掌握自动化流程配置
- [ ] 能够搭建CI/CD流水线
- [ ] 理解测试体系建设
- [ ] 完成中等复杂度项目

### 第3个月目标
- [ ] 掌握组件库开发
- [ ] 能够设计工程化架构
- [ ] 理解性能优化策略
- [ ] 完成高级项目实践

### 持续学习计划
- **每周**：关注前端技术动态，阅读技术博客
- **每月**：深入学习一个新工具或技术
- **每季度**：总结经验，分享技术文章
- **每年**：参与技术会议，更新知识体系

## 常见问题解答

### Q1: 工程化是否会增加项目复杂度？
**A**: 短期内会增加一些复杂度，但长期来看会显著降低维护成本和提升开发效率。建议渐进式引入，从最基础的工具开始。

### Q2: 小团队是否需要完整的工程化？
**A**: 可以根据团队规模选择性实施。小团队建议优先关注代码规范、自动化测试和简单的CI/CD。

### Q3: 如何说服团队采用工程化？
**A**:
- 展示具体的收益数据
- 从小范围试点开始
- 提供充分的培训和支持
- 建立激励机制

### Q4: 工程化工具更新频繁，如何跟上？
**A**:
- 关注官方发布渠道
- 建立技术调研机制
- 制定升级策略
- 保持学习心态

### Q5: 如何平衡工程化投入和业务开发？
**A**:
- 制定合理的时间分配
- 优先解决最痛的问题
- 寻找自动化机会
- 建立长期规划

## 成功案例分析

### 案例1：某电商公司前端工程化改造
**背景**：50+前端开发，100+项目，技术栈混乱

**改造过程**：
1. **现状调研** (2周)
   - 项目技术栈统计
   - 开发痛点收集
   - 团队技能评估

2. **试点项目** (4周)
   - 选择3个项目试点
   - 应用新的工程化方案
   - 收集反馈和数据

3. **全面推广** (12周)
   - 制定迁移计划
   - 团队培训
   - 逐步迁移所有项目

**效果**：
- 开发效率提升40%
- 代码质量问题减少60%
- 部署时间缩短80%
- 团队满意度显著提升

### 案例2：某金融公司组件库建设
**背景**：多个业务线，UI不统一，重复开发严重

**建设过程**：
1. **设计系统建立** (6周)
   - UI设计规范制定
   - 组件API设计
   - 开发规范制定

2. **组件库开发** (12周)
   - 基础组件开发
   - 业务组件开发
   - 文档和示例

3. **推广应用** (8周)
   - 业务线试点
   - 培训和支持
   - 全面推广

**效果**：
- UI一致性提升90%
- 组件重复开发减少70%
- 新项目开发速度提升50%

## 工具对比和选择指南

### 构建工具对比
| 工具 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| Vite | 开发速度快，配置简单 | 生态相对较新 | 现代项目，快速开发 |
| Webpack | 生态成熟，功能强大 | 配置复杂，构建较慢 | 大型项目，复杂需求 |
| Rollup | 打包体积小，适合库 | 功能相对简单 | 组件库，工具库 |
| Parcel | 零配置，开箱即用 | 定制性较差 | 快速原型，小型项目 |

### 包管理工具对比
| 工具 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| pnpm | 速度快，节省空间 | 相对较新 | 现代项目，Monorepo |
| yarn | 功能丰富，稳定 | 体积较大 | 大型项目，企业环境 |
| npm | 官方工具，兼容性好 | 速度较慢 | 传统项目，简单需求 |

### 测试框架对比
| 工具 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| Jest | 功能完善，生态丰富 | 配置复杂 | 大型项目，全面测试 |
| Vitest | 速度快，与Vite集成 | 生态较新 | 现代项目，快速测试 |
| Cypress | E2E测试强大 | 学习成本高 | 复杂交互，端到端测试 |
| Playwright | 跨浏览器支持好 | 资源消耗大 | 多浏览器测试 |

## 进阶学习路径

### 高级主题
1. **微前端架构**
   - Module Federation原理
   - Single-SPA框架使用
   - 微前端最佳实践

2. **性能优化**
   - 构建性能优化
   - 运行时性能监控
   - Core Web Vitals优化

3. **DevOps集成**
   - 容器化部署
   - 监控和日志系统
   - 安全性考虑

4. **工具开发**
   - Webpack插件开发
   - Vite插件开发
   - CLI工具开发

### 学习方法建议
1. **理论与实践结合**
   - 先理解原理，再动手实践
   - 多做项目，积累经验
   - 总结和分享经验

2. **关注技术趋势**
   - 订阅技术博客和Newsletter
   - 参与技术社区讨论
   - 关注开源项目动态

3. **建立知识体系**
   - 制作思维导图
   - 建立个人知识库
   - 定期回顾和更新

## 总结

前端工程化是一个系统性工程，需要：

1. **循序渐进**：从基础工具开始，逐步完善
2. **实践导向**：通过实际项目验证和改进
3. **团队协作**：建立共识，共同推进
4. **持续改进**：根据反馈不断优化

成功的前端工程化能够：
- 提升开发效率50%+
- 减少代码质量问题60%+
- 缩短部署时间80%+
- 提升团队协作效率

记住：**工程化的目标是服务于业务发展，而不是为了技术而技术**。始终以解决实际问题为导向，选择合适的工具和方案。
```
```
```

#### 步骤1：项目结构规划
```
frontend-infrastructure/
├── packages/
│   ├── cli/                 # 脚手架CLI工具
│   ├── build-config/        # 构建配置包
│   ├── eslint-config/       # ESLint配置
│   ├── prettier-config/     # Prettier配置
│   └── tsconfig/           # TypeScript配置
├── templates/
│   ├── react-app/          # React项目模板
│   ├── vue-app/            # Vue项目模板
│   └── component-lib/      # 组件库模板
├── docs/                   # 文档
└── tools/                  # 工具脚本
```

#### 步骤2：创建脚手架CLI
- 使用Commander.js或类似工具创建CLI
- 实现项目创建、组件生成等功能
- 支持交互式选择和配置

#### 步骤3：配置代码规范
- 创建ESLint配置包
- 创建Prettier配置包
- 创建TypeScript配置
- 集成Git Hooks (husky + lint-staged)

### 阶段二：构建工具链 (2-3周)

#### 步骤4：构建配置标准化
- 创建Webpack/Vite配置包
- 支持多环境配置
- 集成常用插件和优化

#### 步骤5：开发服务器配置
- 热重载配置
- 代理配置管理
- Mock服务集成

#### 步骤6：测试框架搭建
- 单元测试配置 (Jest/Vitest)
- 组件测试配置 (Testing Library)
- E2E测试配置 (Cypress/Playwright)

### 阶段三：CI/CD流水线 (1-2周)

#### 步骤7：版本控制规范
- Git工作流规范 (Git Flow/GitHub Flow)
- 提交信息规范 (Conventional Commits)
- 分支命名规范

#### 步骤8：自动化流水线
- GitHub Actions/GitLab CI配置
- 自动化测试
- 自动化构建和部署
- 代码质量检查

### 阶段四：组件库与文档 (3-4周)

#### 步骤9：组件库开发
- 基础组件开发
- 组件API设计
- 主题系统设计
- 组件文档编写

#### 步骤10：文档系统
- Storybook配置
- API文档生成
- 使用指南编写
- 最佳实践文档

### 阶段五：监控与优化 (1-2周)

#### 步骤11：性能监控
- 构建性能分析
- 运行时性能监控
- 错误追踪系统

#### 步骤12：持续优化
- 工具链性能优化
- 开发体验改进
- 团队反馈收集

## 技术选型建议

### 构建工具
- **Vite**：现代化、快速的构建工具
- **Webpack**：成熟稳定，生态丰富
- **Rollup**：适合库的构建

### 包管理
- **pnpm**：性能优秀，节省磁盘空间
- **yarn**：稳定可靠，功能丰富
- **npm**：官方工具，兼容性好

### 代码质量
- **ESLint + Prettier**：代码规范和格式化
- **TypeScript**：类型安全
- **Husky + lint-staged**：Git Hooks

### 测试框架
- **Vitest**：与Vite集成良好
- **Jest**：功能完善，生态丰富
- **Cypress**：E2E测试首选

### CI/CD
- **GitHub Actions**：与GitHub深度集成
- **GitLab CI**：功能强大，自托管友好
- **Jenkins**：企业级选择

## 最佳实践

### 1. 渐进式实施
- 从小范围试点开始
- 逐步推广到整个团队
- 收集反馈持续改进

### 2. 文档先行
- 详细的使用文档
- 清晰的迁移指南
- 常见问题解答

### 3. 团队培训
- 工具使用培训
- 最佳实践分享
- 定期技术交流

### 4. 持续维护
- 定期更新依赖
- 性能监控和优化
- 新技术调研和引入

## 快速开始检查清单

### 第一周目标
- [ ] 确定技术栈和工具选型
- [ ] 创建基础项目结构
- [ ] 搭建基础脚手架CLI
- [ ] 配置基础代码规范

### 第一个月目标
- [ ] 完成构建工具链配置
- [ ] 搭建CI/CD流水线
- [ ] 创建项目模板
- [ ] 编写基础文档

### 三个月目标
- [ ] 完成组件库开发
- [ ] 建立完整的测试体系
- [ ] 部署监控系统
- [ ] 团队全面使用

## 常见挑战与解决方案

### 挑战1：团队接受度
**解决方案**：
- 展示明确的收益
- 提供充分的培训
- 渐进式推进

### 挑战2：维护成本
**解决方案**：
- 自动化更新机制
- 清晰的文档和规范
- 专人负责维护

### 挑战3：技术债务
**解决方案**：
- 制定迁移计划
- 新老项目并行
- 逐步替换旧系统

## 总结

前端基建是一个系统性工程，需要从团队实际需求出发，循序渐进地建设。关键是要：

1. **明确目标**：提升效率、保证质量、降低成本
2. **合理规划**：分阶段实施，避免一次性投入过大
3. **持续改进**：根据团队反馈不断优化
4. **文档完善**：确保团队能够快速上手和维护

成功的前端基建能够显著提升团队的开发效率和代码质量，是现代前端团队不可或缺的基础设施。

## 详细技术实现

### 脚手架CLI实现示例

#### 1. CLI工具结构
```javascript
// packages/cli/src/index.js
#!/usr/bin/env node
const { Command } = require('commander');
const inquirer = require('inquirer');
const chalk = require('chalk');

const program = new Command();

program
  .name('fe-cli')
  .description('前端基建CLI工具')
  .version('1.0.0');

program
  .command('create <project-name>')
  .description('创建新项目')
  .option('-t, --template <template>', '项目模板', 'react')
  .action(async (projectName, options) => {
    await createProject(projectName, options);
  });

program
  .command('generate <type> <name>')
  .alias('g')
  .description('生成代码')
  .action(async (type, name) => {
    await generateCode(type, name);
  });
```

#### 2. 项目模板配置
```json
// templates/react-app/template.json
{
  "name": "React应用模板",
  "description": "基于React + TypeScript的现代化应用模板",
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "vite": "^4.0.0"
  },
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "test": "vitest",
    "lint": "eslint src --ext .ts,.tsx"
  }
}
```

### 构建配置实现

#### 1. Vite配置封装
```javascript
// packages/build-config/vite.config.js
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export function createViteConfig(options = {}) {
  return defineConfig({
    plugins: [
      react(),
      ...options.plugins || []
    ],
    resolve: {
      alias: {
        '@': resolve(process.cwd(), 'src'),
        ...options.alias
      }
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            ...options.chunks
          }
        }
      }
    },
    server: {
      port: 3000,
      proxy: options.proxy || {}
    }
  });
}
```

#### 2. Webpack配置封装
```javascript
// packages/build-config/webpack.config.js
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = function createWebpackConfig(options = {}) {
  const isDev = process.env.NODE_ENV === 'development';

  return {
    mode: isDev ? 'development' : 'production',
    entry: options.entry || './src/index.tsx',
    output: {
      path: path.resolve(process.cwd(), 'dist'),
      filename: isDev ? '[name].js' : '[name].[contenthash].js',
      clean: true
    },
    resolve: {
      extensions: ['.tsx', '.ts', '.jsx', '.js'],
      alias: {
        '@': path.resolve(process.cwd(), 'src'),
        ...options.alias
      }
    },
    module: {
      rules: [
        {
          test: /\.(ts|tsx)$/,
          use: 'ts-loader',
          exclude: /node_modules/
        },
        {
          test: /\.css$/,
          use: [
            isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
            'css-loader',
            'postcss-loader'
          ]
        }
      ]
    },
    plugins: [
      new HtmlWebpackPlugin({
        template: 'public/index.html'
      }),
      !isDev && new MiniCssExtractPlugin({
        filename: '[name].[contenthash].css'
      })
    ].filter(Boolean)
  };
};
```

### 代码规范配置

#### 1. ESLint配置
```javascript
// packages/eslint-config/index.js
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint', 'react', 'react-hooks'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn'
  },
  settings: {
    react: {
      version: 'detect'
    }
  }
};
```

#### 2. Prettier配置
```javascript
// packages/prettier-config/index.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  bracketSpacing: true,
  arrowParens: 'avoid'
};
```

### 测试框架配置

#### 1. Jest配置
```javascript
// packages/test-config/jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### CI/CD配置示例

#### 1. GitHub Actions工作流
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run linting
        run: pnpm lint

      - name: Run tests
        run: pnpm test:coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build application
        run: pnpm build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: dist/

      - name: Deploy to production
        run: |
          # 部署脚本
          echo "Deploying to production..."
```

## 组件库架构设计

### 1. 组件库结构
```
packages/ui-components/
├── src/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── index.tsx
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   ├── Button.stories.tsx
│   │   │   └── styles.module.css
│   │   └── index.ts
│   ├── hooks/
│   ├── utils/
│   ├── types/
│   └── index.ts
├── docs/
├── storybook/
└── package.json
```

### 2. 组件开发规范
```typescript
// 组件接口定义
export interface ButtonProps {
  /** 按钮类型 */
  type?: 'primary' | 'secondary' | 'danger';
  /** 按钮尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否加载中 */
  loading?: boolean;
  /** 点击事件 */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** 子元素 */
  children: React.ReactNode;
}

// 组件实现
export const Button: React.FC<ButtonProps> = ({
  type = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  children,
  ...props
}) => {
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;
    onClick?.(event);
  };

  return (
    <button
      className={`btn btn--${type} btn--${size}`}
      disabled={disabled || loading}
      onClick={handleClick}
      {...props}
    >
      {loading && <Spinner />}
      {children}
    </button>
  );
};
```

## 性能优化策略

### 1. 构建性能优化
- **并行构建**：使用多核CPU进行并行处理
- **缓存策略**：合理使用构建缓存
- **依赖分析**：优化依赖图，减少不必要的依赖

### 2. 运行时性能优化
- **代码分割**：按路由和功能进行代码分割
- **懒加载**：组件和资源的懒加载
- **Tree Shaking**：移除未使用的代码

### 3. 开发体验优化
- **热重载**：快速的热重载机制
- **错误提示**：友好的错误信息和调试工具
- **类型提示**：完善的TypeScript类型定义

## 监控与分析

### 1. 构建监控
```javascript
// 构建性能分析
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
  plugins: [
    new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      openAnalyzer: false,
      reportFilename: 'bundle-report.html'
    })
  ]
};
```

### 2. 运行时监控
```javascript
// 性能监控
export function initPerformanceMonitoring() {
  // 页面加载性能
  window.addEventListener('load', () => {
    const perfData = performance.getEntriesByType('navigation')[0];
    console.log('页面加载时间:', perfData.loadEventEnd - perfData.fetchStart);
  });

  // 资源加载性能
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      console.log('资源加载:', entry.name, entry.duration);
    });
  });
  observer.observe({ entryTypes: ['resource'] });
}
```

## 团队协作规范

### 1. Git工作流
```bash
# 功能开发流程
git checkout develop
git pull origin develop
git checkout -b feature/new-feature
# 开发完成后
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature
# 创建Pull Request
```

### 2. 代码审查清单
- [ ] 代码符合团队规范
- [ ] 包含必要的测试
- [ ] 文档已更新
- [ ] 性能影响评估
- [ ] 安全性检查

### 3. 发布流程
```bash
# 版本发布流程
npm version patch  # 或 minor, major
git push origin main --tags
npm publish
```

## 扩展与定制

### 1. 插件系统设计
```javascript
// 插件接口定义
export interface Plugin {
  name: string;
  version: string;
  apply(compiler: Compiler): void;
}

// 插件注册
export class PluginManager {
  private plugins: Plugin[] = [];

  register(plugin: Plugin) {
    this.plugins.push(plugin);
  }

  apply(compiler: Compiler) {
    this.plugins.forEach(plugin => plugin.apply(compiler));
  }
}
```

### 2. 配置扩展机制
```javascript
// 配置合并策略
export function mergeConfig(baseConfig, userConfig) {
  return {
    ...baseConfig,
    ...userConfig,
    plugins: [
      ...(baseConfig.plugins || []),
      ...(userConfig.plugins || [])
    ],
    rules: {
      ...baseConfig.rules,
      ...userConfig.rules
    }
  };
}
```

## 迁移指南

### 1. 从传统项目迁移
1. **评估现状**：分析现有项目结构和依赖
2. **制定计划**：分阶段迁移计划
3. **创建新分支**：在新分支上进行迁移
4. **逐步替换**：逐个模块进行替换
5. **测试验证**：确保功能正常
6. **团队培训**：新工具使用培训

### 2. 版本升级策略
- **语义化版本**：遵循semver规范
- **变更日志**：详细记录每次变更
- **兼容性保证**：向后兼容策略
- **迁移工具**：提供自动化迁移脚本

## 故障排查

### 1. 常见问题
- **构建失败**：检查依赖版本、配置文件
- **热重载不工作**：检查文件监听配置
- **类型错误**：检查TypeScript配置
- **样式问题**：检查CSS处理配置

### 2. 调试工具
```javascript
// 调试配置
export const debugConfig = {
  webpack: {
    stats: 'verbose',
    devtool: 'source-map'
  },
  vite: {
    logLevel: 'info',
    clearScreen: false
  }
};
```

## 未来发展方向

### 1. 技术趋势
- **微前端**：支持微前端架构
- **Serverless**：无服务器部署
- **Edge Computing**：边缘计算优化
- **WebAssembly**：高性能计算场景

### 2. 工具演进
- **构建工具**：更快的构建工具（如SWC、esbuild）
- **开发工具**：更好的开发体验
- **测试工具**：更智能的测试工具
- **部署工具**：更简单的部署流程

## 资源链接

### 官方文档
- [Vite官方文档](https://vitejs.dev/)
- [Webpack官方文档](https://webpack.js.org/)
- [ESLint官方文档](https://eslint.org/)
- [Jest官方文档](https://jestjs.io/)

### 社区资源
- [前端工程化最佳实践](https://github.com/topics/frontend-engineering)
- [现代前端工具链](https://github.com/topics/frontend-tooling)
- [组件库设计模式](https://github.com/topics/design-system)

### 学习资料
- 《前端工程化实战》
- 《现代前端技术解析》
- 《JavaScript工程师修炼之道》

---

*本文档将持续更新，欢迎提出建议和改进意见。*
