# InputNumber 自定义数字输入组件

基于ElementUI InputNumber组件的增强版本，支持精度控制和长度限制，适用于各种数字输入场景。

## 功能特性

- 🔢 支持精度控制，自动计算步长
- 📏 支持数字长度限制
- ⚖️ 自动计算最小值和最大值范围
- 🎯 100%宽度适配容器
- 🔧 完全兼容ElementUI InputNumber的所有功能

## 基础用法

```vue
<template>
  <div>
    <!-- 基础用法 -->
    <input-number 
      v-model="amount" 
      :maxlength="8" 
      :precision="2"
      placeholder="请输入金额"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      amount: null
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| value / v-model | 绑定值 | Number | — | undefined | 否 |
| placeholder | 输入框占位文本 | String | — | null | 否 |
| maxlength | 数字最大长度（包含小数点和负号） | Number | — | 1 | 是 |
| precision | 数值精度（小数位数） | Number | — | 0 | 否 |
| min | 设置最小值（如不设置将自动计算） | Number | — | null | 否 |
| max | 设置最大值（如不设置将自动计算） | Number | — | null | 否 |
| disabled | 是否禁用 | Boolean | true/false | false | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 绑定值被改变时触发 | (currentValue: number, oldValue: number) |

### 自动计算规则

组件会根据 `maxlength` 和 `precision` 自动计算：

1. **步长(step)**：`Math.pow(10, -precision)`
2. **最大值(max)**：`Math.pow(10, (maxlength - precision)) - step`
3. **最小值(min)**：`-(Math.pow(10, (maxlength - precision)) - step)`

## 使用示例

### 金额输入

```vue
<template>
  <div>
    <el-form :model="orderForm" label-width="120px">
      <el-form-item label="订单金额">
        <input-number 
          v-model="orderForm.amount"
          :maxlength="10"
          :precision="2"
          placeholder="请输入订单金额"
        />
        <span class="unit">元</span>
      </el-form-item>
      
      <el-form-item label="折扣率">
        <input-number 
          v-model="orderForm.discount"
          :maxlength="4"
          :precision="2"
          :min="0"
          :max="1"
          placeholder="0.00-1.00"
        />
        <span class="unit">%</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      orderForm: {
        amount: null,
        discount: null
      }
    }
  }
}
</script>

<style scoped>
.unit {
  margin-left: 10px;
  color: #606266;
}
</style>
```

### 测量数据输入

```vue
<template>
  <div>
    <el-card header="测量数据录入">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="温度(℃)">
            <input-number 
              v-model="measurements.temperature"
              :maxlength="5"
              :precision="1"
              :min="-50"
              :max="100"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="湿度(%)">
            <input-number 
              v-model="measurements.humidity"
              :maxlength="5"
              :precision="1"
              :min="0"
              :max="100"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="压力(Pa)">
            <input-number 
              v-model="measurements.pressure"
              :maxlength="8"
              :precision="0"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      measurements: {
        temperature: null,
        humidity: null,
        pressure: null
      }
    }
  }
}
</script>
```

### 配置参数输入

```vue
<template>
  <div>
    <el-form :model="config" label-width="150px">
      <el-form-item label="超时时间(秒)">
        <input-number 
          v-model="config.timeout"
          :maxlength="4"
          :precision="0"
          :min="1"
          :max="3600"
          placeholder="1-3600"
        />
      </el-form-item>
      
      <el-form-item label="重试次数">
        <input-number 
          v-model="config.retryCount"
          :maxlength="2"
          :precision="0"
          :min="0"
          :max="10"
        />
      </el-form-item>
      
      <el-form-item label="成功率阈值">
        <input-number 
          v-model="config.successRate"
          :maxlength="5"
          :precision="3"
          :min="0"
          :max="1"
          placeholder="0.000-1.000"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      config: {
        timeout: 30,
        retryCount: 3,
        successRate: 0.95
      }
    }
  },
  watch: {
    config: {
      handler(newConfig) {
        console.log('配置更新:', newConfig)
        this.saveConfig(newConfig)
      },
      deep: true
    }
  },
  methods: {
    saveConfig(config) {
      // 保存配置到后端
      localStorage.setItem('app-config', JSON.stringify(config))
    }
  }
}
</script>
```

## 注意事项

1. **长度计算**：`maxlength` 包含小数点和负号，需要合理设置
2. **精度设置**：`precision` 会影响步长和有效范围
3. **边界值**：组件会自动处理边界值，确保输入有效
4. **性能考虑**：大量数字输入时建议防抖处理

## 常见问题

### Q: 如何设置千分位分隔符？

A: 组件基于ElementUI InputNumber，可以通过其原生属性实现。

### Q: 支持负数输入吗？

A: 支持，组件会自动计算负数范围，也可以通过 `min` 属性限制。

### Q: 如何处理精度丢失？

A: 组件内部使用ElementUI的精度处理机制，避免了JavaScript浮点数精度问题。

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<!--
@File    index.vue
@Desc    自定义数字输入组件.
<AUTHOR> href="mailto:<EMAIL>">xiaoQQya</a>
@Date    2023/10/10
-->
<template>
  <el-input-number
    v-bind:value="value"
    @input="input"
    :placeholder="placeholder"
    :precision="precision"
    :step="step"
    :min="nativeMin"
    :max="nativeMax"
    :disabled="disabled"
    style="width: 100%;"
  />
</template>

<script>
export default {
  name: "InputNumber",
  props: {
    value: {
      default: undefined
    },
    placeholder: {
      type: String,
      default: null
    },
    maxlength: {
      type: Number,
      default: 1,
      required: true
    },
    precision: {
      type: Number,
      default: 0
    },
    min: {
      type: Number,
      default: null
    },
    max: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      step: undefined,
      nativeMin: undefined,
      nativeMax: undefined
    }
  },
  created() {
    this.computePrecision();
  },
  methods: {
    input(value) {
      this.$emit("input", value);
    },
    computePrecision() {
      this.step = Math.pow(10, -this.precision);
      this.nativeMax = this.max !== null ? this.max : Math.pow(10, (this.maxlength - this.precision)) - this.step;
      this.nativeMin = this.min !== null ? this.min : -(Math.pow(10, (this.maxlength - this.precision)) - this.step);
    }
  }
}
</script>
```

</details>
