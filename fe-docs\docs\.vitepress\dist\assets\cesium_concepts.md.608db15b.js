import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const A=JSON.parse('{"title":"核心概念","description":"","frontmatter":{},"headers":[],"relativePath":"cesium/concepts.md","filePath":"cesium/concepts.md"}'),p={name:"cesium/concepts.md"},o=l(`<h1 id="核心概念" tabindex="-1">核心概念 <a class="header-anchor" href="#核心概念" aria-label="Permalink to &quot;核心概念&quot;">​</a></h1><p>理解Cesium的核心概念是掌握3D地图开发的基础。本章将介绍Cesium的主要组件、坐标系统、数据类型等核心知识。</p><h2 id="主要组件" tabindex="-1">主要组件 <a class="header-anchor" href="#主要组件" aria-label="Permalink to &quot;主要组件&quot;">​</a></h2><h3 id="viewer-查看器" tabindex="-1">Viewer（查看器） <a class="header-anchor" href="#viewer-查看器" aria-label="Permalink to &quot;Viewer（查看器）&quot;">​</a></h3><p>Viewer是Cesium应用的主入口，包含了完整的3D地球场景和用户界面控件。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><p><strong>主要属性：</strong></p><ul><li><code>scene</code> - 3D场景</li><li><code>camera</code> - 相机控制</li><li><code>canvas</code> - HTML5 Canvas元素</li><li><code>clock</code> - 时钟控制</li><li><code>entities</code> - 实体集合</li><li><code>dataSources</code> - 数据源集合</li></ul><h3 id="scene-场景" tabindex="-1">Scene（场景） <a class="header-anchor" href="#scene-场景" aria-label="Permalink to &quot;Scene（场景）&quot;">​</a></h3><p>Scene管理3D场景的渲染，包含地球、天空盒、光照等。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> scene </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 场景相关配置</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">              </span><span style="color:#676E95;font-style:italic;">// 显示地球</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">skyBox</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 显示天空盒</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">sun</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">                </span><span style="color:#676E95;font-style:italic;">// 显示太阳</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">moon</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">               </span><span style="color:#676E95;font-style:italic;">// 显示月亮</span></span>
<span class="line"><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">fog</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enabled </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 启用雾效</span></span></code></pre></div><h3 id="camera-相机" tabindex="-1">Camera（相机） <a class="header-anchor" href="#camera-相机" aria-label="Permalink to &quot;Camera（相机）&quot;">​</a></h3><p>Camera控制用户的视角和导航。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> camera </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机位置和方向</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 相机位置（笛卡尔坐标）</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">direction</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">       </span><span style="color:#676E95;font-style:italic;">// 观看方向</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">up</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 向上方向</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">right</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">          </span><span style="color:#676E95;font-style:italic;">// 向右方向</span></span></code></pre></div><h3 id="globe-地球" tabindex="-1">Globe（地球） <a class="header-anchor" href="#globe-地球" aria-label="Permalink to &quot;Globe（地球）&quot;">​</a></h3><p>Globe表示地球几何体，包含地形和影像数据。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> globe </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 地球配置</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">                    </span><span style="color:#676E95;font-style:italic;">// 显示地球</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enableLighting </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">         </span><span style="color:#676E95;font-style:italic;">// 启用光照</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">showWaterEffect </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">         </span><span style="color:#676E95;font-style:italic;">// 显示水体效果</span></span>
<span class="line"><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">shadows </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ShadowMode</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RECEIVE_ONLY</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 阴影模式</span></span></code></pre></div><h2 id="坐标系统" tabindex="-1">坐标系统 <a class="header-anchor" href="#坐标系统" aria-label="Permalink to &quot;坐标系统&quot;">​</a></h2><h3 id="wgs84坐标系" tabindex="-1">WGS84坐标系 <a class="header-anchor" href="#wgs84坐标系" aria-label="Permalink to &quot;WGS84坐标系&quot;">​</a></h3><p>Cesium使用WGS84大地坐标系作为标准参考坐标系。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 经纬度坐标（度）</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> longitude </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 经度</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> latitude </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 纬度</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> height </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 高度（米）</span></span></code></pre></div><h3 id="笛卡尔坐标系" tabindex="-1">笛卡尔坐标系 <a class="header-anchor" href="#笛卡尔坐标系" aria-label="Permalink to &quot;笛卡尔坐标系&quot;">​</a></h3><p>3D空间中的直角坐标系，原点位于地球中心。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 从经纬度转换为笛卡尔坐标</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> cartesian </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(longitude</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> latitude</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> height)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 从笛卡尔坐标转换为经纬度</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> cartographic </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromCartesian</span><span style="color:#BABED8;">(cartesian)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> lon </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDegrees</span><span style="color:#BABED8;">(cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">longitude)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> lat </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDegrees</span><span style="color:#BABED8;">(cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">latitude)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> alt </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">height</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="屏幕坐标系" tabindex="-1">屏幕坐标系 <a class="header-anchor" href="#屏幕坐标系" aria-label="Permalink to &quot;屏幕坐标系&quot;">​</a></h3><p>屏幕上的2D坐标系，原点在左上角。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 世界坐标转屏幕坐标</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> screenPosition </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">SceneTransforms</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">wgs84ToWindowCoordinates</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span></span>
<span class="line"><span style="color:#BABED8;">  cartesian</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 屏幕坐标转世界坐标</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> worldPosition </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pickEllipsoid</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  screenPosition</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span></span>
<span class="line"><span style="color:#BABED8;">  viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ellipsoid</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="实体系统-entity-api" tabindex="-1">实体系统（Entity API） <a class="header-anchor" href="#实体系统-entity-api" aria-label="Permalink to &quot;实体系统（Entity API）&quot;">​</a></h2><h3 id="entity-实体" tabindex="-1">Entity（实体） <a class="header-anchor" href="#entity-实体" aria-label="Permalink to &quot;Entity（实体）&quot;">​</a></h3><p>Entity是Cesium中表示对象的高级API，提供了声明式的图形描述。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建点实体</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> pointEntity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">id</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">myPoint</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">示例点</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="图形类型" tabindex="-1">图形类型 <a class="header-anchor" href="#图形类型" aria-label="Permalink to &quot;图形类型&quot;">​</a></h3><p>Cesium支持多种图形类型：</p><h4 id="点-point" tabindex="-1">点（Point） <a class="header-anchor" href="#点-point" aria-label="Permalink to &quot;点（Point）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">heightReference</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">HeightReference</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CLAMP_TO_GROUND</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="标签-label" tabindex="-1">标签（Label） <a class="header-anchor" href="#标签-label" aria-label="Permalink to &quot;标签（Label）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">label</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">text</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">标签文本</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">14pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="广告牌-billboard" tabindex="-1">广告牌（Billboard） <a class="header-anchor" href="#广告牌-billboard" aria-label="Permalink to &quot;广告牌（Billboard）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">billboard</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">image</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path/to/image.png</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">scale</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">50</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">eyeOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian3</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.0</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">horizontalOrigin</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">HorizontalOrigin</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CENTER</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">verticalOrigin</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">VerticalOrigin</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BOTTOM</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="线-polyline" tabindex="-1">线（Polyline） <a class="header-anchor" href="#线-polyline" aria-label="Permalink to &quot;线（Polyline）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">polyline</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">positions</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegreesArray</span><span style="color:#F07178;">([</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">118.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">41.0</span></span>
<span class="line"><span style="color:#F07178;">  ])</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">width</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">clampToGround</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="多边形-polygon" tabindex="-1">多边形（Polygon） <a class="header-anchor" href="#多边形-polygon" aria-label="Permalink to &quot;多边形（Polygon）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">polygon</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">hierarchy</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegreesArray</span><span style="color:#F07178;">([</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40.0</span></span>
<span class="line"><span style="color:#F07178;">  ])</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLUE</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">withAlpha</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0.5</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outline</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h4 id="_3d模型-model" tabindex="-1">3D模型（Model） <a class="header-anchor" href="#_3d模型-model" aria-label="Permalink to &quot;3D模型（Model）&quot;">​</a></h4><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">model</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">uri</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path/to/model.glb</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">scale</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">minimumPixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">128</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#FFCB6B;">maximumScale</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">20000</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="材质系统" tabindex="-1">材质系统 <a class="header-anchor" href="#材质系统" aria-label="Permalink to &quot;材质系统&quot;">​</a></h2><h3 id="基础材质" tabindex="-1">基础材质 <a class="header-anchor" href="#基础材质" aria-label="Permalink to &quot;基础材质&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 纯色材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 图片材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">ImageMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">image</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path/to/texture.jpg</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">repeat</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">4.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">4.0</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 条纹材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">StripeMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">evenColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">oddColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">repeat</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5.0</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span></code></pre></div><h3 id="动画材质" tabindex="-1">动画材质 <a class="header-anchor" href="#动画材质" aria-label="Permalink to &quot;动画材质&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 发光线材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">PolylineGlowMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">glowPower</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLUE</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 轮廓线材质</span></span>
<span class="line"><span style="color:#FFCB6B;">material</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">PolylineOutlineMaterialProperty</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ORANGE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span></code></pre></div><h2 id="数据源系统" tabindex="-1">数据源系统 <a class="header-anchor" href="#数据源系统" aria-label="Permalink to &quot;数据源系统&quot;">​</a></h2><h3 id="datasource-数据源" tabindex="-1">DataSource（数据源） <a class="header-anchor" href="#datasource-数据源" aria-label="Permalink to &quot;DataSource（数据源）&quot;">​</a></h3><p>DataSource是Entity的容器，用于管理大量实体。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建自定义数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> dataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">CustomDataSource</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">myDataSource</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 添加实体到数据源</span></span>
<span class="line"><span style="color:#BABED8;">dataSource</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 将数据源添加到viewer</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(dataSource)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="内置数据源" tabindex="-1">内置数据源 <a class="header-anchor" href="#内置数据源" aria-label="Permalink to &quot;内置数据源&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// GeoJSON数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> geoJsonDataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">GeoJsonDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data.geojson</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(geoJsonDataSource)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// KML数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> kmlDataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">KmlDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data.kml</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(kmlDataSource)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// CZML数据源</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> czmlDataSource </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CzmlDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data.czml</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(czmlDataSource)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="时间系统" tabindex="-1">时间系统 <a class="header-anchor" href="#时间系统" aria-label="Permalink to &quot;时间系统&quot;">​</a></h2><h3 id="juliandate-儒略日" tabindex="-1">JulianDate（儒略日） <a class="header-anchor" href="#juliandate-儒略日" aria-label="Permalink to &quot;JulianDate（儒略日）&quot;">​</a></h3><p>Cesium使用儒略日表示时间。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建当前时间</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> now </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">now</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 从ISO8601字符串创建时间</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> time </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T12:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 时间计算</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> tomorrow </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addDays</span><span style="color:#BABED8;">(now</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">JulianDate</span><span style="color:#BABED8;">())</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="clock-时钟" tabindex="-1">Clock（时钟） <a class="header-anchor" href="#clock-时钟" aria-label="Permalink to &quot;Clock（时钟）&quot;">​</a></h3><p>Clock控制时间的流逝和动画。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> clock </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 设置时间范围</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">startTime </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T00:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">stopTime </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-02T00:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">currentTime </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">startTime</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 设置时钟模式</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">clockRange </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ClockRange</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LOOP_STOP</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 循环播放</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">multiplier </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">3600</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 时间倍速（1小时/秒）</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 启动时钟</span></span>
<span class="line"><span style="color:#BABED8;">clock</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">shouldAnimate </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="时间动态属性" tabindex="-1">时间动态属性 <a class="header-anchor" href="#时间动态属性" aria-label="Permalink to &quot;时间动态属性&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建时间相关的位置属性</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> property </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">SampledPositionProperty</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 添加时间样本</span></span>
<span class="line"><span style="color:#BABED8;">property</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addSample</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T00:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.0</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#BABED8;">property</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addSample</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">JulianDate</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromIso8601</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">2023-01-01T01:00:00Z</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">117.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">40.0</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 将属性应用到实体</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> entity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> property</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="图层系统" tabindex="-1">图层系统 <a class="header-anchor" href="#图层系统" aria-label="Permalink to &quot;图层系统&quot;">​</a></h2><h3 id="imagerylayer-影像图层" tabindex="-1">ImageryLayer（影像图层） <a class="header-anchor" href="#imagerylayer-影像图层" aria-label="Permalink to &quot;ImageryLayer（影像图层）&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 添加影像图层</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> imageryLayer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addImageryProvider</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">BingMapsImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://dev.virtualearth.net</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">key</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-bing-maps-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">mapStyle</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BingMapsStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">AERIAL</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 调整图层属性</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">alpha </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 透明度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">brightness </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.2</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 亮度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">contrast </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 对比度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">saturation </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 饱和度</span></span></code></pre></div><h3 id="terrainprovider-地形提供者" tabindex="-1">TerrainProvider（地形提供者） <a class="header-anchor" href="#terrainprovider-地形提供者" aria-label="Permalink to &quot;TerrainProvider（地形提供者）&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 设置地形提供者</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">terrainProvider </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">CesiumTerrainProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">IonResource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromAssetId</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">1</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">requestWaterMask</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">requestVertexNormals</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="事件系统" tabindex="-1">事件系统 <a class="header-anchor" href="#事件系统" aria-label="Permalink to &quot;事件系统&quot;">​</a></h2><h3 id="鼠标事件" tabindex="-1">鼠标事件 <a class="header-anchor" href="#鼠标事件" aria-label="Permalink to &quot;鼠标事件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> handler </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">ScreenSpaceEventHandler</span><span style="color:#BABED8;">(viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 左键点击</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">左键点击:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LEFT_CLICK)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 鼠标移动</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">鼠标移动:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">endPosition</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">MOUSE_MOVE)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 滚轮缩放</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">滚轮事件:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">event</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHEEL)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="相机事件" tabindex="-1">相机事件 <a class="header-anchor" href="#相机事件" aria-label="Permalink to &quot;相机事件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 相机移动开始</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">moveStart</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">相机开始移动</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机移动结束</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">moveEnd</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">相机移动结束</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机位置改变</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">changed</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">percentage</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">相机位置改变:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">percentage</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="场景事件" tabindex="-1">场景事件 <a class="header-anchor" href="#场景事件" aria-label="Permalink to &quot;场景事件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 渲染前事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">preRender</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">scene</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">time</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 在每帧渲染前执行</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 渲染后事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">postRender</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">scene</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">time</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 在每帧渲染后执行</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 地形加载事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">tileLoadProgressEvent</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">queuedTileCount</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">待加载瓦片数量:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">queuedTileCount</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="性能考虑" tabindex="-1">性能考虑 <a class="header-anchor" href="#性能考虑" aria-label="Permalink to &quot;性能考虑&quot;">​</a></h2><h3 id="实体管理" tabindex="-1">实体管理 <a class="header-anchor" href="#实体管理" aria-label="Permalink to &quot;实体管理&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 批量添加实体时使用 suspendEvents</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">suspendEvents</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">for</span><span style="color:#BABED8;"> (</span><span style="color:#C792EA;">let</span><span style="color:#BABED8;"> i </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> i </span><span style="color:#89DDFF;">&lt;</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> i</span><span style="color:#89DDFF;">++</span><span style="color:#BABED8;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#F78C6C;">116</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#F78C6C;">39</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">    )</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    point</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">5</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">resumeEvents</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="距离显示条件" tabindex="-1">距离显示条件 <a class="header-anchor" href="#距离显示条件" aria-label="Permalink to &quot;距离显示条件&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 根据距离显示不同细节级别</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> entity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 只在特定距离范围内显示</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">distanceDisplayCondition</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">DistanceDisplayCondition</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10000</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="下一步" tabindex="-1">下一步 <a class="header-anchor" href="#下一步" aria-label="Permalink to &quot;下一步&quot;">​</a></h2><ul><li><a href="/cesium/operations.html">常用操作</a> - 学习具体的地图操作和交互功能</li></ul>`,84),e=[o];function t(c,r,D,y,F,i){return a(),n("div",null,e)}const E=s(p,[["render",t]]);export{A as __pageData,E as default};
