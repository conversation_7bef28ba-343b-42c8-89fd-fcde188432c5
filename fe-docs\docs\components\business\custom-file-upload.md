# CustomFileUpload 自定义文件上传组件

支持多种文件格式的上传组件，基于ElementUI Upload组件封装，提供完整的文件列表管理功能。

## 功能特性

- 📁 支持多文件上传，可设置数量限制
- 🔧 支持文件类型和大小限制
- 📋 内置文件列表展示和管理
- 🗑️ 支持文件删除功能
- 🔗 支持文件预览和下载
- ⚡ 自动处理上传状态和错误
- 🎨 美观的文件列表界面

## 基础用法

### 基础文件上传

```vue
<template>
  <div>
    <custom-file-upload
      v-model="fileList"
      :limit="5"
      :file-size="10"
      :file-type="['pdf', 'doc', 'docx']"
      @input="handleFileChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      fileList: ''
    }
  },
  methods: {
    handleFileChange(fileList, fileName) {
      console.log('文件列表改变:', fileList)
      console.log('文件字段名:', fileName)
    }
  }
}
</script>
```

### 带自定义参数的上传

```vue
<template>
  <div>
    <custom-file-upload
      v-model="documents"
      :limit="3"
      :file-size="50"
      :file-type="['pdf', 'doc', 'docx', 'xls', 'xlsx']"
      file-name="documents"
      file-url="/api/upload/documents"
      :data="uploadData"
      :is-show-tip="true"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      documents: '',
      uploadData: {
        category: 'business',
        userId: 123
      }
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| value / v-model | 绑定值，文件URL字符串或文件对象数组 | String/Object/Array | — | — | 是 |
| limit | 文件数量限制 | Number | — | 5 | 否 |
| fileSize | 文件大小限制(MB) | Number | — | 100 | 否 |
| fileType | 允许的文件类型数组 | Array | — | ['doc', 'xls', 'ppt', 'txt', 'pdf'] | 否 |
| isShowTip | 是否显示上传提示 | Boolean | true/false | true | 否 |
| fileUrl | 文件上传接口地址 | String | — | '/file/custom/upload' | 否 |
| fileName | 文件字段名（用于标识文件） | String | — | '' | 否 |
| data | 额外的上传参数 | Object | — | {} | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 文件列表改变时触发 | (fileList: string, fileName: string) |

### Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| handleBeforeUpload | 上传前的钩子函数 | (file: File) | Boolean |
| handleUploadSuccess | 上传成功的钩子函数 | (response: Object, file: File) | — |
| handleUploadError | 上传失败的钩子函数 | (error: Error) | — |
| handleDelete | 删除文件的方法 | (index: number) | — |

## 文件类型支持

### 默认支持的文件类型

| 类型 | 扩展名 | 说明 |
|------|--------|------|
| 文档 | doc, docx | Word文档 |
| 表格 | xls, xlsx | Excel表格 |
| 演示 | ppt, pptx | PowerPoint演示文稿 |
| PDF | pdf | PDF文档 |
| 文本 | txt | 纯文本文件 |

### 自定义文件类型

```vue
<template>
  <div>
    <!-- 支持图片和文档 -->
    <custom-file-upload
      v-model="files"
      :file-type="['jpg', 'png', 'pdf', 'doc']"
      :file-size="20"
    />
    
    <!-- 支持压缩包 -->
    <custom-file-upload
      v-model="archives"
      :file-type="['zip', 'rar', '7z']"
      :file-size="100"
    />
  </div>
</template>
```

## 使用示例

### 合同文件上传

```vue
<template>
  <div>
    <el-form :model="contractForm" label-width="120px">
      <el-form-item label="合同名称">
        <el-input v-model="contractForm.name" />
      </el-form-item>
      
      <el-form-item label="合同文件">
        <custom-file-upload
          v-model="contractForm.files"
          :limit="3"
          :file-size="50"
          :file-type="['pdf', 'doc', 'docx']"
          file-name="contract"
          placeholder="请上传合同文件"
        />
      </el-form-item>
      
      <el-form-item label="附件">
        <custom-file-upload
          v-model="contractForm.attachments"
          :limit="10"
          :file-size="20"
          :file-type="['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png']"
          file-name="attachment"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      contractForm: {
        name: '',
        files: '',
        attachments: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      // 处理表单提交
      console.log('合同数据:', this.contractForm)
    }
  }
}
</script>
```

### 批量文档上传

```vue
<template>
  <div>
    <el-card header="文档批量上传">
      <custom-file-upload
        v-model="batchFiles"
        :limit="20"
        :file-size="100"
        :file-type="documentTypes"
        file-name="batch_documents"
        :data="uploadParams"
        @input="handleBatchUpload"
      />
      
      <div class="upload-stats" v-if="uploadStats.total > 0">
        <p>已上传: {{ uploadStats.success }} / {{ uploadStats.total }}</p>
        <p>失败: {{ uploadStats.failed }}</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      batchFiles: '',
      documentTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
      uploadParams: {
        category: 'batch',
        department: 'finance'
      },
      uploadStats: {
        total: 0,
        success: 0,
        failed: 0
      }
    }
  },
  methods: {
    handleBatchUpload(fileList) {
      if (fileList) {
        const files = fileList.split(',')
        this.uploadStats.total = files.length
        this.uploadStats.success = files.length
        this.uploadStats.failed = 0
      }
    }
  }
}
</script>

<style scoped>
.upload-stats {
  margin-top: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
```

## 高级特性

### 自定义上传参数

```vue
<template>
  <div>
    <custom-file-upload
      v-model="files"
      :data="dynamicParams"
      file-url="/api/upload/custom"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      files: '',
      userId: 123,
      projectId: 456
    }
  },
  computed: {
    dynamicParams() {
      return {
        userId: this.userId,
        projectId: this.projectId,
        timestamp: Date.now()
      }
    }
  }
}
</script>
```

### 上传进度监控

```vue
<template>
  <div>
    <custom-file-upload
      ref="fileUpload"
      v-model="files"
      @input="handleUploadProgress"
    />
    
    <el-progress 
      v-if="uploading"
      :percentage="uploadProgress"
      :status="uploadStatus"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      files: '',
      uploading: false,
      uploadProgress: 0,
      uploadStatus: ''
    }
  },
  methods: {
    handleUploadProgress(fileList) {
      // 监控上传进度的逻辑
      this.uploading = true
      this.uploadProgress = 100
      this.uploadStatus = 'success'
      
      setTimeout(() => {
        this.uploading = false
      }, 1000)
    }
  }
}
</script>
```

## 注意事项

1. **文件格式**：确保后端接口支持指定的文件类型
2. **文件大小**：合理设置文件大小限制，避免上传过大文件
3. **数量限制**：根据业务需求设置合适的文件数量限制
4. **网络异常**：组件已内置网络异常处理，会自动显示错误提示
5. **权限控制**：确保用户有相应的文件上传权限

## 常见问题

### Q: 如何获取已上传的文件列表？

A: 组件的 `v-model` 绑定值会返回文件URL字符串，多个文件以逗号分隔。

### Q: 如何自定义上传接口？

A: 通过 `file-url` 属性设置自定义的上传接口地址。

### Q: 支持拖拽上传吗？

A: 当前版本基于ElementUI Upload组件，支持所有其原生特性，包括拖拽上传。

### Q: 如何处理上传失败？

A: 组件内置了错误处理机制，上传失败时会自动显示错误提示，并允许重新上传。

### Q: 文件预览功能如何实现？

A: 点击文件名会在新窗口中打开文件进行预览，需要确保服务器支持文件访问。

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<template>
  <div class="upload-file">
    <el-upload
      multiple
      ref="fileUpload"
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      :data="data"
    >
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary">选取文件</el-button>
      <!-- 上传提示 -->
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b></template>
        <template v-if="fileType">
          格式为
          <b style="color: #f56c6c">{{ fileType.join("/") }}</b></template>
        的文件
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group
      :style="{ height: fileList.length > 3 ? 125 + 'px' : 'auto' }"
      style="overflow: auto"
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        v-for="(file, index) in fileList"
        :key="file.url"
        class="el-upload-list__item ele-upload-list__item-content"
      >
        <el-link
          :href="`${baseUrl}/file/static${file.url}`"
          :underline="false"
          target="_blank"
        >
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link
            :underline="false"
            type="danger"
            @click="handleDelete(index)"
          >删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'CustomFileUpload',
  props: {
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 100
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ['doc', 'xls', 'ppt', 'txt', 'pdf']
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    fileUrl: {
      type: String,
      default: process.env.VUE_APP_BASE_API + '/file/custom/upload'
    },
    fileName: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: this.fileUrl, // 上传文件服务器地址
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      fileList: []
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',')
          // 然后将数组转为对象数组
          this.fileList = list.map((item) => {
            if (typeof item === 'string') {
              item = { name: item, url: item }
            }
            item.uid = item.uid || new Date().getTime() + temp++
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        const fileName = file.name.split('.')
        const fileExt = fileName[fileName.length - 1]
        const isTypeOk = this.fileType.indexOf(fileExt) >= 0
        if (!isTypeOk) {
          this.$modal.msgError(
            `文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`
          )
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$modal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },
    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError('上传文件失败，请重试')
      this.$modal.closeLoading()
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        this.uploadList.push({ name: res.data.name, url: res.data.url })
        this.uploadedSuccessfully()
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.fileUpload.handleRemove(file)
        this.uploadedSuccessfully()
      }
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1)
      const url = this.fileList.map((el) => el.url).join(',')
      const list = url.split(',')
      const imgStr = list
        .map((el) => {
          if (el.split('static').length === 2) {
            return el.split('static')[1]
          } else {
            return el.split('static')[0]
          }
        })
        .join(',')
      this.$emit('input', imgStr, this.fileName, this.fileName)
    },
    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0
        const url = this.fileList.map((el) => el.url).join(',')
        const list = url.split(',')
        const imgStr = list
          .map((el) => {
            if (el.split('static').length === 2) {
              return el.split('static')[1]
            } else {
              return el.split('static')[0]
            }
          })
          .join(',')
        this.$emit('input', imgStr, this.fileName)
        this.$modal.closeLoading()
      }
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1)
      } else {
        return name
      }
    },
    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ''
      separator = separator || ','
      for (const i in list) {
        strs += list[i].url + separator
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : ''
    }
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
```

</details>
