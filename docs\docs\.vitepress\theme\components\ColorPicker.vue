<template>
    <div class="color-picker">
        <button
            class="color-picker-trigger"
            @click="togglePicker"
            :title="isOpen ? '关闭色彩选择器' : '打开色彩选择器'"
        >
            <div class="palette-icon">
                <div class="palette-circle"></div>
                <div class="palette-colors">
                    <div class="color-dot color-1"></div>
                    <div class="color-dot color-2"></div>
                    <div class="color-dot color-3"></div>
                    <div class="color-dot color-4"></div>
                </div>
            </div>
        </button>

        <transition name="picker-fade">
            <div v-if="isOpen" class="color-picker-panel">
                <div class="color-picker-header">
                    <h3>选择主题色</h3>
                    <button class="close-btn" @click="closePicker">×</button>
                </div>

                <div class="color-presets">
                    <div class="preset-label">预设颜色</div>
                    <div class="preset-colors">
                        <button
                            v-for="preset in colorPresets"
                            :key="preset.name"
                            class="preset-color"
                            :class="{ active: currentColor === preset.primary }"
                            :style="{ backgroundColor: preset.primary }"
                            @click="selectColor(preset)"
                            :title="preset.name"
                        ></button>
                    </div>
                </div>

                <div class="custom-color">
                    <div class="custom-label">自定义颜色</div>
                    <div class="color-input-group">
                        <input
                            type="color"
                            v-model="customColor"
                            @input="onCustomColorChange"
                            class="color-input"
                        />
                        <input
                            type="text"
                            v-model="customColor"
                            @input="onCustomColorInput"
                            class="color-text"
                            placeholder="#2563eb"
                        />
                    </div>
                </div>

                <div class="color-preview">
                    <div class="preview-label">预览效果</div>
                    <div class="preview-items">
                        <div class="preview-button" :style="{ backgroundColor: previewColor }">
                            按钮样式
                        </div>
                        <div class="preview-link" :style="{ color: previewColor }">链接样式</div>
                        <div class="preview-border" :style="{ borderColor: previewColor }">
                            边框样式
                        </div>
                    </div>
                </div>

                <div class="color-actions">
                    <button class="reset-btn" @click="resetColor">重置默认</button>
                    <button
                        class="apply-btn"
                        @click="applyColor"
                        :style="{ backgroundColor: previewColor }"
                    >
                        应用主题
                    </button>
                </div>
            </div>
        </transition>

        <div v-if="isOpen" class="color-picker-overlay" @click="closePicker"></div>
    </div>
</template>

<script>
    export default {
        name: 'ColorPicker',
        data() {
            return {
                isOpen: false,
                currentColor: '#2563eb',
                customColor: '#2563eb',
                previewColor: '#2563eb',
                colorPresets: [
                    { name: '蓝色', primary: '#2563eb', light: '#3b82f6' },
                    { name: '青色', primary: '#0891b2', light: '#06b6d4' },
                    { name: '绿色', primary: '#059669', light: '#10b981' },
                    { name: '紫色', primary: '#7c3aed', light: '#8b5cf6' },
                    { name: '粉色', primary: '#db2777', light: '#ec4899' },
                    { name: '橙色', primary: '#ea580c', light: '#f97316' },
                    { name: '红色', primary: '#dc2626', light: '#ef4444' },
                    { name: '灰色', primary: '#4b5563', light: '#6b7280' },
                ],
            }
        },
        mounted() {
            // 从本地存储加载保存的颜色
            const savedColor = localStorage.getItem('vitepress-theme-color')
            if (savedColor) {
                this.currentColor = savedColor
                this.customColor = savedColor
                this.previewColor = savedColor
                this.applyColorToCSS(savedColor)
            }
        },
        methods: {
            togglePicker() {
                this.isOpen = !this.isOpen
                if (this.isOpen) {
                    this.previewColor = this.currentColor
                    this.customColor = this.currentColor
                }
            },

            closePicker() {
                this.isOpen = false
            },

            selectColor(preset) {
                this.previewColor = preset.primary
                this.customColor = preset.primary
            },

            onCustomColorChange() {
                this.previewColor = this.customColor
            },

            onCustomColorInput() {
                // 验证颜色格式
                if (/^#[0-9A-F]{6}$/i.test(this.customColor)) {
                    this.previewColor = this.customColor
                }
            },

            applyColor() {
                this.currentColor = this.previewColor
                this.applyColorToCSS(this.previewColor)

                // 保存到本地存储
                localStorage.setItem('vitepress-theme-color', this.previewColor)

                this.closePicker()

                // 显示成功提示
                this.showToast('主题色已更新！')
            },

            resetColor() {
                const defaultColor = '#2563eb'
                this.previewColor = defaultColor
                this.customColor = defaultColor
                this.currentColor = defaultColor
                this.applyColorToCSS(defaultColor)
                localStorage.removeItem('vitepress-theme-color')
                this.showToast('已重置为默认主题色')
            },

            applyColorToCSS(color) {
                // 生成浅色变体
                const lightColor = this.lightenColor(color, 20)
                const lighterColor = this.lightenColor(color, 40)

                // 更新CSS变量
                document.documentElement.style.setProperty('--vp-c-brand', color)
                document.documentElement.style.setProperty('--vp-c-brand-light', lightColor)
                document.documentElement.style.setProperty('--vp-c-brand-lighter', lighterColor)
                document.documentElement.style.setProperty('--vp-c-brand-1', color)
                document.documentElement.style.setProperty('--vp-c-brand-2', lightColor)
                document.documentElement.style.setProperty('--vp-c-brand-3', lighterColor)

                // 更新软色调
                const softColor = this.hexToRgba(color, 0.14)
                document.documentElement.style.setProperty('--vp-c-brand-soft', softColor)
            },

            lightenColor(hex, percent) {
                // 将十六进制转换为RGB
                const num = parseInt(hex.replace('#', ''), 16)
                const amt = Math.round(2.55 * percent)
                const R = (num >> 16) + amt
                const G = ((num >> 8) & 0x00ff) + amt
                const B = (num & 0x0000ff) + amt

                return (
                    '#' +
                    (
                        0x1000000 +
                        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
                        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
                        (B < 255 ? (B < 1 ? 0 : B) : 255)
                    )
                        .toString(16)
                        .slice(1)
                )
            },

            hexToRgba(hex, alpha) {
                const r = parseInt(hex.slice(1, 3), 16)
                const g = parseInt(hex.slice(3, 5), 16)
                const b = parseInt(hex.slice(5, 7), 16)
                return `rgba(${r}, ${g}, ${b}, ${alpha})`
            },

            showToast(message) {
                // 简单的提示实现
                const toast = document.createElement('div')
                toast.textContent = message
                toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--vp-c-brand);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
      `

                document.body.appendChild(toast)

                setTimeout(() => {
                    toast.style.opacity = '0'
                    toast.style.transform = 'translateX(100%)'
                    setTimeout(() => {
                        document.body.removeChild(toast)
                    }, 300)
                }, 2000)
            },
        },
    }
</script>

<style lang="scss" scoped>
    @use 'sass:map';
    @use '../styles/variables' as vars;
    @use '../styles/mixins' as mixins;

    .color-picker {
        position: relative;
        display: inline-block;
    }

    .color-picker-trigger {
        @include mixins.center;
        width: 40px;
        height: 40px;
        border: none;
        background: transparent;
        border-radius: map.get(vars.$border-radius, md);
        cursor: pointer;
        color: var(--vp-c-text-1);
        transition: all map.get(vars.$transitions, normal);

        &:hover {
            background: var(--vp-c-bg-soft);

            .palette-icon {
                transform: scale(1.1);
            }
        }
    }

    /* CSS调色板图标 */
    .palette-icon {
        position: relative;
        width: 20px;
        height: 20px;
        transition: transform map.get(vars.$transitions, normal);
    }

    .palette-circle {
        width: 20px;
        height: 20px;
        border: 2px solid currentColor;
        border-radius: 50%;
        position: relative;
        background: transparent;
    }

    .palette-colors {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 12px;
        height: 12px;
    }

    .color-dot {
        position: absolute;
        width: 3px;
        height: 3px;
        border-radius: 50%;
    }

    .color-1 {
        top: 1px;
        left: 4px;
        background: #ef4444;
    }

    .color-2 {
        top: 4px;
        right: 1px;
        background: #3b82f6;
    }

    .color-3 {
        bottom: 1px;
        left: 4px;
        background: #10b981;
    }

    .color-4 {
        top: 4px;
        left: 1px;
        background: #f59e0b;
    }

    .color-picker-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 999;
    }

    .color-picker-panel {
        position: absolute;
        top: 50px;
        right: 0;
        width: 320px;
        background: var(--vp-c-bg);
        border: 1px solid var(--vp-c-divider);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        z-index: 1000;
        padding: 20px;
        backdrop-filter: blur(8px);
    }

    .dark .color-picker-panel {
        background: var(--vp-c-bg-alt);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .color-picker-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .color-picker-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--vp-c-text-1);
    }

    .close-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        font-size: 18px;
        cursor: pointer;
        color: var(--vp-c-text-2);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .close-btn:hover {
        background: var(--vp-c-bg-soft);
        color: var(--vp-c-text-1);
    }

    .preset-label,
    .custom-label,
    .preview-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--vp-c-text-1);
        margin-bottom: 8px;
    }

    .preset-colors {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
        margin-bottom: 20px;
    }

    .preset-color {
        width: 40px;
        height: 40px;
        border: 2px solid transparent;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .preset-color:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .preset-color.active {
        border-color: var(--vp-c-text-1);
        transform: scale(1.1);
    }

    .color-input-group {
        display: flex;
        gap: 8px;
        margin-bottom: 20px;
    }

    .color-input {
        width: 60px;
        height: 40px;
        border: 1px solid var(--vp-c-divider);
        border-radius: 8px;
        cursor: pointer;
    }

    .color-text {
        flex: 1;
        height: 40px;
        padding: 0 12px;
        border: 1px solid var(--vp-c-divider);
        border-radius: 8px;
        background: var(--vp-c-bg);
        color: var(--vp-c-text-1);
        font-family: monospace;
    }

    .color-text:focus {
        outline: none;
        border-color: var(--vp-c-brand);
    }

    .preview-items {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .preview-button {
        padding: 8px 16px;
        border-radius: 6px;
        color: white;
        font-size: 12px;
        font-weight: 500;
    }

    .preview-link {
        padding: 8px 0;
        font-size: 12px;
        font-weight: 500;
        text-decoration: underline;
    }

    .preview-border {
        padding: 8px 16px;
        border: 2px solid;
        border-radius: 6px;
        font-size: 12px;
        color: var(--vp-c-text-1);
    }

    .color-actions {
        display: flex;
        gap: 8px;
    }

    .reset-btn,
    .apply-btn {
        flex: 1;
        height: 40px;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .reset-btn {
        background: var(--vp-c-bg-soft);
        color: var(--vp-c-text-1);
    }

    .reset-btn:hover {
        background: var(--vp-c-bg-mute);
    }

    .apply-btn {
        color: white;
    }

    .apply-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .picker-fade-enter-active,
    .picker-fade-leave-active {
        transition: all 0.3s ease;
    }

    .picker-fade-enter-from,
    .picker-fade-leave-to {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
</style>
