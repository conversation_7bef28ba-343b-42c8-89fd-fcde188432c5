# v-copy

提供一键复制文本功能。

## 使用场景

- 复制链接、文本内容
- 复制配置信息、代码片段

## 基本用法

```vue
<template>
  <div>
    <span>{{text}}</span>
    <el-button v-copy="text">复制文本</el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      text: '这是要复制的文本内容'
    }
  }
}
</script>
```

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directives/copy.js
export default {
  bind(el, binding) {
    // 获取要复制的文本
    const value = binding.value;
    
    // 设置元素样式，表明可点击
    el.style.cursor = 'pointer';
    
    // 创建点击事件处理函数
    const copyHandler = () => {
      // 如果没有值，不执行复制
      if (!value && value !== 0) {
        console.warn('v-copy指令需要一个非空值');
        return;
      }
      
      // 创建一个临时textarea元素
      const textarea = document.createElement('textarea');
      // 设置要复制的文本
      textarea.value = typeof value === 'object' ? JSON.stringify(value) : String(value);
      // 设置样式使其不可见
      textarea.style.position = 'fixed';
      textarea.style.top = '-9999px';
      textarea.style.left = '-9999px';
      textarea.style.zIndex = '-9999';
      
      // 添加到DOM
      document.body.appendChild(textarea);
      
      // 选中文本
      textarea.select();
      textarea.setSelectionRange(0, textarea.value.length);
      
      // 尝试复制
      let success = false;
      try {
        success = document.execCommand('copy');
      } catch (err) {
        console.error('复制失败:', err);
      }
      
      // 移除临时元素
      document.body.removeChild(textarea);
      
      // 显示复制结果提示
      if (success) {
        // 如果项目中使用了Element UI，可以使用其Message组件
        if (window.ELEMENT && window.ELEMENT.Message) {
          window.ELEMENT.Message.success('复制成功');
        } else {
          alert('复制成功');
        }
      } else {
        if (window.ELEMENT && window.ELEMENT.Message) {
          window.ELEMENT.Message.error('复制失败');
        } else {
          alert('复制失败');
        }
      }
    };
    
    // 保存处理函数，用于解绑
    el._copyHandler = copyHandler;
    
    // 绑定点击事件
    el.addEventListener('click', copyHandler);
  },
  
  // 当传入的值变化时，更新要复制的内容
  update(el, binding) {
    // 更新要复制的值
    el._copyValue = binding.value;
  },
  
  // 指令与元素解绑时
  unbind(el) {
    // 解绑点击事件
    if (el._copyHandler) {
      el.removeEventListener('click', el._copyHandler);
      delete el._copyHandler;
    }
  }
};
```
</details>
