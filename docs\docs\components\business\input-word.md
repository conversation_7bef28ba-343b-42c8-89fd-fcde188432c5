# InputWord 自定义文本输入组件

基于ElementUI Input组件的增强版本，支持字符长度限制和自动去除空格功能，适用于各种文本输入场景。

## 功能特性

- ✂️ 自动去除输入内容的前后空格
- 📏 支持字符长度限制和实时显示
- 📝 支持单行和多行文本输入
- 🧹 失去焦点时自动清理空格
- 🔧 完全兼容ElementUI Input的所有功能

## 基础用法

```vue
<template>
  <div>
    <!-- 基础文本输入 -->
    <input-word 
      v-model="name" 
      :maxlength="50"
      placeholder="请输入姓名"
      clearable
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      name: ''
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| value / v-model | 绑定值 | String | — | undefined | 否 |
| placeholder | 输入框占位文本 | String | — | null | 否 |
| maxlength | 最大字符长度 | Number | — | null | 否 |
| disabled | 是否禁用 | Boolean | true/false | false | 否 |
| type | 输入框类型 | String | text/textarea | text | 否 |
| rows | 文本域行数（仅在type="textarea"时有效） | Number | — | null | 否 |
| clearable | 是否可清空 | Boolean | true/false | false | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 绑定值被改变时触发 | (value: string) |
| change | 仅在输入框失去焦点或用户按下回车时触发 | (value: string) |
| blur | 在 Input 失去焦点时触发 | (event: Event) |

### 特殊功能

- **自动去空格**：在 `blur` 事件时自动去除输入内容的前后空格
- **字符计数**：当设置 `maxlength` 时，自动显示字符计数
- **类型切换**：支持 `text` 和 `textarea` 两种模式

## 使用示例

### 用户信息输入

```vue
<template>
  <div>
    <el-form :model="userForm" label-width="120px">
      <el-form-item label="用户名">
        <input-word 
          v-model="userForm.username"
          :maxlength="20"
          placeholder="请输入用户名"
          clearable
          @change="validateUsername"
        />
      </el-form-item>
      
      <el-form-item label="真实姓名">
        <input-word 
          v-model="userForm.realName"
          :maxlength="10"
          placeholder="请输入真实姓名"
        />
      </el-form-item>
      
      <el-form-item label="个人简介">
        <input-word 
          v-model="userForm.bio"
          type="textarea"
          :rows="4"
          :maxlength="200"
          placeholder="请输入个人简介"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userForm: {
        username: '',
        realName: '',
        bio: ''
      }
    }
  },
  methods: {
    validateUsername(value) {
      // 用户名验证逻辑
      if (value && value.length < 3) {
        this.$message.warning('用户名长度不能少于3个字符')
      }
    }
  }
}
</script>
```

### 商品信息编辑

```vue
<template>
  <div>
    <el-card header="商品信息">
      <el-form :model="product" label-width="100px">
        <el-form-item label="商品名称">
          <input-word 
            v-model="product.name"
            :maxlength="100"
            placeholder="请输入商品名称"
            clearable
          />
        </el-form-item>
        
        <el-form-item label="品牌">
          <input-word 
            v-model="product.brand"
            :maxlength="50"
            placeholder="请输入品牌名称"
          />
        </el-form-item>
        
        <el-form-item label="商品描述">
          <input-word 
            v-model="product.description"
            type="textarea"
            :rows="6"
            :maxlength="1000"
            placeholder="请输入商品描述"
          />
        </el-form-item>
        
        <el-form-item label="关键词">
          <input-word 
            v-model="product.keywords"
            type="textarea"
            :rows="3"
            :maxlength="500"
            placeholder="请输入关键词，用逗号分隔"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      product: {
        name: '',
        brand: '',
        description: '',
        keywords: ''
      }
    }
  },
  watch: {
    product: {
      handler(newProduct) {
        // 自动保存草稿
        this.saveDraft(newProduct)
      },
      deep: true
    }
  },
  methods: {
    saveDraft(product) {
      // 防抖保存
      clearTimeout(this.saveTimer)
      this.saveTimer = setTimeout(() => {
        localStorage.setItem('product-draft', JSON.stringify(product))
      }, 1000)
    }
  }
}
</script>
```

### 配置项输入

```vue
<template>
  <div>
    <el-form :model="config" label-width="150px">
      <el-form-item label="应用名称">
        <input-word 
          v-model="config.appName"
          :maxlength="30"
          placeholder="请输入应用名称"
        />
      </el-form-item>
      
      <el-form-item label="API地址">
        <input-word 
          v-model="config.apiUrl"
          :maxlength="200"
          placeholder="https://api.example.com"
          @blur="validateUrl"
        />
      </el-form-item>
      
      <el-form-item label="备注信息">
        <input-word 
          v-model="config.remark"
          type="textarea"
          :rows="3"
          :maxlength="300"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      config: {
        appName: '',
        apiUrl: '',
        remark: ''
      }
    }
  },
  methods: {
    validateUrl() {
      const url = this.config.apiUrl
      if (url && !url.startsWith('http')) {
        this.$message.warning('请输入有效的URL地址')
      }
    }
  }
}
</script>
```

### 搜索和过滤

```vue
<template>
  <div>
    <el-card header="搜索条件">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="关键词">
            <input-word 
              v-model="searchForm.keyword"
              :maxlength="50"
              placeholder="请输入搜索关键词"
              clearable
              @input="handleSearch"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="标签">
            <input-word 
              v-model="searchForm.tags"
              :maxlength="100"
              placeholder="标签1,标签2,标签3"
              @change="parseTags"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="备注">
            <input-word 
              v-model="searchForm.remark"
              :maxlength="100"
              placeholder="备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    
    <div class="parsed-tags" v-if="parsedTags.length > 0">
      <span>解析的标签：</span>
      <el-tag 
        v-for="tag in parsedTags" 
        :key="tag"
        style="margin-right: 10px;"
      >
        {{ tag }}
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        keyword: '',
        tags: '',
        remark: ''
      },
      parsedTags: []
    }
  },
  methods: {
    handleSearch() {
      // 防抖搜索
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.performSearch()
      }, 500)
    },
    
    parseTags(tagsStr) {
      if (tagsStr) {
        this.parsedTags = tagsStr
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag)
      } else {
        this.parsedTags = []
      }
    },
    
    performSearch() {
      console.log('执行搜索:', this.searchForm)
    }
  }
}
</script>

<style scoped>
.parsed-tags {
  margin-top: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
```

## 注意事项

1. **空格处理**：组件会在失去焦点时自动去除前后空格
2. **字符计数**：中文字符和英文字符都按1个字符计算
3. **性能优化**：大量输入组件时建议使用防抖处理
4. **数据验证**：建议在 `change` 事件中进行数据验证

## 常见问题

### Q: 如何禁用自动去空格功能？

A: 当前版本默认启用自动去空格，如需禁用可以考虑直接使用原生 ElementUI Input。

### Q: 支持输入法吗？

A: 完全支持各种输入法，包括中文输入法。

### Q: 如何实现实时验证？

A: 可以使用 `input` 事件进行实时验证，或使用 `change` 事件进行失焦验证。

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<!--
@File    index.vue
@Desc    自定义单词输入组件.
<AUTHOR> href="mailto:<EMAIL>">xiaoQQya</a>
@Date    2023/10/10
-->
<template>
  <el-input
    v-if="maxlength"
    v-bind:value="value"
    @input="input"
    :type="type"
    :rows="rows"
    :placeholder="placeholder"
    :maxlength="maxlength"
    show-word-limit
    :disabled="disabled"
    :clearable="clearable"
    @blur="e => blur(e.target.value)"
    @change="change"
  />
  <el-input
    v-else
    v-bind:value="value"
    @input="input"
    :type="type"
    :rows="rows"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    @blur="e => blur(e.target.value)"
    @change="change"
  />
</template>

<script>
import {strTrim} from "@/utils/zhy";

export default {
  name: "InputWord",
  props: {
    value: {
      type: String,
      default: undefined
    },
    placeholder: {
      type: String,
      default: null
    },
    maxlength: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      default: "text"
    },
    rows: {
      type: Number,
      default: null
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    input(value) {
      this.$emit("input", value);
    },
    blur(value) {
      this.$emit("input", strTrim(value));
    },
    change(value) {
      this.$emit("change", value);
    }
  }
}
</script>

<style scoped>
::v-deep .el-input__inner {
  padding: 0 55px 0 15px;
}

::v-deep .el-textarea__inner {
  padding: 5px 55px 5px 15px;
}

::v-deep .el-input__count {
  background-color: transparent;
}
</style>
```

</details>
