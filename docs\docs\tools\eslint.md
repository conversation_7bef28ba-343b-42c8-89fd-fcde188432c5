# ESLint 配置指南

ESLint 是一个用于识别和报告 JavaScript 代码中模式的工具，旨在使代码更加一致并避免错误。本文档提供了完整的 ESLint 配置指南。

## 安装和基础配置

### 1. 安装 ESLint

```bash
# 项目本地安装
npm install eslint --save-dev

# 全局安装
npm install -g eslint

# 初始化配置
npx eslint --init
```

### 2. Vue 项目 ESLint 配置

```bash
# 安装 Vue 相关插件
npm install --save-dev \
  eslint \
  eslint-plugin-vue \
  @vue/eslint-config-standard \
  eslint-plugin-import \
  eslint-plugin-node \
  eslint-plugin-promise
```

## 配置文件

### .eslintrc.js

项目根目录创建 `.eslintrc.js` 文件：

```javascript
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module'
  },
  rules: {
    // 基础规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    
    // 代码风格
    'indent': ['error', 2],
    'quotes': ['error', 'single'],
    'semi': ['error', 'never'],
    'comma-dangle': ['error', 'never'],
    
    // Vue 特定规则
    'vue/html-indent': ['error', 2],
    'vue/html-self-closing': ['error', {
      'html': {
        'void': 'never',
        'normal': 'always',
        'component': 'always'
      },
      'svg': 'always',
      'math': 'always'
    }],
    'vue/max-attributes-per-line': ['error', {
      'singleline': 3,
      'multiline': {
        'max': 1,
        'allowFirstLine': false
      }
    }],
    
    // 命名规范
    'camelcase': ['error', { 'properties': 'never' }],
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    
    // 空格和换行
    'space-before-function-paren': ['error', 'never'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    
    // 其他规则
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }],
    'no-multiple-empty-lines': ['error', { 'max': 1 }],
    'eol-last': ['error', 'always']
  },
  overrides: [
    {
      files: [
        '**/__tests__/*.{j,t}s?(x)',
        '**/tests/unit/**/*.spec.{j,t}s?(x)'
      ],
      env: {
        jest: true
      }
    }
  ]
}
```

### TypeScript 项目配置

如果使用 TypeScript，需要额外配置：

```bash
# 安装 TypeScript ESLint 插件
npm install --save-dev \
  @typescript-eslint/eslint-plugin \
  @typescript-eslint/parser
```

```javascript
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard',
    '@vue/typescript/recommended'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    parser: '@typescript-eslint/parser'
  },
  rules: {
    // TypeScript 特定规则
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }]
  }
}
```

## 忽略文件配置

### .eslintignore

创建 `.eslintignore` 文件忽略不需要检查的文件：

```bash
# 构建输出
dist/
build/

# 依赖
node_modules/

# 配置文件
*.config.js
.eslintrc.js

# 静态资源
public/
static/

# 测试覆盖率报告
coverage/

# 临时文件
*.tmp
*.temp

# 日志文件
*.log

# 特定文件
src/assets/js/vendor/
```

## 常用规则详解

### 1. 代码风格规则

```javascript
{
  // 缩进
  'indent': ['error', 2, { 
    'SwitchCase': 1,
    'VariableDeclarator': 1,
    'outerIIFEBody': 1
  }],
  
  // 引号
  'quotes': ['error', 'single', {
    'avoidEscape': true,
    'allowTemplateLiterals': true
  }],
  
  // 分号
  'semi': ['error', 'never'],
  
  // 逗号
  'comma-dangle': ['error', {
    'arrays': 'never',
    'objects': 'never',
    'imports': 'never',
    'exports': 'never',
    'functions': 'never'
  }],
  
  // 空格
  'space-before-function-paren': ['error', {
    'anonymous': 'always',
    'named': 'never',
    'asyncArrow': 'always'
  }]
}
```

### 2. Vue 特定规则

```javascript
{
  // 组件名称
  'vue/component-name-in-template-casing': ['error', 'PascalCase', {
    'registeredComponentsOnly': true
  }],
  
  // 属性顺序
  'vue/attributes-order': ['error', {
    'order': [
      'DEFINITION',
      'LIST_RENDERING',
      'CONDITIONALS',
      'RENDER_MODIFIERS',
      'GLOBAL',
      'UNIQUE',
      'TWO_WAY_BINDING',
      'OTHER_DIRECTIVES',
      'OTHER_ATTR',
      'EVENTS',
      'CONTENT'
    ]
  }],
  
  // 组件选项顺序
  'vue/order-in-components': ['error', {
    'order': [
      'el',
      'name',
      'parent',
      'functional',
      ['delimiters', 'comments'],
      ['components', 'directives', 'filters'],
      'extends',
      'mixins',
      'inheritAttrs',
      'model',
      ['props', 'propsData'],
      'data',
      'computed',
      'watch',
      'LIFECYCLE_HOOKS',
      'methods',
      ['template', 'render'],
      'renderError'
    ]
  }]
}
```

### 3. 错误预防规则

```javascript
{
  // 禁止未使用的变量
  'no-unused-vars': ['error', {
    'vars': 'all',
    'args': 'after-used',
    'ignoreRestSiblings': true
  }],
  
  // 禁止重复声明
  'no-redeclare': 'error',
  
  // 禁止不必要的分号
  'no-extra-semi': 'error',
  
  // 禁止不可达代码
  'no-unreachable': 'error',
  
  // 要求使用 === 和 !==
  'eqeqeq': ['error', 'always', { 'null': 'ignore' }]
}
```

## 集成到开发流程

### 1. package.json 脚本

```json
{
  "scripts": {
    "lint": "eslint --ext .js,.vue src",
    "lint:fix": "eslint --ext .js,.vue src --fix",
    "lint:check": "eslint --print-config src/main.js | eslint-config-prettier-check"
  }
}
```

### 2. VS Code 集成

在 `.vscode/settings.json` 中配置：

```json
{
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue"
  ],
  "eslint.options": {
    "extensions": [".js", ".vue"]
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### 3. Git Hooks 集成

使用 husky 和 lint-staged：

```bash
# 安装
npm install --save-dev husky lint-staged

# 配置 package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,vue}": [
      "eslint --fix",
      "git add"
    ]
  }
}
```

### 4. CI/CD 集成

在 `.github/workflows/ci.yml` 中添加：

```yaml
name: CI
on: [push, pull_request]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '14'
      - run: npm ci
      - run: npm run lint
```

## 自定义规则

### 1. 创建自定义规则

```javascript
// eslint-rules/no-console-log.js
module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'disallow console.log statements',
      category: 'Best Practices'
    },
    fixable: null,
    schema: []
  },
  create(context) {
    return {
      CallExpression(node) {
        if (
          node.callee.type === 'MemberExpression' &&
          node.callee.object.name === 'console' &&
          node.callee.property.name === 'log'
        ) {
          context.report({
            node,
            message: 'console.log is not allowed'
          })
        }
      }
    }
  }
}
```

### 2. 使用自定义规则

```javascript
// .eslintrc.js
module.exports = {
  plugins: ['./eslint-rules'],
  rules: {
    './eslint-rules/no-console-log': 'error'
  }
}
```

## 常见问题解决

### 1. 解析错误

```bash
# 检查配置
npx eslint --print-config src/main.js

# 调试模式
npx eslint --debug src/main.js
```

### 2. 性能优化

```javascript
// .eslintrc.js
module.exports = {
  // 缓存
  cache: true,
  cacheLocation: '.eslintcache',
  
  // 并行处理
  parallel: true
}
```

### 3. 规则冲突

```javascript
// 禁用特定规则
/* eslint-disable no-console */
console.log('debug info')
/* eslint-enable no-console */

// 单行禁用
console.log('debug') // eslint-disable-line no-console

// 下一行禁用
// eslint-disable-next-line no-console
console.log('debug')
```

## 最佳实践

### 1. 渐进式采用

- 从基础规则开始
- 逐步增加严格程度
- 团队讨论规则调整

### 2. 规则分类

```javascript
module.exports = {
  rules: {
    // 错误预防（必须）
    'no-undef': 'error',
    'no-unused-vars': 'error',
    
    // 代码风格（警告）
    'indent': 'warn',
    'quotes': 'warn',
    
    // 最佳实践（建议）
    'prefer-const': 'warn',
    'no-var': 'warn'
  }
}
```

### 3. 团队协作

- 统一配置文件
- 定期更新规则
- 文档化自定义规则
- 培训团队成员

通过合理配置 ESLint，可以显著提高代码质量，减少潜在错误，保持团队代码风格的一致性。
