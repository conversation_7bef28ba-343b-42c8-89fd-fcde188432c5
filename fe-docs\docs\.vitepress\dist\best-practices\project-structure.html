<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Vue项目结构最佳实践 | 前端技术开发文档</title>
    <meta name="description" content="A VitePress site">
    <link rel="preload stylesheet" href="/assets/style.5ccb9172.css" as="style">
    <script type="module" src="/assets/app.067a0132.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.2ed14f66.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="modulepreload" href="/assets/chunks/framework.3d729ebc.js">
  <link rel="modulepreload" href="/assets/chunks/theme.169415ae.js">
  <link rel="modulepreload" href="/assets/best-practices_project-structure.md.1dfc19f7.lean.js">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="apple-touch-icon" href="/logo.png">
  <meta name="theme-color" content="#0ea5e9">
  <script id="check-dark-light">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-21678b25 data-v-b2cf3e0b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-c8616af1></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-c8616af1> Skip to content </a><!--]--><!----><header class="VPNav" data-v-b2cf3e0b data-v-7e5bc4a5><div class="VPNavBar has-sidebar" data-v-7e5bc4a5 data-v-94c81dcc><div class="container" data-v-94c81dcc><div class="title" data-v-94c81dcc><div class="VPNavBarTitle has-sidebar" data-v-94c81dcc data-v-f4ef19a3><a class="title" href="/" data-v-f4ef19a3><!--[--><!--]--><!--[--><img class="VPImage logo" src="/logo.jpeg" alt data-v-6db2186b><!--]--><!--[-->前端技术开发文档<!--]--><!--[--><!--]--></a></div></div><div class="content" data-v-94c81dcc><div class="curtain" data-v-94c81dcc></div><div class="content-body" data-v-94c81dcc><!--[--><!--]--><div class="VPNavBarSearch search" style="--vp-meta-key:&#39;Meta&#39;;" data-v-94c81dcc><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><svg class="DocSearch-Search-Icon" width="20" height="20" viewBox="0 0 20 20" aria-label="search icon"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-94c81dcc data-v-7f418b0f><span id="main-nav-aria-label" class="visually-hidden" data-v-7f418b0f>Main Navigation</span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->首页<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/guide/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->指南<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/components/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->组件<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/best-practices/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->最佳实践<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/standards/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->规范标准<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/tools/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->工具配置<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/cesium/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->Cesium<!--]--><!----></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-94c81dcc data-v-f6a63727><label title="toggle dark mode" data-v-f6a63727 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div><!----><div class="VPFlyout VPNavBarExtra extra" data-v-94c81dcc data-v-40855f84 data-v-764effdf><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-764effdf><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="icon" data-v-764effdf><circle cx="12" cy="12" r="2"></circle><circle cx="19" cy="12" r="2"></circle><circle cx="5" cy="12" r="2"></circle></svg></button><div class="menu" data-v-764effdf><div class="VPMenu" data-v-764effdf data-v-e7ea1737><!----><!--[--><!--[--><!----><div class="group" data-v-40855f84><div class="item appearance" data-v-40855f84><p class="label" data-v-40855f84>Appearance</p><div class="appearance-action" data-v-40855f84><label title="toggle dark mode" data-v-40855f84 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div></div></div><!----><!--]--><!--]--></div></div></div><!--[--><!--[--><!--[--><div class="nav-color-picker" data-v-21678b25><div class="color-picker" data-v-21678b25 data-v-917787cc><button class="color-picker-trigger" title="打开色彩选择器" data-v-917787cc><div class="palette-icon" data-v-917787cc><div class="palette-circle" data-v-917787cc></div><div class="palette-colors" data-v-917787cc><div class="color-dot color-1" data-v-917787cc></div><div class="color-dot color-2" data-v-917787cc></div><div class="color-dot color-3" data-v-917787cc></div><div class="color-dot color-4" data-v-917787cc></div></div></div></button><!----><!----></div></div><!--]--><!--]--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-94c81dcc data-v-e5dd9c1c><span class="container" data-v-e5dd9c1c><span class="top" data-v-e5dd9c1c></span><span class="middle" data-v-e5dd9c1c></span><span class="bottom" data-v-e5dd9c1c></span></span></button></div></div></div></div><!----></header><div class="VPLocalNav" data-v-b2cf3e0b data-v-392e1bf8><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-392e1bf8><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="menu-icon" data-v-392e1bf8><path d="M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"></path><path d="M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"></path><path d="M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"></path><path d="M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"></path></svg><span class="menu-text" data-v-392e1bf8>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-392e1bf8 data-v-079b16a8><button data-v-079b16a8>Return to top</button><!----></div></div><aside class="VPSidebar" data-v-b2cf3e0b data-v-af16598e><div class="curtain" data-v-af16598e></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-af16598e><span class="visually-hidden" id="sidebar-aria-label" data-v-af16598e> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 has-active" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>最佳实践</h2><!----></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>概述</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/performance.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>性能优化</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/reuse.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>代码复用</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/state-management.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>状态管理</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/routing.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>路由管理</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/component-communication.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>组件通信</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/async-data.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>异步数据处理</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/modular-development.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>模块化开发</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/error-handling.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>错误处理</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/i18n.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>国际化实现</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/component-design.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Vue组件设计</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/vuex-best-practices.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Vuex最佳实践</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link is-active has-active" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/project-structure.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Vue项目结构</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/api-request.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>API请求封装</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/best-practices/utils.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>工具函数</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-b2cf3e0b data-v-a494bd1d><div class="VPDoc has-sidebar has-aside" data-v-a494bd1d data-v-c4b0d3cf><!--[--><!--]--><div class="container" data-v-c4b0d3cf><div class="aside" data-v-c4b0d3cf><div class="aside-curtain" data-v-c4b0d3cf></div><div class="aside-container" data-v-c4b0d3cf><div class="aside-content" data-v-c4b0d3cf><div class="VPDocAside" data-v-c4b0d3cf data-v-3f215769><!--[--><!--]--><!--[--><!--]--><div class="VPDocAsideOutline" data-v-3f215769 data-v-ff0f39c8><div class="content" data-v-ff0f39c8><div class="outline-marker" data-v-ff0f39c8></div><div class="outline-title" data-v-ff0f39c8>On this page</div><nav aria-labelledby="doc-outline-aria-label" data-v-ff0f39c8><span class="visually-hidden" id="doc-outline-aria-label" data-v-ff0f39c8> Table of Contents for current page </span><ul class="root" data-v-ff0f39c8 data-v-9a431c33><!--[--><!--]--></ul></nav></div></div><!--[--><!--]--><div class="spacer" data-v-3f215769></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-c4b0d3cf><div class="content-container" data-v-c4b0d3cf><!--[--><!--]--><!----><main class="main" data-v-c4b0d3cf><div style="position:relative;" class="vp-doc _best-practices_project-structure" data-v-c4b0d3cf><div><h1 id="vue项目结构最佳实践" tabindex="-1">Vue项目结构最佳实践 <a class="header-anchor" href="#vue项目结构最佳实践" aria-label="Permalink to &quot;Vue项目结构最佳实践&quot;">​</a></h1><p>一个良好的项目结构是构建可维护和可扩展Vue应用的基础。本文档将介绍Vue项目的目录结构组织最佳实践，适用于中大型Vue.js应用。</p><h2 id="目录" tabindex="-1">目录 <a class="header-anchor" href="#目录" aria-label="Permalink to &quot;目录&quot;">​</a></h2><nav class="table-of-contents"><ul><li><a href="#目录">目录</a></li><li><a href="#基础项目结构">基础项目结构</a><ul><li><a href="#标准vue-cli生成的项目结构">标准Vue CLI生成的项目结构</a></li></ul></li><li><a href="#中大型项目推荐结构">中大型项目推荐结构</a></li><li><a href="#关键目录详解">关键目录详解</a><ul><li><a href="#src-api">src/api</a></li><li><a href="#src-components">src/components</a></li><li><a href="#src-views">src/views</a></li><li><a href="#src-router">src/router</a></li><li><a href="#src-store">src/store</a></li><li><a href="#src-utils">src/utils</a></li></ul></li><li><a href="#高级项目结构">高级项目结构</a><ul><li><a href="#特性驱动的目录结构">特性驱动的目录结构</a></li><li><a href="#微前端架构">微前端架构</a></li></ul></li><li><a href="#多环境配置">多环境配置</a><ul><li><a href="#环境变量文件">环境变量文件</a></li></ul></li></ul></nav><h2 id="基础项目结构" tabindex="-1">基础项目结构 <a class="header-anchor" href="#基础项目结构" aria-label="Permalink to &quot;基础项目结构&quot;">​</a></h2><h3 id="标准vue-cli生成的项目结构" tabindex="-1">标准Vue CLI生成的项目结构 <a class="header-anchor" href="#标准vue-cli生成的项目结构" aria-label="Permalink to &quot;标准Vue CLI生成的项目结构&quot;">​</a></h3><p>使用Vue CLI创建的项目默认结构如下：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">project-root/</span></span>
<span class="line"><span style="color:#babed8;">├── public/                 # 静态资源目录</span></span>
<span class="line"><span style="color:#babed8;">│   ├── favicon.ico         # 网站图标</span></span>
<span class="line"><span style="color:#babed8;">│   └── index.html          # 主HTML模板</span></span>
<span class="line"><span style="color:#babed8;">├── src/                    # 源代码目录</span></span>
<span class="line"><span style="color:#babed8;">│   ├── assets/             # 资源文件(图片、字体等)</span></span>
<span class="line"><span style="color:#babed8;">│   ├── components/         # 全局组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── views/              # 页面组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── App.vue             # 根组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── main.js             # 入口文件</span></span>
<span class="line"><span style="color:#babed8;">│   └── router.js           # 路由配置</span></span>
<span class="line"><span style="color:#babed8;">├── .gitignore              # Git忽略文件</span></span>
<span class="line"><span style="color:#babed8;">├── babel.config.js         # Babel配置</span></span>
<span class="line"><span style="color:#babed8;">├── package.json            # 项目依赖和脚本</span></span>
<span class="line"><span style="color:#babed8;">├── README.md               # 项目说明</span></span>
<span class="line"><span style="color:#babed8;">└── vue.config.js           # Vue CLI配置文件</span></span></code></pre></div><p>这个结构适合小型项目，但对于中大型项目，我们需要更细致的组织。</p><h2 id="中大型项目推荐结构" tabindex="-1">中大型项目推荐结构 <a class="header-anchor" href="#中大型项目推荐结构" aria-label="Permalink to &quot;中大型项目推荐结构&quot;">​</a></h2><p>对于中大型项目，推荐以下目录结构：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">project-root/</span></span>
<span class="line"><span style="color:#babed8;">├── public/                 # 静态资源目录</span></span>
<span class="line"><span style="color:#babed8;">│   ├── favicon.ico         # 网站图标</span></span>
<span class="line"><span style="color:#babed8;">│   └── index.html          # 主HTML模板</span></span>
<span class="line"><span style="color:#babed8;">├── src/                    # 源代码目录</span></span>
<span class="line"><span style="color:#babed8;">│   ├── api/                # API请求模块</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # API导出</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── user.js         # 用户相关API</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── product.js      # 产品相关API</span></span>
<span class="line"><span style="color:#babed8;">│   ├── assets/             # 资源文件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── images/         # 图片资源</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── styles/         # 全局样式</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── fonts/          # 字体资源</span></span>
<span class="line"><span style="color:#babed8;">│   ├── components/         # 全局通用组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── common/         # 基础UI组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── business/       # 业务相关组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── constants/          # 常量定义</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # 导出文件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── api.js          # API相关常量</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── enum.js         # 枚举常量</span></span>
<span class="line"><span style="color:#babed8;">│   ├── directives/         # 全局指令</span></span>
<span class="line"><span style="color:#babed8;">│   ├── filters/            # 全局过滤器</span></span>
<span class="line"><span style="color:#babed8;">│   ├── icons/              # SVG图标</span></span>
<span class="line"><span style="color:#babed8;">│   ├── layout/             # 布局组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── components/     # 布局子组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── index.vue       # 主布局组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── mixins/             # 全局混入</span></span>
<span class="line"><span style="color:#babed8;">│   ├── plugins/            # 插件配置</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # 插件导出</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── element.js      # Element UI配置</span></span>
<span class="line"><span style="color:#babed8;">│   ├── router/             # 路由配置</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # 路由导出</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── routes.js       # 路由定义</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── guards.js       # 路由守卫</span></span>
<span class="line"><span style="color:#babed8;">│   ├── services/           # 服务层</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── http.js         # HTTP请求服务</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── auth.js         # 认证服务</span></span>
<span class="line"><span style="color:#babed8;">│   ├── store/              # Vuex状态管理</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # Store导出</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── getters.js      # 全局Getters</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── modules/        # Store模块</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── user.js     # 用户模块</span></span>
<span class="line"><span style="color:#babed8;">│   │       └── app.js      # 应用模块</span></span>
<span class="line"><span style="color:#babed8;">│   ├── utils/              # 工具函数</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.js        # 工具导出</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── request.js      # 请求工具</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── storage.js      # 存储工具</span></span>
<span class="line"><span style="color:#babed8;">│   ├── views/              # 页面组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── home/           # 首页模块</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── index.vue   # 首页</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   └── components/ # 首页专用组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── user/           # 用户模块</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   ├── login.vue   # 登录页</span></span>
<span class="line"><span style="color:#babed8;">│   │   │   └── profile.vue # 个人资料页</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── error/          # 错误页</span></span>
<span class="line"><span style="color:#babed8;">│   │       ├── 404.vue     # 404页面</span></span>
<span class="line"><span style="color:#babed8;">│   │       └── 500.vue     # 500页面</span></span>
<span class="line"><span style="color:#babed8;">│   ├── App.vue             # 根组件</span></span>
<span class="line"><span style="color:#babed8;">│   └── main.js             # 入口文件</span></span>
<span class="line"><span style="color:#babed8;">├── .env                    # 环境变量</span></span>
<span class="line"><span style="color:#babed8;">├── .env.development        # 开发环境变量</span></span>
<span class="line"><span style="color:#babed8;">├── .env.production         # 生产环境变量</span></span>
<span class="line"><span style="color:#babed8;">├── .eslintrc.js            # ESLint配置</span></span>
<span class="line"><span style="color:#babed8;">├── .gitignore              # Git忽略文件</span></span>
<span class="line"><span style="color:#babed8;">├── babel.config.js         # Babel配置</span></span>
<span class="line"><span style="color:#babed8;">├── package.json            # 项目依赖和脚本</span></span>
<span class="line"><span style="color:#babed8;">├── README.md               # 项目说明</span></span>
<span class="line"><span style="color:#babed8;">└── vue.config.js           # Vue CLI配置文件</span></span></code></pre></div><h2 id="关键目录详解" tabindex="-1">关键目录详解 <a class="header-anchor" href="#关键目录详解" aria-label="Permalink to &quot;关键目录详解&quot;">​</a></h2><h3 id="src-api" tabindex="-1">src/api <a class="header-anchor" href="#src-api" aria-label="Permalink to &quot;src/api&quot;">​</a></h3><p>API目录负责处理所有与后端API的通信：</p><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/api/user.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> request </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/utils/request</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">login</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/user/login</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">post</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">data</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">getUserInfo</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">token</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/user/info</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">get</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    params</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">token</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">logout</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">request</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    url</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/user/logout</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    method</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">post</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/api/index.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">*</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">as</span><span style="color:#BABED8;"> user </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">./user</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">*</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">as</span><span style="color:#BABED8;"> product </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">./product</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  user</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  product</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span></code></pre></div><h3 id="src-components" tabindex="-1">src/components <a class="header-anchor" href="#src-components" aria-label="Permalink to &quot;src/components&quot;">​</a></h3><p>组件目录包含可在整个应用中重用的组件：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">components/</span></span>
<span class="line"><span style="color:#babed8;">├── common/          # 基础UI组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── Button/</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── index.vue</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── style.scss</span></span>
<span class="line"><span style="color:#babed8;">│   ├── Input/</span></span>
<span class="line"><span style="color:#babed8;">│   └── Modal/</span></span>
<span class="line"><span style="color:#babed8;">└── business/        # 业务组件</span></span>
<span class="line"><span style="color:#babed8;">    ├── UserList/</span></span>
<span class="line"><span style="color:#babed8;">    ├── ProductCard/</span></span>
<span class="line"><span style="color:#babed8;">    └── OrderForm/</span></span></code></pre></div><p>每个组件可以是单个文件组件(SFC)，也可以是包含多个文件的目录。对于复杂组件，推荐使用目录结构：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">Button/</span></span>
<span class="line"><span style="color:#babed8;">├── index.vue        # 主组件文件</span></span>
<span class="line"><span style="color:#babed8;">├── style.scss       # 组件样式</span></span>
<span class="line"><span style="color:#babed8;">├── types.js         # 类型定义</span></span>
<span class="line"><span style="color:#babed8;">└── utils.js         # 组件工具函数</span></span></code></pre></div><h3 id="src-views" tabindex="-1">src/views <a class="header-anchor" href="#src-views" aria-label="Permalink to &quot;src/views&quot;">​</a></h3><p>视图目录包含应用的页面组件，通常与路由对应：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">views/</span></span>
<span class="line"><span style="color:#babed8;">├── home/</span></span>
<span class="line"><span style="color:#babed8;">│   ├── index.vue            # 首页</span></span>
<span class="line"><span style="color:#babed8;">│   └── components/          # 首页专用组件</span></span>
<span class="line"><span style="color:#babed8;">│       ├── Banner.vue</span></span>
<span class="line"><span style="color:#babed8;">│       └── Featured.vue</span></span>
<span class="line"><span style="color:#babed8;">├── user/</span></span>
<span class="line"><span style="color:#babed8;">│   ├── login.vue            # 登录页</span></span>
<span class="line"><span style="color:#babed8;">│   ├── register.vue         # 注册页</span></span>
<span class="line"><span style="color:#babed8;">│   └── components/          # 用户模块专用组件</span></span>
<span class="line"><span style="color:#babed8;">│       └── UserForm.vue</span></span>
<span class="line"><span style="color:#babed8;">└── product/</span></span>
<span class="line"><span style="color:#babed8;">    ├── list.vue             # 产品列表页</span></span>
<span class="line"><span style="color:#babed8;">    ├── detail.vue           # 产品详情页</span></span>
<span class="line"><span style="color:#babed8;">    └── components/          # 产品模块专用组件</span></span>
<span class="line"><span style="color:#babed8;">        ├── ProductFilter.vue</span></span>
<span class="line"><span style="color:#babed8;">        └── ProductDetail.vue</span></span></code></pre></div><p>对于特定页面才使用的组件，应放在对应页面的components目录下，而不是全局components目录。</p><h3 id="src-router" tabindex="-1">src/router <a class="header-anchor" href="#src-router" aria-label="Permalink to &quot;src/router&quot;">​</a></h3><p>路由目录包含路由相关配置：</p><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/router/routes.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> Layout </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/layout</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> constantRoutes </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/login</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/user/login</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">hidden</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/404</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/error/404</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">hidden</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Layout</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">redirect</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/dashboard</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">children</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">dashboard</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/dashboard/index</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Dashboard</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">title</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">首页</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">icon</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">dashboard</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> asyncRoutes </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/user</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Layout</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">title</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">用户管理</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">icon</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">user</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">roles</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">admin</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">] </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">children</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">list</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/user/list</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">UserList</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">title</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">用户列表</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">path</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">create</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#82AAFF;">component</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">import</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/views/user/create</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">UserCreate</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#F07178;">meta</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">title</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">创建用户</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">;</span></span></code></pre></div><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/router/index.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> Vue </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> Router </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">vue-router</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">constantRoutes</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">./routes</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#BABED8;">Vue</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">use</span><span style="color:#BABED8;">(Router)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> createRouter </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">Router</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mode</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">history</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">scrollBehavior</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> (</span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">y</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">routes</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> constantRoutes</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> router </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">createRouter</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 重置路由方法</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">resetRouter</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">newRouter</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">createRouter</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">router</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">matcher</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">newRouter</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">matcher</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> router</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="src-store" tabindex="-1">src/store <a class="header-anchor" href="#src-store" aria-label="Permalink to &quot;src/store&quot;">​</a></h3><p>Vuex状态管理目录：</p><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/store/modules/user.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">login</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">logout</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">getUserInfo</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/api/user</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">getToken</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">setToken</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">removeToken</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/utils/auth</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> state </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">token</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">getToken</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">name</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">avatar</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">roles</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> []</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> mutations </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">SET_TOKEN</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">token</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">token</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">token</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">SET_NAME</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">name</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">name</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">name</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">SET_AVATAR</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">avatar</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">avatar</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">avatar</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">SET_ROLES</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">roles</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">roles</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">roles</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> actions </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 用户登录</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">login</span><span style="color:#89DDFF;">({</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">commit</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">userInfo</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">username</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">password</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">userInfo</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">resolve</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;font-style:italic;">reject</span><span style="color:#89DDFF;">)</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#82AAFF;">login</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> username</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">username</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">trim</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">password</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">then</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">response</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">response</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">commit</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SET_TOKEN</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">token</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">setToken</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">token</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">resolve</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">catch</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">error</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 获取用户信息</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">getInfo</span><span style="color:#89DDFF;">({</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">commit</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">})</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">resolve</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;font-style:italic;">reject</span><span style="color:#89DDFF;">)</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#82AAFF;">getUserInfo</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">token</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">then</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">response</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">response</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;">data</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">            </span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">验证失败，请重新登录</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">          </span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">roles</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">name</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">avatar</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;">roles</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">||</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">roles</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&lt;=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">            </span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">用户角色必须是非空数组!</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">          </span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">commit</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SET_ROLES</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">roles</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">commit</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SET_NAME</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">name</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">commit</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SET_AVATAR</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">avatar</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">resolve</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">data</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">catch</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">error</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 用户登出</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">logout</span><span style="color:#89DDFF;">({</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">commit</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">state</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">dispatch</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">})</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">resolve</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;font-style:italic;">reject</span><span style="color:#89DDFF;">)</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#82AAFF;">logout</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">state</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">token</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">then</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">commit</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SET_TOKEN</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">commit</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">SET_ROLES</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> [])</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">removeToken</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span></span>
<span class="line"><span style="color:#89DDFF;">          </span><span style="color:#676E95;font-style:italic;">// 重置路由</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">resetRouter</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">          </span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">resolve</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">catch</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">error</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">namespaced</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  state</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  mutations</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  actions</span></span>
<span class="line"><span style="color:#89DDFF;">};</span></span></code></pre></div><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/store/index.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> Vue </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> Vuex </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">vuex</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> getters </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">./getters</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> app </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">./modules/app</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> user </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">./modules/user</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#BABED8;">Vue</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">use</span><span style="color:#BABED8;">(Vuex)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> store </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Vuex</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Store</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">modules</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    app</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    user</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  getters</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> store</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="src-utils" tabindex="-1">src/utils <a class="header-anchor" href="#src-utils" aria-label="Permalink to &quot;src/utils&quot;">​</a></h3><p>工具函数目录：</p><div class="language-js"><button title="Copy Code" class="copy"></button><span class="lang">js</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// src/utils/request.js</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> axios </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">axios</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> store </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/store</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">getToken</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@/utils/auth</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Message</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">element-ui</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 创建axios实例</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> service </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> axios</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">create</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">baseURL</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> process</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">env</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">VUE_APP_BASE_API</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">timeout</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5000</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 请求拦截器</span></span>
<span class="line"><span style="color:#BABED8;">service</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">interceptors</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">request</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">use</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#BABED8;font-style:italic;">config</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">store</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">getters</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">token</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">headers</span><span style="color:#F07178;">[</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Authorization</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">] </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">Bearer </span><span style="color:#89DDFF;">${</span><span style="color:#82AAFF;">getToken</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">}`</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">config</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#BABED8;font-style:italic;">error</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 响应拦截器</span></span>
<span class="line"><span style="color:#BABED8;">service</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">interceptors</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">response</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">use</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#BABED8;font-style:italic;">response</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">res</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">response</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 根据自定义错误码处理错误</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">res</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">code</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">!==</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">200</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#82AAFF;">Message</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        message</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">res</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">message</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">||</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">请求错误</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        type</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">error</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        duration</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">5</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1000</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 处理特定错误（例如认证错误）</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">res</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">code</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">===</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">401</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#BABED8;">store</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">dispatch</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">user/logout</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">then</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#BABED8;">location</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">reload</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Error</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">res</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">message</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">||</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">请求错误</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">))</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">else</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">res</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#BABED8;font-style:italic;">error</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">请求错误:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#82AAFF;">Message</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      message</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">error</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">message</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">||</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">请求错误</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      type</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">error</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      duration</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">5</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1000</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">reject</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">export</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">default</span><span style="color:#BABED8;"> service</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="高级项目结构" tabindex="-1">高级项目结构 <a class="header-anchor" href="#高级项目结构" aria-label="Permalink to &quot;高级项目结构&quot;">​</a></h2><h3 id="特性驱动的目录结构" tabindex="-1">特性驱动的目录结构 <a class="header-anchor" href="#特性驱动的目录结构" aria-label="Permalink to &quot;特性驱动的目录结构&quot;">​</a></h3><p>对于非常大型的项目，可以考虑按特性/业务模块组织代码，而不是按技术类型：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">src/</span></span>
<span class="line"><span style="color:#babed8;">├── core/                # 核心模块</span></span>
<span class="line"><span style="color:#babed8;">│   ├── api/             # 核心API</span></span>
<span class="line"><span style="color:#babed8;">│   ├── components/      # 核心组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── utils/           # 核心工具</span></span>
<span class="line"><span style="color:#babed8;">│   └── store/           # 核心状态</span></span>
<span class="line"><span style="color:#babed8;">├── features/            # 业务特性</span></span>
<span class="line"><span style="color:#babed8;">│   ├── authentication/  # 认证模块</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── api/         # 认证API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── components/  # 认证组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── store/       # 认证状态</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── views/       # 认证页面</span></span>
<span class="line"><span style="color:#babed8;">│   ├── products/        # 产品模块</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── api/         # 产品API</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── components/  # 产品组件</span></span>
<span class="line"><span style="color:#babed8;">│   │   ├── store/       # 产品状态</span></span>
<span class="line"><span style="color:#babed8;">│   │   └── views/       # 产品页面</span></span>
<span class="line"><span style="color:#babed8;">│   └── users/           # 用户模块</span></span>
<span class="line"><span style="color:#babed8;">│       ├── api/         # 用户API</span></span>
<span class="line"><span style="color:#babed8;">│       ├── components/  # 用户组件</span></span>
<span class="line"><span style="color:#babed8;">│       ├── store/       # 用户状态</span></span>
<span class="line"><span style="color:#babed8;">│       └── views/       # 用户页面</span></span>
<span class="line"><span style="color:#babed8;">└── shared/              # 共享资源</span></span>
<span class="line"><span style="color:#babed8;">    ├── assets/          # 共享资产</span></span>
<span class="line"><span style="color:#babed8;">    ├── styles/          # 共享样式</span></span>
<span class="line"><span style="color:#babed8;">    └── constants/       # 共享常量</span></span></code></pre></div><p>这种结构的优点是相关代码放在一起，便于团队协作和功能开发。</p><h3 id="微前端架构" tabindex="-1">微前端架构 <a class="header-anchor" href="#微前端架构" aria-label="Permalink to &quot;微前端架构&quot;">​</a></h3><p>对于特别复杂的应用，可以考虑微前端架构：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">project-root/</span></span>
<span class="line"><span style="color:#babed8;">├── packages/             # 子应用包</span></span>
<span class="line"><span style="color:#babed8;">│   ├── main/             # 主应用</span></span>
<span class="line"><span style="color:#babed8;">│   ├── user-center/      # 用户中心子应用</span></span>
<span class="line"><span style="color:#babed8;">│   ├── product-manager/  # 产品管理子应用</span></span>
<span class="line"><span style="color:#babed8;">│   └── dashboard/        # 仪表盘子应用</span></span>
<span class="line"><span style="color:#babed8;">├── shared/               # 共享库</span></span>
<span class="line"><span style="color:#babed8;">│   ├── components/       # 共享组件</span></span>
<span class="line"><span style="color:#babed8;">│   ├── utils/            # 共享工具</span></span>
<span class="line"><span style="color:#babed8;">│   └── styles/           # 共享样式</span></span>
<span class="line"><span style="color:#babed8;">└── platform/             # 平台配置</span></span>
<span class="line"><span style="color:#babed8;">    ├── config/           # 全局配置</span></span>
<span class="line"><span style="color:#babed8;">    └── scripts/          # 构建脚本</span></span></code></pre></div><p>每个子应用可以有自己的Vue项目结构，并独立开发和部署。</p><h2 id="多环境配置" tabindex="-1">多环境配置 <a class="header-anchor" href="#多环境配置" aria-label="Permalink to &quot;多环境配置&quot;">​</a></h2><h3 id="环境变量文件" tabindex="-1">环境变量文件 <a class="header-anchor" href="#环境变量文件" aria-label="Permalink to &quot;环境变量文件&quot;">​</a></h3><p>使用多个.env文件配置不同环境：</p><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;"># .env                # 所有环境</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_TITLE=我的应用</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;"># .env.development    # 开发环境</span></span>
<span class="line"><span style="color:#babed8;">NODE_ENV=development</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_BASE_API=/dev-api</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_MOCK=true</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;"># .env.staging        # 预发布环境</span></span>
<span class="line"><span style="color:#babed8;">NODE_ENV=production</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_BASE_API=https://staging-api.example.com</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_MOCK=false</span></span>
<span class="line"><span style="color:#babed8;"></span></span>
<span class="line"><span style="color:#babed8;"># .env.production     # 生产环境</span></span>
<span class="line"><span style="color:#babed8;">NODE_ENV=production</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_BASE_API=https://api.example.com</span></span>
<span class="line"><span style="color:#babed8;">VUE_APP_MOCK=false</span></span></code></pre></div></div></div></main><footer class="VPDocFooter" data-v-c4b0d3cf data-v-face870a><!--[--><!--]--><!----><div class="prev-next" data-v-face870a><div class="pager" data-v-face870a><a class="pager-link prev" href="/best-practices/vuex-best-practices.html" data-v-face870a><span class="desc" data-v-face870a>Previous page</span><span class="title" data-v-face870a>Vuex最佳实践</span></a></div><div class="has-prev pager" data-v-face870a><a class="pager-link next" href="/best-practices/api-request.html" data-v-face870a><span class="desc" data-v-face870a>Next page</span><span class="title" data-v-face870a>API请求封装</span></a></div></div></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-b2cf3e0b data-v-2f86ebd2><div class="container" data-v-2f86ebd2><!----><p class="copyright" data-v-2f86ebd2>Copyright © 2025 智洋上水</p></div></footer><!--[--><!--]--></div></div>
    <script>__VP_HASH_MAP__ = JSON.parse("{\"tools_index.md\":\"6738f00c\",\"best-practices_index.md\":\"38aee9d5\",\"components_directives_copy.md\":\"1f572838\",\"best-practices_charts.md\":\"3ff7afe8\",\"components_directives_drag-dialog.md\":\"fc009f97\",\"tools_vscode.md\":\"71a1eb8e\",\"best-practices_performance.md\":\"495f9c35\",\"cesium_operations.md\":\"608310c0\",\"components_directives_permission.md\":\"72d28b46\",\"best-practices_reuse.md\":\"d4c27921\",\"components_business_input-number.md\":\"bac6c7af\",\"cesium_examples_index.md\":\"6b2ba648\",\"tools_debugging.md\":\"506e7f52\",\"components_directives_table-height.md\":\"1acd3698\",\"standards_code-review.md\":\"04c78cff\",\"best-practices_api-request.md\":\"32a5c3df\",\"best-practices_component-design.md\":\"d570a27f\",\"standards_git-workflow.md\":\"a7004f93\",\"standards_index.md\":\"e5fe1d60\",\"index.md\":\"d11c82a3\",\"standards_documentation.md\":\"4ebedf27\",\"components_business_map-visualization.md\":\"0aa7826b\",\"components_business_custom-file-upload.md\":\"6c36e06b\",\"components_screen.md\":\"1c0d4883\",\"best-practices_utils.md\":\"08da2e9d\",\"cesium_examples_water-monitor.md\":\"432751c3\",\"best-practices_vuex-best-practices.md\":\"42c910e9\",\"best-practices_error-handling.md\":\"37d5b8d1\",\"best-practices_modular-development.md\":\"ce72d502\",\"components_business_dict-select.md\":\"f5401956\",\"best-practices_routing.md\":\"a83fde5f\",\"cesium_index.md\":\"b9eb6f3c\",\"components_directives_debounce.md\":\"6c3b5296\",\"components_form.md\":\"ee77edf0\",\"cesium_concepts.md\":\"516ce30e\",\"tools_husky.md\":\"586bda1d\",\"standards_css-standard.md\":\"43da9895\",\"components_business_common-dialog-box.md\":\"14a05c89\",\"best-practices_async-data.md\":\"d6f4dbd8\",\"best-practices_component-communication.md\":\"da381d67\",\"best-practices_project-structure.md\":\"1dfc19f7\",\"components_directives_throttle.md\":\"b30fc5ee\",\"components_charts.md\":\"bcc5267b\",\"components_business_file-preview.md\":\"4379efa6\",\"components_business_input-word.md\":\"82e26484\",\"components_business.md\":\"********\",\"cesium_basics.md\":\"18bfba98\",\"components_business_image-upload.md\":\"b6affc47\",\"components_index.md\":\"06a93849\",\"best-practices_state-management.md\":\"6cde5f2d\",\"standards_git-commit.md\":\"4953e1ed\",\"components_directives_loading.md\":\"0bd89f42\",\"standards_js-standard.md\":\"e5f97f25\",\"guide_admin-development.md\":\"b638fd3e\",\"components_directives_index.md\":\"e6ec9d5d\",\"standards_vue-standard.md\":\"204374d5\",\"components_business_dict-tag.md\":\"7c2d9c5e\",\"tools_prettier.md\":\"dc0ae721\",\"guide_index.md\":\"a218fcb9\",\"tools_eslint.md\":\"eea2bb46\",\"guide_development-process.md\":\"7d0d3459\",\"standards_html-standard.md\":\"87257590\",\"guide_project-structure.md\":\"83547606\",\"best-practices_i18n.md\":\"83c49ca7\",\"tools_package-manager.md\":\"7c6faea2\",\"components_table.md\":\"d1c3a5a7\"}")
__VP_SITE_DATA__ = JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"前端技术开发文档\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"appearance\":true,\"themeConfig\":{\"logo\":\"/logo.jpeg\",\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"指南\",\"link\":\"/guide/\"},{\"text\":\"组件\",\"link\":\"/components/\"},{\"text\":\"最佳实践\",\"link\":\"/best-practices/\"},{\"text\":\"规范标准\",\"link\":\"/standards/\"},{\"text\":\"工具配置\",\"link\":\"/tools/\"},{\"text\":\"Cesium\",\"link\":\"/cesium/\"}],\"search\":{\"provider\":\"local\",\"options\":{\"translations\":{\"button\":{\"buttonText\":\"搜索文档\",\"buttonAriaLabel\":\"搜索文档\"},\"modal\":{\"noResultsText\":\"无法找到相关结果\",\"resetButtonTitle\":\"清除查询条件\",\"footer\":{\"selectText\":\"选择\",\"navigateText\":\"切换\",\"closeText\":\"关闭\"}}}}},\"sidebar\":{\"/guide/\":[{\"text\":\"开发指南\",\"items\":[{\"text\":\"快速开始\",\"link\":\"/guide/\"},{\"text\":\"项目结构\",\"link\":\"/guide/project-structure\"},{\"text\":\"开发流程\",\"link\":\"/guide/development-process\"},{\"text\":\"后台管理开发\",\"link\":\"/guide/admin-development\"}]}],\"/components/\":[{\"text\":\"组件库\",\"items\":[{\"text\":\"组件概览\",\"link\":\"/components/\"},{\"text\":\"业务组件\",\"collapsed\":false,\"items\":[{\"text\":\"业务组件总览\",\"link\":\"/components/business\"},{\"text\":\"字典标签组件\",\"link\":\"/components/business/dict-tag\"},{\"text\":\"字典选择器\",\"link\":\"/components/business/dict-select\"},{\"text\":\"自定义文件上传\",\"link\":\"/components/business/custom-file-upload\"},{\"text\":\"图片上传组件\",\"link\":\"/components/business/image-upload\"},{\"text\":\"自定义数字输入框\",\"link\":\"/components/business/input-number\"},{\"text\":\"自定义文本输入框\",\"link\":\"/components/business/input-word\"},{\"text\":\"文件预览组件\",\"link\":\"/components/business/file-preview\"},{\"text\":\"地图可视化组件\",\"link\":\"/components/business/map-visualization\"}]},{\"text\":\"表单组件\",\"link\":\"/components/form\"},{\"text\":\"表格组件\",\"link\":\"/components/table\"},{\"text\":\"图表组件\",\"link\":\"/components/charts\"},{\"text\":\"全局指令\",\"collapsed\":false,\"items\":[{\"text\":\"指令概览\",\"link\":\"/components/directives/index\"},{\"text\":\"表格高度\",\"link\":\"/components/directives/table-height\"},{\"text\":\"权限控制\",\"link\":\"/components/directives/permission\"},{\"text\":\"弹窗拖拽\",\"link\":\"/components/directives/drag-dialog\"},{\"text\":\"防抖处理\",\"link\":\"/components/directives/debounce\"},{\"text\":\"节流处理\",\"link\":\"/components/directives/throttle\"},{\"text\":\"一键复制\",\"link\":\"/components/directives/copy\"}]},{\"text\":\"大屏开发\",\"link\":\"/components/screen\"}]}],\"/best-practices/\":[{\"text\":\"最佳实践\",\"items\":[{\"text\":\"概述\",\"link\":\"/best-practices/\"},{\"text\":\"性能优化\",\"link\":\"/best-practices/performance\"},{\"text\":\"代码复用\",\"link\":\"/best-practices/reuse\"},{\"text\":\"状态管理\",\"link\":\"/best-practices/state-management\"},{\"text\":\"路由管理\",\"link\":\"/best-practices/routing\"},{\"text\":\"组件通信\",\"link\":\"/best-practices/component-communication\"},{\"text\":\"异步数据处理\",\"link\":\"/best-practices/async-data\"},{\"text\":\"模块化开发\",\"link\":\"/best-practices/modular-development\"},{\"text\":\"错误处理\",\"link\":\"/best-practices/error-handling\"},{\"text\":\"国际化实现\",\"link\":\"/best-practices/i18n\"},{\"text\":\"Vue组件设计\",\"link\":\"/best-practices/component-design\"},{\"text\":\"Vuex最佳实践\",\"link\":\"/best-practices/vuex-best-practices\"},{\"text\":\"Vue项目结构\",\"link\":\"/best-practices/project-structure\"},{\"text\":\"API请求封装\",\"link\":\"/best-practices/api-request\"},{\"text\":\"工具函数\",\"link\":\"/best-practices/utils\"}]}],\"/standards/\":[{\"text\":\"规范标准\",\"items\":[{\"text\":\"规范概述\",\"link\":\"/standards/\"},{\"text\":\"编码规范\",\"collapsed\":false,\"items\":[{\"text\":\"JavaScript规范\",\"link\":\"/standards/js-standard\"},{\"text\":\"CSS规范\",\"link\":\"/standards/css-standard\"},{\"text\":\"HTML规范\",\"link\":\"/standards/html-standard\"},{\"text\":\"Vue开发规范\",\"link\":\"/standards/vue-standard\"}]},{\"text\":\"Git提交规范\",\"link\":\"/standards/git-commit\"},{\"text\":\"Git工作流\",\"link\":\"/standards/git-workflow\"},{\"text\":\"代码审查\",\"link\":\"/standards/code-review\"},{\"text\":\"文档规范\",\"link\":\"/standards/documentation\"}]}],\"/tools/\":[{\"text\":\"开发工具配置\",\"items\":[{\"text\":\"工具概览\",\"link\":\"/tools/\"},{\"text\":\"VS Code配置\",\"link\":\"/tools/vscode\"},{\"text\":\"调试工具\",\"link\":\"/tools/debugging\"},{\"text\":\"包管理工具\",\"link\":\"/tools/package-manager\"}]},{\"text\":\"代码质量工具\",\"collapsed\":false,\"items\":[{\"text\":\"ESLint配置\",\"link\":\"/tools/eslint\"},{\"text\":\"Prettier配置\",\"link\":\"/tools/prettier\"},{\"text\":\"Husky配置\",\"link\":\"/tools/husky\"}]}],\"/cesium/\":[{\"text\":\"Cesium 3D地图引擎\",\"items\":[{\"text\":\"简介\",\"link\":\"/cesium/\"},{\"text\":\"基础配置\",\"link\":\"/cesium/basics\"},{\"text\":\"核心概念\",\"link\":\"/cesium/concepts\"},{\"text\":\"常用操作\",\"link\":\"/cesium/operations\"}]},{\"text\":\"实战案例\",\"collapsed\":false,\"items\":[{\"text\":\"案例概览\",\"link\":\"/cesium/examples/\"},{\"text\":\"智慧水务监控\",\"link\":\"/cesium/examples/water-monitor\"}]}]},\"footer\":{\"copyright\":\"Copyright © 2025 智洋上水\"}},\"locales\":{},\"scrollOffset\":90,\"cleanUrls\":false}")</script>
    
  </body>
</html>