# 异步数据处理

本文档提供Vue.js项目中异步数据处理的最佳实践，帮助开发者有效处理API请求和异步操作。

## 异步数据处理概述

在前端应用中，异步数据处理主要包括：

- API请求和响应处理
- 数据加载状态管理
- 错误处理与重试
- 并发请求控制
- 请求缓存与数据持久化

合理的异步数据处理策略能够提升用户体验、减少不必要的网络请求并简化代码逻辑。

## 基础异步处理模式

### Promise链式处理

```js
// 基础Promise用法
fetchUserData(userId)
  .then(userData => {
    this.userData = userData
    return fetchUserPosts(userId)
  })
  .then(posts => {
    this.userPosts = posts
  })
  .catch(error => {
    this.error = error.message
  })
  .finally(() => {
    this.loading = false
  })
```

### Async/Await模式

```js
// 推荐使用async/await
async fetchUserData() {
  this.loading = true
  this.error = null
  
  try {
    this.userData = await api.fetchUserData(this.userId)
    this.userPosts = await api.fetchUserPosts(this.userId)
  } catch (error) {
    this.error = error.message
    console.error('Failed to fetch user data:', error)
  } finally {
    this.loading = false
  }
}
```

## 组件中的异步数据

### 生命周期钩子中加载数据

```vue
<template>
  <div>
    <div v-if="loading" class="loading">加载中...</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <div v-else>
      <h1>{{ user.name }}</h1>
      <p>{{ user.email }}</p>
    </div>
  </div>
</template>

<script>
import api from '@/api'

export default {
  data() {
    return {
      user: null,
      loading: true,
      error: null
    }
  },
  async created() {
    try {
      this.user = await api.getUser(this.$route.params.id)
    } catch (error) {
      this.error = '无法加载用户数据'
      console.error(error)
    } finally {
      this.loading = false
    }
  }
}
</script>
```

### 路由导航守卫中预加载数据

```js
// router.js
const router = new VueRouter({
  routes: [
    {
      path: '/user/:id',
      component: UserDetail,
      // 路由级数据预获取
      beforeEnter: async (to, from, next) => {
        try {
          // 获取用户数据
          const userData = await api.getUser(to.params.id)
          // 将数据附加到路由对象
          to.params.userData = userData
          next()
        } catch (error) {
          next({ name: 'error', params: { error } })
        }
      }
    }
  ]
})

// UserDetail.vue
export default {
  data() {
    return {
      user: null
    }
  },
  created() {
    // 使用路由守卫预加载的数据
    if (this.$route.params.userData) {
      this.user = this.$route.params.userData
    }
  }
}
```

## Vuex中的异步数据处理

### 基础Action模式

```js
// store/modules/user.js
export default {
  namespaced: true,
  state: {
    user: null,
    loading: false,
    error: null
  },
  mutations: {
    SET_LOADING(state, status) {
      state.loading = status
    },
    SET_USER(state, user) {
      state.user = user
    },
    SET_ERROR(state, error) {
      state.error = error
    }
  },
  actions: {
    async fetchUser({ commit }, userId) {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)
      
      try {
        const user = await api.getUser(userId)
        commit('SET_USER', user)
        return user
      } catch (error) {
        commit('SET_ERROR', error.message)
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    }
  },
  getters: {
    isLoading: state => state.loading,
    hasError: state => !!state.error
  }
}

// 在组件中使用
import { mapState, mapActions } from 'vuex'

export default {
  computed: {
    ...mapState('user', ['user', 'loading', 'error'])
  },
  methods: {
    ...mapActions('user', ['fetchUser']),
    async loadUserData() {
      try {
        await this.fetchUser(this.$route.params.id)
      } catch (error) {
        // 组件级处理
        this.$message.error('加载用户数据失败')
      }
    }
  },
  created() {
    this.loadUserData()
  }
}
```

### 多Action协调

```js
// store/modules/dashboard.js
export default {
  namespaced: true,
  actions: {
    // 协调多个异步action
    async loadDashboard({ dispatch }) {
      // 并行请求数据
      await Promise.all([
        dispatch('fetchUserProfile'),
        dispatch('fetchNotifications'),
        dispatch('fetchStatistics')
      ])
    },
    
    // 串行请求 - 第二个请求依赖第一个请求的结果
    async fetchUserAndPosts({ dispatch, state }) {
      await dispatch('fetchUser')
      if (state.user) {
        await dispatch('fetchUserPosts', state.user.id)
      }
    }
  }
}
```

## 高级异步处理技巧

### 请求取消

使用axios的取消功能：

```js
// 封装支持取消的API调用
import axios from 'axios'

export function useCancel() {
  // 存储当前活跃的取消令牌
  const cancelTokens = {}
  
  // 创建带有取消功能的请求
  function createCancelableRequest(key) {
    // 如果已有同名请求，取消它
    if (cancelTokens[key]) {
      cancelTokens[key]('Operation canceled due to new request')
      delete cancelTokens[key]
    }
    
    // 创建新的取消令牌
    const source = axios.CancelToken.source()
    cancelTokens[key] = source.cancel
    
    return {
      cancelToken: source.token,
      cancel: () => {
        if (cancelTokens[key]) {
          cancelTokens[key]('Operation canceled by user')
          delete cancelTokens[key]
        }
      }
    }
  }
  
  // 取消所有请求
  function cancelAll() {
    Object.keys(cancelTokens).forEach(key => {
      cancelTokens[key]('Operation canceled - component unmounted')
      delete cancelTokens[key]
    })
  }
  
  return {
    createCancelableRequest,
    cancelAll
  }
}

// 在组件中使用
export default {
  data() {
    return {
      searchResults: [],
      loading: false
    }
  },
  created() {
    this.cancelApi = useCancel()
  },
  beforeDestroy() {
    // 组件销毁时取消所有请求
    this.cancelApi.cancelAll()
  },
  methods: {
    async searchProducts(query) {
      this.loading = true
      
      // 创建可取消的请求
      const { cancelToken } = this.cancelApi.createCancelableRequest('search')
      
      try {
        const results = await api.searchProducts(query, { cancelToken })
        this.searchResults = results
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error('Search failed:', error)
        }
      } finally {
        this.loading = false
      }
    }
  }
}
```

### 请求节流和防抖

对于频繁触发的请求进行控制：

```js
// 防抖搜索实现
import { debounce } from 'lodash-es'

export default {
  data() {
    return {
      searchQuery: '',
      results: []
    }
  },
  created() {
    // 创建防抖搜索函数
    this.debouncedSearch = debounce(this.performSearch, 300)
  },
  methods: {
    onSearchInput() {
      this.debouncedSearch()
    },
    async performSearch() {
      if (!this.searchQuery.trim()) {
        this.results = []
        return
      }
      
      try {
        this.results = await api.search(this.searchQuery)
      } catch (error) {
        console.error('Search failed:', error)
      }
    }
  },
  beforeDestroy() {
    // 取消待执行的防抖函数
    this.debouncedSearch.cancel()
  }
}
```

### 并发请求控制

```js
// 并发请求限制
async function fetchAllWithConcurrency(items, fetchFn, concurrency = 3) {
  const results = []
  const chunks = []
  
  // 将items分成多个chunks
  for (let i = 0; i < items.length; i += concurrency) {
    chunks.push(items.slice(i, i + concurrency))
  }
  
  // 逐个处理chunks
  for (const chunk of chunks) {
    // 并行处理当前chunk中的所有项
    const chunkResults = await Promise.all(
      chunk.map(item => fetchFn(item))
    )
    results.push(...chunkResults)
  }
  
  return results
}

// 使用示例
async function loadAllUserData(userIds) {
  try {
    const users = await fetchAllWithConcurrency(
      userIds,
      id => api.getUser(id),
      5 // 最多同时5个请求
    )
    this.users = users
  } catch (error) {
    console.error('Failed to load users:', error)
  }
}
```

## 异步数据加载组件

### 创建异步加载容器组件

```vue
<!-- AsyncDataContainer.vue -->
<template>
  <div>
    <div v-if="loading" class="loading-container">
      <slot name="loading">
        <div class="default-loader">加载中...</div>
      </slot>
    </div>
    
    <div v-else-if="error" class="error-container">
      <slot name="error" :error="error" :retry="fetchData">
        <div class="default-error">
          <p>{{ error }}</p>
          <button @click="fetchData">重试</button>
        </div>
      </slot>
    </div>
    
    <div v-else-if="isEmpty" class="empty-container">
      <slot name="empty">
        <div class="default-empty">暂无数据</div>
      </slot>
    </div>
    
    <div v-else class="data-container">
      <slot :data="data"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    fetchFunction: {
      type: Function,
      required: true
    },
    params: {
      type: [Object, Array, String, Number],
      default: null
    },
    immediate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      data: null,
      loading: false,
      error: null
    }
  },
  computed: {
    isEmpty() {
      return this.data === null || 
        (Array.isArray(this.data) && this.data.length === 0) ||
        (typeof this.data === 'object' && Object.keys(this.data).length === 0)
    }
  },
  watch: {
    params: {
      handler: 'fetchData',
      deep: true
    }
  },
  created() {
    if (this.immediate) {
      this.fetchData()
    }
  },
  methods: {
    async fetchData() {
      this.loading = true
      this.error = null
      
      try {
        this.data = await this.fetchFunction(this.params)
      } catch (error) {
        this.error = error.message || '加载数据失败'
        this.$emit('error', error)
      } finally {
        this.loading = false
        this.$emit('loaded', this.data)
      }
    }
  }
}
</script>
```

使用异步加载容器：

```vue
<template>
  <div>
    <h1>用户列表</h1>
    
    <AsyncDataContainer 
      :fetch-function="fetchUsers"
      :params="{ page, limit }"
      @loaded="handleUsersLoaded"
    >
      <template #loading>
        <CustomLoader />
      </template>
      
      <template #error="{ error, retry }">
        <div class="custom-error">
          <p>{{ error }}</p>
          <button @click="retry">重新加载</button>
        </div>
      </template>
      
      <template #default="{ data }">
        <UserList :users="data" />
      </template>
    </AsyncDataContainer>
  </div>
</template>

<script>
import AsyncDataContainer from '@/components/AsyncDataContainer.vue'
import UserList from '@/components/UserList.vue'
import CustomLoader from '@/components/CustomLoader.vue'
import api from '@/api'

export default {
  components: {
    AsyncDataContainer,
    UserList,
    CustomLoader
  },
  data() {
    return {
      page: 1,
      limit: 10
    }
  },
  methods: {
    fetchUsers(params) {
      return api.getUsers(params)
    },
    handleUsersLoaded(users) {
      console.log('Users loaded:', users.length)
    }
  }
}
</script>
```

## 错误处理最佳实践

### 全局错误处理

```js
// main.js - 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue error:', err)
  
  // 记录错误
  errorTrackingService.captureError(err, {
    extra: {
      componentName: vm.$options.name,
      info,
      url: window.location.href
    }
  })
  
  // 显示全局错误通知
  if (vm.$store) {
    vm.$store.dispatch('notification/add', {
      type: 'error',
      message: '操作失败，请稍后重试'
    })
  }
}

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled Promise Rejection:', event.reason)
  
  // 防止默认处理
  event.preventDefault()
  
  // 记录错误
  errorTrackingService.captureError(event.reason, {
    extra: {
      type: 'unhandledrejection',
      url: window.location.href
    }
  })
})
```

### 组件级错误处理

```vue
<script>
export default {
  data() {
    return {
      retryCount: 0,
      maxRetries: 3
    }
  },
  methods: {
    async fetchData() {
      try {
        return await api.getData()
      } catch (error) {
        // 错误分类处理
        if (error.response) {
          // 服务器返回错误
          switch (error.response.status) {
            case 401:
              this.$router.push('/login')
              break
            case 403:
              this.handleForbiddenError()
              break
            case 404:
              this.handleNotFoundError()
              break
            case 500:
              this.handleServerError()
              break
          }
        } else if (error.request) {
          // 网络错误
          if (this.retryCount < this.maxRetries) {
            this.retryCount++
            console.log(`重试 (${this.retryCount}/${this.maxRetries})...`)
            return this.fetchDataWithBackoff()
          } else {
            this.handleNetworkError()
          }
        } else {
          // 请求设置错误
          console.error('Request setup error:', error)
        }
        
        throw error
      }
    },
    // 带指数退避的重试
    async fetchDataWithBackoff() {
      const backoffTime = Math.pow(2, this.retryCount) * 1000
      await new Promise(resolve => setTimeout(resolve, backoffTime))
      return this.fetchData()
    }
  }
}
</script>
```

## 数据缓存与持久化

### 内存缓存

```js
// 简单的内存缓存
const cache = new Map()
const CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟缓存

async function fetchWithCache(key, fetchFn) {
  const cacheKey = `data:${key}`
  const now = Date.now()
  
  // 检查缓存
  if (cache.has(cacheKey)) {
    const { data, timestamp } = cache.get(cacheKey)
    // 检查是否过期
    if (now - timestamp < CACHE_EXPIRY) {
      return data
    }
  }
  
  // 缓存未命中或过期，重新获取数据
  const data = await fetchFn()
  
  // 存入缓存
  cache.set(cacheKey, {
    data,
    timestamp: now
  })
  
  return data
}

// 使用缓存
async function getUserData(userId) {
  return fetchWithCache(`user:${userId}`, () => api.getUser(userId))
}
```

### 持久化缓存

```js
// localStorage缓存
const STORAGE_KEY_PREFIX = 'app_cache:'
const CACHE_EXPIRY = 24 * 60 * 60 * 1000 // 24小时缓存

async function fetchWithPersistence(key, fetchFn) {
  const storageKey = `${STORAGE_KEY_PREFIX}${key}`
  const now = Date.now()
  
  // 尝试从localStorage获取
  try {
    const cachedData = localStorage.getItem(storageKey)
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData)
      // 检查是否过期
      if (now - timestamp < CACHE_EXPIRY) {
        return data
      }
    }
  } catch (e) {
    console.warn('Cache read error:', e)
  }
  
  // 缓存未命中或过期，重新获取数据
  const data = await fetchFn()
  
  // 存入localStorage
  try {
    localStorage.setItem(storageKey, JSON.stringify({
      data,
      timestamp: now
    }))
  } catch (e) {
    console.warn('Cache write error:', e)
  }
  
  return data
}

// 清除缓存
function clearCache(keyPattern = null) {
  if (keyPattern) {
    // 清除特定模式的缓存
    const pattern = `${STORAGE_KEY_PREFIX}${keyPattern}`
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key.startsWith(pattern)) {
        localStorage.removeItem(key)
      }
    }
  } else {
    // 清除所有缓存
    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i)
      if (key.startsWith(STORAGE_KEY_PREFIX)) {
        localStorage.removeItem(key)
      }
    }
  }
}
```

## 最佳实践总结

1. **加载状态管理**
   - 始终显示加载状态指示器
   - 对长时间加载使用进度条或骨架屏
   - 避免UI闪烁和布局偏移

2. **错误处理**
   - 实现完善的错误捕获和展示
   - 分类处理不同类型的错误
   - 提供用户友好的错误信息和恢复选项

3. **性能优化**
   - 合理使用缓存避免重复请求
   - 实现请求防抖和节流
   - 控制并发请求数量

4. **代码结构**
   - 将异步逻辑与UI分离
   - 使用Vuex集中管理异步状态
   - 创建可复用的异步处理组件

5. **用户体验**
   - 实现乐观更新
   - 提供数据刷新和重试机制
   - 使用骨架屏减少等待感

通过合理的异步数据处理策略，可以提升应用性能和用户体验，同时保持代码的可维护性。 