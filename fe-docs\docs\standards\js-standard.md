# JavaScript编码规范

本文档定义了项目中JavaScript代码的编写规范，旨在保持代码风格一致，提高代码质量和可维护性。

## 命名规范

### 变量命名

- 使用有意义的、能准确表达意图的名称
- 使用小驼峰命名法（camelCase）
- 布尔类型变量使用 `is`、`has`、`can` 等前缀
- 常量使用全大写，下划线分隔（UPPER_SNAKE_CASE）

```js
// 好的命名
const userName = 'Zhang San';
const isActive = true;
const MAX_RETRY_COUNT = 3;

// 不好的命名
const u = 'Zhang San';
const active = true;
const retry = 3;
```

### 函数命名

- 使用动词或动词短语命名
- 使用小驼峰命名法（camelCase）
- 函数名应当表明其功能和返回值

```js
// 好的命名
function getUserData() { /* ... */ }
function validateForm() { /* ... */ }
function handleSubmit() { /* ... */ }

// 不好的命名
function data() { /* ... */ }
function form() { /* ... */ }
function onSubmit() { /* ... */ } // 除非是真正的事件处理函数
```

### 文件命名

- 工具/服务文件：使用小驼峰命名法（camelCase），如 `httpService.js`
- 类文件：使用大驼峰命名法（PascalCase），如 `UserModel.js`

## 代码风格

### 缩进与格式化

- 使用2个空格进行缩进
- 语句末尾使用分号
- 使用单引号作为字符串的默认引用符号
- 对象字面量的冒号后应有一个空格

```js
// 正确的格式
const user = {
  name: 'Zhang San',
  age: 30,
  isActive: true
};
```

### 空白与换行

- 运算符两侧应有空格
- 逗号后应有空格
- 代码块的大括号前应有空格
- 函数参数的括号与函数名之间不应有空格
- 每个文件末尾保留一个空行

```js
// 正确的空白使用
if (isActive && user) {
  const fullName = firstName + ' ' + lastName;
  console.log(fullName);
}

function add(a, b) {
  return a + b;
}
```

## 语言特性使用

### ES6+特性

- 优先使用 `const`，其次是 `let`，避免使用 `var`
- 使用箭头函数简化函数表达式
- 使用模板字符串代替字符串拼接
- 使用解构赋值简化数据访问
- 使用展开运算符进行浅拷贝
- 使用 `Promise` 和 `async/await` 处理异步操作

```js
// 推荐的ES6+特性使用
const { name, age } = user;
const fullName = `${firstName} ${lastName}`;

const newArray = [...oldArray, newItem];
const newObject = { ...oldObject, newProperty: value };

async function fetchData() {
  try {
    const response = await api.getData();
    return response.data;
  } catch (error) {
    console.error('获取数据失败:', error);
    throw error;
  }
}
```

### 条件语句

- 优先使用三元运算符处理简单条件
- 使用 `===` 和 `!==` 进行比较，避免使用 `==` 和 `!=`
- 避免嵌套过多的条件语句，可考虑提前返回或使用策略模式

```js
// 推荐的条件语句写法
const result = isActive ? 'Active' : 'Inactive';

if (value === undefined || value === null) {
  return defaultValue;
}

// 避免嵌套过多的条件语句
function getPayAmount() {
  if (isDead) return deadAmount();
  if (isSeparated) return separatedAmount();
  if (isRetired) return retiredAmount();
  return normalAmount();
}
```

## 注释规范

### 文档注释

- 使用 JSDoc 风格为函数、类和复杂代码块添加注释
- 公共API必须有完整的文档注释

```js
/**
 * 计算两个数的和
 * @param {number} a - 第一个数
 * @param {number} b - 第二个数
 * @returns {number} 两数之和
 */
function add(a, b) {
  return a + b;
}

/**
 * 用户类
 * @class
 */
class User {
  /**
   * 创建用户实例
   * @param {string} name - 用户名
   * @param {number} age - 年龄
   */
  constructor(name, age) {
    this.name = name;
    this.age = age;
  }
}
```

### 行内注释

- 添加必要的注释，但避免无意义的注释
- 复杂的逻辑应当有注释说明
- 临时代码或需要未来改进的代码应当标记（TODO, FIXME等）

```js
// 复杂算法的关键步骤说明
function complexAlgorithm() {
  // 第一步：初始化数据结构
  const data = initializeData();
  
  // 第二步：应用转换
  const transformed = applyTransformation(data);
  
  // TODO: 优化此处的性能问题
  return finalizeResult(transformed);
}
```

## 代码组织

### 模块化

- 每个文件只包含单一职责的代码
- 相关功能应放在同一模块中
- 避免过长的函数和文件（函数通常不超过50行，文件通常不超过300行）
- 使用ES模块系统（import/export）

```js
// 导出单个值
export default function formatDate(date) {
  // ...
}

// 导出多个值
export function add(a, b) {
  return a + b;
}

export function subtract(a, b) {
  return a - b;
}

// 导入
import formatDate from './formatDate';
import { add, subtract } from './math';
```

### 错误处理

- 使用 try/catch 捕获并处理异常
- 避免吞掉错误，始终进行适当的错误处理或传播
- 对于异步代码，确保错误被正确捕获和处理

```js
async function fetchUserData(userId) {
  try {
    const response = await api.getUser(userId);
    return response.data;
  } catch (error) {
    // 记录错误
    logger.error('获取用户数据失败', { userId, error });
    
    // 根据错误类型进行处理
    if (error.response && error.response.status === 404) {
      throw new NotFoundError('用户不存在');
    }
    
    // 重新抛出错误以便上层处理
    throw error;
  }
}
```

## 性能考虑

- 避免不必要的计算和DOM操作
- 使用节流(throttle)和防抖(debounce)处理频繁触发的事件
- 大型列表考虑虚拟滚动
- 大型应用考虑代码分割和懒加载

```js
// 防抖函数示例
function debounce(fn, delay) {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 使用防抖
const debouncedSearch = debounce(searchFunction, 300);
```

## 工具配置

所有项目统一使用 ESLint + Prettier 进行代码格式化，相关配置文件：

- `.eslintrc.js`
- `.prettierrc.js`

建议在编辑器中配置保存时自动格式化。

## 总结

遵循一致的JavaScript编码规范有助于提高代码质量、可读性和可维护性。每个开发人员都应该熟悉并遵循这些规范。如有任何问题或建议，请在团队会议中提出讨论。 