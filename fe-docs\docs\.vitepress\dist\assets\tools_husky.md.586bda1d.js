import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"Husky 配置指南","description":"","frontmatter":{},"headers":[],"relativePath":"tools/husky.md","filePath":"tools/husky.md"}'),p={name:"tools/husky.md"},o=l(`<h1 id="husky-配置指南" tabindex="-1">Husky 配置指南 <a class="header-anchor" href="#husky-配置指南" aria-label="Permalink to &quot;Husky 配置指南&quot;">​</a></h1><p>Husky 是一个 Git hooks 工具，可以在 Git 操作（如提交、推送）时自动执行脚本，确保代码质量和规范。本文档提供了完整的 Husky 配置指南。</p><h2 id="什么是-git-hooks" tabindex="-1">什么是 Git Hooks <a class="header-anchor" href="#什么是-git-hooks" aria-label="Permalink to &quot;什么是 Git Hooks&quot;">​</a></h2><p>Git Hooks 是 Git 在特定事件发生时自动执行的脚本，常用的 hooks 包括：</p><ul><li><code>pre-commit</code>：提交前执行</li><li><code>commit-msg</code>：提交信息验证</li><li><code>pre-push</code>：推送前执行</li><li><code>post-merge</code>：合并后执行</li></ul><h2 id="安装和基础配置" tabindex="-1">安装和基础配置 <a class="header-anchor" href="#安装和基础配置" aria-label="Permalink to &quot;安装和基础配置&quot;">​</a></h2><h3 id="_1-安装-husky" tabindex="-1">1. 安装 Husky <a class="header-anchor" href="#_1-安装-husky" aria-label="Permalink to &quot;1. 安装 Husky&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装 Husky</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 启用 Git hooks</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 添加到 package.json 脚本中</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pkg</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">scripts.prepare=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">husky install</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h3 id="_2-husky-v7-配置" tabindex="-1">2. Husky v7+ 配置 <a class="header-anchor" href="#_2-husky-v7-配置" aria-label="Permalink to &quot;2. Husky v7+ 配置&quot;">​</a></h3><p>创建 <code>.husky</code> 目录和 hooks：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 创建 pre-commit hook</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-commit</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">npm run lint</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 创建 commit-msg hook</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/commit-msg</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">npx commitlint --edit </span><span style="color:#BABED8;font-style:italic;">$1</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 创建 pre-push hook</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-push</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">npm run test</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h3 id="_3-目录结构" tabindex="-1">3. 目录结构 <a class="header-anchor" href="#_3-目录结构" aria-label="Permalink to &quot;3. 目录结构&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">.husky/</span></span>
<span class="line"><span style="color:#babed8;">├── _/</span></span>
<span class="line"><span style="color:#babed8;">│   ├── .gitignore</span></span>
<span class="line"><span style="color:#babed8;">│   └── husky.sh</span></span>
<span class="line"><span style="color:#babed8;">├── pre-commit</span></span>
<span class="line"><span style="color:#babed8;">├── commit-msg</span></span>
<span class="line"><span style="color:#babed8;">└── pre-push</span></span></code></pre></div><h2 id="常用-hooks-配置" tabindex="-1">常用 Hooks 配置 <a class="header-anchor" href="#常用-hooks-配置" aria-label="Permalink to &quot;常用 Hooks 配置&quot;">​</a></h2><h3 id="_1-pre-commit-hook" tabindex="-1">1. pre-commit Hook <a class="header-anchor" href="#_1-pre-commit-hook" aria-label="Permalink to &quot;1. pre-commit Hook&quot;">​</a></h3><p><code>.husky/pre-commit</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行 lint-staged</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 或者直接运行命令</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># npm run lint</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># npm run format</span></span></code></pre></div><h3 id="_2-commit-msg-hook" tabindex="-1">2. commit-msg Hook <a class="header-anchor" href="#_2-commit-msg-hook" aria-label="Permalink to &quot;2. commit-msg Hook&quot;">​</a></h3><p><code>.husky/commit-msg</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 验证提交信息格式</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commitlint</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--edit</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">$1</span></span></code></pre></div><h3 id="_3-pre-push-hook" tabindex="-1">3. pre-push Hook <a class="header-anchor" href="#_3-pre-push-hook" aria-label="Permalink to &quot;3. pre-push Hook&quot;">​</a></h3><p><code>.husky/pre-push</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行测试</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行构建检查</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">build</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查分支名称</span></span>
<span class="line"><span style="color:#BABED8;">branch</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">$(</span><span style="color:#FFCB6B;">git</span><span style="color:#C3E88D;"> rev-parse --abbrev-ref HEAD</span><span style="color:#89DDFF;">)</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[[</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">master</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">||</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">main</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">]];</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">不能直接推送到主分支</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span></code></pre></div><h2 id="与-lint-staged-集成" tabindex="-1">与 lint-staged 集成 <a class="header-anchor" href="#与-lint-staged-集成" aria-label="Permalink to &quot;与 lint-staged 集成&quot;">​</a></h2><h3 id="_1-安装-lint-staged" tabindex="-1">1. 安装 lint-staged <a class="header-anchor" href="#_1-安装-lint-staged" aria-label="Permalink to &quot;1. 安装 lint-staged&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span></code></pre></div><h3 id="_2-配置-lint-staged" tabindex="-1">2. 配置 lint-staged <a class="header-anchor" href="#_2-配置-lint-staged" aria-label="Permalink to &quot;2. 配置 lint-staged&quot;">​</a></h3><p>在 <code>package.json</code> 中配置：</p><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">lint-staged</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">*.{js,vue}</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">eslint --fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">],</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">*.{css,scss}</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">stylelint --fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">],</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">*.{json,md}</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><p>或创建 <code>.lintstagedrc.js</code> 文件：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{css,scss}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">stylelint --fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{json,md}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{png,jpg,jpeg,gif,svg}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">imagemin-lint-staged</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-高级-lint-staged-配置" tabindex="-1">3. 高级 lint-staged 配置 <a class="header-anchor" href="#_3-高级-lint-staged-配置" aria-label="Permalink to &quot;3. 高级 lint-staged 配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// .lintstagedrc.js</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> path </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">require</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 相对路径配置</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">map</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filename</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">filename</span><span style="color:#89DDFF;">}\`</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 条件执行</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">]</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 只对 src 目录下的文件运行测试</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">srcFiles</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">filter</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">file</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">file</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">startsWith</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">src/</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    )</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">srcFiles</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">commands</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">npm run test:unit</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 自定义函数</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 检查组件命名</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">filename</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">basename</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">path</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">basename</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">filename</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">.vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">basename</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">!==</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">basename</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">charAt</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toUpperCase</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">basename</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">slice</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">)) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;font-style:italic;">throw</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Error</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">组件文件名应该使用 PascalCase: </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">filename</span><span style="color:#89DDFF;">}\`</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">commands</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}\`</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="提交信息验证" tabindex="-1">提交信息验证 <a class="header-anchor" href="#提交信息验证" aria-label="Permalink to &quot;提交信息验证&quot;">​</a></h2><h3 id="_1-安装-commitlint" tabindex="-1">1. 安装 commitlint <a class="header-anchor" href="#_1-安装-commitlint" aria-label="Permalink to &quot;1. 安装 commitlint&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@commitlint/cli</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@commitlint/config-conventional</span></span></code></pre></div><h3 id="_2-配置-commitlint" tabindex="-1">2. 配置 commitlint <a class="header-anchor" href="#_2-配置-commitlint" aria-label="Permalink to &quot;2. 配置 commitlint&quot;">​</a></h3><p>创建 <code>commitlint.config.js</code> 文件：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">extends</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@commitlint/config-conventional</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">rules</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-enum</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">feat</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 新功能</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 修复</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">docs</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 文档</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">style</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 格式</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">refactor</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 重构</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">perf</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 性能优化</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">test</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 测试</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">chore</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 构建过程或辅助工具的变动</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">revert</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 回滚</span></span>
<span class="line"><span style="color:#BABED8;">      ]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-empty</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">scope-empty</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">scope-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-empty</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-full-stop</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">.</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">header-max-length</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">72</span><span style="color:#BABED8;">]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-自定义提交信息规则" tabindex="-1">3. 自定义提交信息规则 <a class="header-anchor" href="#_3-自定义提交信息规则" aria-label="Permalink to &quot;3. 自定义提交信息规则&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// commitlint.config.js</span></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">extends</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@commitlint/config-conventional</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">rules</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 自定义类型</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-enum</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">feat</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 新功能</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 修复bug</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">docs</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 文档更新</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">style</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 代码风格调整</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">refactor</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 重构</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">perf</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 性能优化</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">test</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 测试</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">chore</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 构建过程或辅助工具的变动</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">revert</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">   </span><span style="color:#676E95;font-style:italic;">// 回滚</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">wip</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 开发中</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">build</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 构建系统</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">ci</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// CI配置</span></span>
<span class="line"><span style="color:#BABED8;">      ]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 自定义作用域</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">scope-enum</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">components</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 组件</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">utils</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 工具</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">api</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 接口</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">router</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 路由</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">store</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 状态管理</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">styles</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 样式</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">config</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 配置</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">deps</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 依赖</span></span>
<span class="line"><span style="color:#BABED8;">      ]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 主题长度限制</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-max-length</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">50</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 主题格式</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">sentence-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">start-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">pascal-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">upper-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="高级配置" tabindex="-1">高级配置 <a class="header-anchor" href="#高级配置" aria-label="Permalink to &quot;高级配置&quot;">​</a></h2><h3 id="_1-条件执行" tabindex="-1">1. 条件执行 <a class="header-anchor" href="#_1-条件执行" aria-label="Permalink to &quot;1. 条件执行&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .husky/pre-commit</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查是否在 CI 环境中</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$CI</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">true</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">];</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">跳过 pre-commit hooks (CI 环境)</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查分支</span></span>
<span class="line"><span style="color:#BABED8;">branch</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">$(</span><span style="color:#FFCB6B;">git</span><span style="color:#C3E88D;"> rev-parse --abbrev-ref HEAD</span><span style="color:#89DDFF;">)</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[[</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">master</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">||</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">main</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">]];</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">主分支需要更严格的检查</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test:all</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint:strict</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">else</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span></code></pre></div><h3 id="_2-性能优化" tabindex="-1">2. 性能优化 <a class="header-anchor" href="#_2-性能优化" aria-label="Permalink to &quot;2. 性能优化&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// .lintstagedrc.js</span></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 并行执行</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix --cache</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write --cache</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 只检查修改的文件</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.js</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}\`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">jest --findRelatedTests </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}\`</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-错误处理" tabindex="-1">3. 错误处理 <a class="header-anchor" href="#_3-错误处理" aria-label="Permalink to &quot;3. 错误处理&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .husky/pre-commit</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 设置错误处理</span></span>
<span class="line"><span style="color:#82AAFF;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-e</span></span>
<span class="line"></span>
<span class="line"><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">运行 pre-commit 检查...</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行 lint-staged</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">❌ lint-staged 检查失败</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行测试</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test:changed</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">❌ 测试失败</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">✅ 所有检查通过</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h2 id="团队协作配置" tabindex="-1">团队协作配置 <a class="header-anchor" href="#团队协作配置" aria-label="Permalink to &quot;团队协作配置&quot;">​</a></h2><h3 id="_1-统一配置" tabindex="-1">1. 统一配置 <a class="header-anchor" href="#_1-统一配置" aria-label="Permalink to &quot;1. 统一配置&quot;">​</a></h3><p>在 <code>package.json</code> 中添加：</p><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">scripts</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">prepare</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">husky install</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">postinstall</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">husky install</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">husky</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">hooks</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#F78C6C;">pre-commit</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">lint-staged</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#F78C6C;">commit-msg</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">commitlint -E HUSKY_GIT_PARAMS</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_2-跳过-hooks" tabindex="-1">2. 跳过 Hooks <a class="header-anchor" href="#_2-跳过-hooks" aria-label="Permalink to &quot;2. 跳过 Hooks&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 跳过 pre-commit hook</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix: urgent fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--no-verify</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 跳过所有 hooks</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix: urgent fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-n</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 环境变量跳过</span></span>
<span class="line"><span style="color:#BABED8;">HUSKY</span><span style="color:#89DDFF;">=</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix: urgent fix</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h3 id="_3-调试-hooks" tabindex="-1">3. 调试 Hooks <a class="header-anchor" href="#_3-调试-hooks" aria-label="Permalink to &quot;3. 调试 Hooks&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 调试模式</span></span>
<span class="line"><span style="color:#BABED8;">HUSKY_DEBUG</span><span style="color:#89DDFF;">=</span><span style="color:#F78C6C;">1</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">test</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看 hook 执行过程</span></span>
<span class="line"><span style="color:#FFCB6B;">sh</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-x</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-commit</span></span></code></pre></div><h2 id="常见问题解决" tabindex="-1">常见问题解决 <a class="header-anchor" href="#常见问题解决" aria-label="Permalink to &quot;常见问题解决&quot;">​</a></h2><h3 id="_1-hooks-不执行" tabindex="-1">1. Hooks 不执行 <a class="header-anchor" href="#_1-hooks-不执行" aria-label="Permalink to &quot;1. Hooks 不执行&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 检查 husky 是否正确安装</span></span>
<span class="line"><span style="color:#FFCB6B;">ls</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-la</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.git/hooks/</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 重新安装 husky</span></span>
<span class="line"><span style="color:#FFCB6B;">rm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-rf</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.git/hooks</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查权限</span></span>
<span class="line"><span style="color:#FFCB6B;">chmod</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">+x</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-commit</span></span></code></pre></div><h3 id="_2-windows-兼容性" tabindex="-1">2. Windows 兼容性 <a class="header-anchor" href="#_2-windows-兼容性" aria-label="Permalink to &quot;2. Windows 兼容性&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .husky/pre-commit (Windows 兼容)</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 使用 npx 确保跨平台兼容</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span></code></pre></div><h3 id="_3-性能问题" tabindex="-1">3. 性能问题 <a class="header-anchor" href="#_3-性能问题" aria-label="Permalink to &quot;3. 性能问题&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 优化 lint-staged 配置</span></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 使用缓存</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix --cache</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write --cache</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 限制文件数量</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.js</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">npm run lint:all</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">\`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">\${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}\`</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="最佳实践" tabindex="-1">最佳实践 <a class="header-anchor" href="#最佳实践" aria-label="Permalink to &quot;最佳实践&quot;">​</a></h2><h3 id="_1-渐进式采用" tabindex="-1">1. 渐进式采用 <a class="header-anchor" href="#_1-渐进式采用" aria-label="Permalink to &quot;1. 渐进式采用&quot;">​</a></h3><ul><li>从简单的 lint 检查开始</li><li>逐步添加更多检查</li><li>团队讨论和调整规则</li></ul><h3 id="_2-性能考虑" tabindex="-1">2. 性能考虑 <a class="header-anchor" href="#_2-性能考虑" aria-label="Permalink to &quot;2. 性能考虑&quot;">​</a></h3><ul><li>使用缓存加速检查</li><li>只检查修改的文件</li><li>合理设置超时时间</li></ul><h3 id="_3-团队协作" tabindex="-1">3. 团队协作 <a class="header-anchor" href="#_3-团队协作" aria-label="Permalink to &quot;3. 团队协作&quot;">​</a></h3><ul><li>统一 hooks 配置</li><li>提供跳过机制</li><li>文档化配置说明</li></ul><h3 id="_4-错误处理" tabindex="-1">4. 错误处理 <a class="header-anchor" href="#_4-错误处理" aria-label="Permalink to &quot;4. 错误处理&quot;">​</a></h3><ul><li>提供清晰的错误信息</li><li>给出修复建议</li><li>记录常见问题解决方案</li></ul><p>通过合理配置 Husky，可以在代码提交前自动执行质量检查，确保代码库的健康和团队开发规范的执行。</p>`,73),e=[o];function t(c,r,D,y,F,i){return a(),n("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
