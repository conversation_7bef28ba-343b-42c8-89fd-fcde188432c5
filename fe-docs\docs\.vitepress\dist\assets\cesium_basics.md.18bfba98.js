import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"基础配置","description":"","frontmatter":{},"headers":[],"relativePath":"cesium/basics.md","filePath":"cesium/basics.md"}'),p={name:"cesium/basics.md"},o=l(`<h1 id="基础配置" tabindex="-1">基础配置 <a class="header-anchor" href="#基础配置" aria-label="Permalink to &quot;基础配置&quot;">​</a></h1><p>本章将介绍如何在项目中集成Cesium，包括安装、初始化和基础配置。</p><h2 id="安装与引入" tabindex="-1">安装与引入 <a class="header-anchor" href="#安装与引入" aria-label="Permalink to &quot;安装与引入&quot;">​</a></h2><h3 id="npm安装" tabindex="-1">NPM安装 <a class="header-anchor" href="#npm安装" aria-label="Permalink to &quot;NPM安装&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 使用npm安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cesium</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 使用yarn安装</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cesium</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 使用pnpm安装</span></span>
<span class="line"><span style="color:#FFCB6B;">pnpm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cesium</span></span></code></pre></div><h3 id="cdn引入" tabindex="-1">CDN引入 <a class="header-anchor" href="#cdn引入" aria-label="Permalink to &quot;CDN引入&quot;">​</a></h3><div class="language-html"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">&lt;!-- 引入Cesium CSS --&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">link</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">href</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Widgets/widgets.css</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">rel</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">stylesheet</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">&lt;!-- 引入Cesium JavaScript --&gt;</span></span>
<span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">script</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">src</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Cesium.js</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;&lt;/</span><span style="color:#F07178;">script</span><span style="color:#89DDFF;">&gt;</span></span></code></pre></div><h3 id="es6模块导入" tabindex="-1">ES6模块导入 <a class="header-anchor" href="#es6模块导入" aria-label="Permalink to &quot;ES6模块导入&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 引入Cesium库</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">*</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">as</span><span style="color:#BABED8;"> Cesium </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesium</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 引入Cesium样式</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesium/Build/Cesium/Widgets/widgets.css</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="ion访问令牌配置" tabindex="-1">Ion访问令牌配置 <a class="header-anchor" href="#ion访问令牌配置" aria-label="Permalink to &quot;Ion访问令牌配置&quot;">​</a></h2><p>Cesium Ion是Cesium的云端平台，提供全球高质量的3D数据。使用前需要配置访问令牌：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 设置Cesium Ion默认访问令牌</span></span>
<span class="line"><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Ion</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">defaultAccessToken </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your_access_token_here</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span></code></pre></div><div class="tip custom-block"><p class="custom-block-title">获取访问令牌</p><ol><li>访问 <a href="https://cesium.com/ion/" target="_blank" rel="noreferrer">Cesium Ion官网</a></li><li>注册或登录账户</li><li>在控制台中获取访问令牌</li><li>免费账户每月有一定的数据配额</li></ol></div><h2 id="基础初始化" tabindex="-1">基础初始化 <a class="header-anchor" href="#基础初始化" aria-label="Permalink to &quot;基础初始化&quot;">​</a></h2><h3 id="最简单的初始化" tabindex="-1">最简单的初始化 <a class="header-anchor" href="#最简单的初始化" aria-label="Permalink to &quot;最简单的初始化&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建最基本的Cesium Viewer</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="完整的初始化配置" tabindex="-1">完整的初始化配置 <a class="header-anchor" href="#完整的初始化配置" aria-label="Permalink to &quot;完整的初始化配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 地形数据提供者</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">terrainProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createWorldTerrain</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 影像图层选择器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">baseLayerPicker</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 地理编码搜索</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">geocoder</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 主页按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">homeButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 场景模式选择器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">sceneModePicker</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 导航帮助按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">navigationHelpButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 动画控件</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">animation</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 时间轴</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">timeline</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 全屏按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">fullscreenButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// VR按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">vrButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 信息框</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">infoBox</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 选择指示器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">selectionIndicator</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 仅3D模式</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">scene3DOnly</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 阴影</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">shadows</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 是否显示渲染循环错误</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">showRenderLoopErrors</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 地图模式（2D、3D、Columbus View）</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">sceneMode</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">SceneMode</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">SCENE3D</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 地图投影</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mapProjection</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">WebMercatorProjection</span><span style="color:#BABED8;">()</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="配置选项详解" tabindex="-1">配置选项详解 <a class="header-anchor" href="#配置选项详解" aria-label="Permalink to &quot;配置选项详解&quot;">​</a></h2><h3 id="控件配置" tabindex="-1">控件配置 <a class="header-anchor" href="#控件配置" aria-label="Permalink to &quot;控件配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 隐藏所有默认控件</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">animation</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">           </span><span style="color:#676E95;font-style:italic;">// 动画控件</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">baseLayerPicker</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 图层选择器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">fullscreenButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 全屏按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">vrButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">            </span><span style="color:#676E95;font-style:italic;">// VR按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">geocoder</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">            </span><span style="color:#676E95;font-style:italic;">// 搜索框</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">homeButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">          </span><span style="color:#676E95;font-style:italic;">// 主页按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">infoBox</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">             </span><span style="color:#676E95;font-style:italic;">// 信息框</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">sceneModePicker</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 场景模式选择器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">selectionIndicator</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 选择指示器</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">timeline</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">            </span><span style="color:#676E95;font-style:italic;">// 时间轴</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">navigationHelpButton</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 导航帮助按钮</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">navigationInstructionsInitiallyVisible</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="地形配置" tabindex="-1">地形配置 <a class="header-anchor" href="#地形配置" aria-label="Permalink to &quot;地形配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 使用Cesium World Terrain</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">terrainProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createWorldTerrain</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">requestWaterMask</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 请求水体遮罩</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">requestVertexNormals</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 请求顶点法线</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用自定义地形</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">terrainProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">CesiumTerrainProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-terrain-server-url</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 不使用地形（平面）</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">terrainProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">EllipsoidTerrainProvider</span><span style="color:#BABED8;">()</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="影像图层配置" tabindex="-1">影像图层配置 <a class="header-anchor" href="#影像图层配置" aria-label="Permalink to &quot;影像图层配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 使用Bing Maps</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">imageryProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">BingMapsImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://dev.virtualearth.net</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">key</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-bing-maps-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">mapStyle</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BingMapsStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">AERIAL_WITH_LABELS</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用OpenStreetMap</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">imageryProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">OpenStreetMapImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://a.tile.openstreetmap.org/</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用天地图</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">imageryProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">WebMapTileServiceImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&amp;REQUEST=GetTile&amp;VERSION=1.0.0&amp;LAYER=img&amp;STYLE=default&amp;TILEMATRIXSET=w&amp;FORMAT=tiles&amp;TILEMATRIX={TileMatrix}&amp;TILEROW={TileRow}&amp;TILECOL={TileCol}&amp;tk=your-tianditu-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">layer</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">img</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">style</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">default</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">format</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">tiles</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">tileMatrixSetID</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">w</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="初始视角设置" tabindex="-1">初始视角设置 <a class="header-anchor" href="#初始视角设置" aria-label="Permalink to &quot;初始视角设置&quot;">​</a></h2><h3 id="设置初始相机位置" tabindex="-1">设置初始相机位置 <a class="header-anchor" href="#设置初始相机位置" aria-label="Permalink to &quot;设置初始相机位置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建viewer后设置初始位置</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setView</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">destination</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1500</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 经度、纬度、高度</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">orientation</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">heading</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 航向角</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pitch</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">45</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 俯仰角</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">roll</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.0</span><span style="color:#BABED8;">                             </span><span style="color:#676E95;font-style:italic;">// 翻滚角</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 或者使用flyTo实现平滑过渡</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">flyTo</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">destination</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1500</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">orientation</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">heading</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pitch</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">45</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">roll</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.0</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">duration</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">3</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 飞行时间（秒）</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="设置默认主页位置" tabindex="-1">设置默认主页位置 <a class="header-anchor" href="#设置默认主页位置" aria-label="Permalink to &quot;设置默认主页位置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 设置主页按钮的默认位置</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">homeButton</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">viewModel</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">command</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">beforeExecute</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">e</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">e</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">cancel</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">flyTo</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    destination</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10000</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="场景配置" tabindex="-1">场景配置 <a class="header-anchor" href="#场景配置" aria-label="Permalink to &quot;场景配置&quot;">​</a></h2><h3 id="光照配置" tabindex="-1">光照配置 <a class="header-anchor" href="#光照配置" aria-label="Permalink to &quot;光照配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 启用阴影</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">shadows </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 配置太阳光照</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enableLighting </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 配置环境光</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">atmosphere</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">brightnessShift </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.4</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="性能配置" tabindex="-1">性能配置 <a class="header-anchor" href="#性能配置" aria-label="Permalink to &quot;性能配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 启用抗锯齿</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">postProcessStages</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">fxaa</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enabled </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 配置最大屏幕空间误差</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">maximumScreenSpaceError </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 配置瓦片缓存大小</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">tileCacheSize </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 启用渐进式渲染</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">requestRenderMode </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">maximumRenderTimeChange </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">Infinity;</span></span></code></pre></div><h3 id="相机控制配置" tabindex="-1">相机控制配置 <a class="header-anchor" href="#相机控制配置" aria-label="Permalink to &quot;相机控制配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 限制相机缩放范围</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">minimumZoomDistance </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">maximumZoomDistance </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">50000</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 限制相机俯仰角</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">constrainedAxis </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">UNIT_Z</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 禁用地形碰撞检测</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enableCollisionDetection </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 配置惯性</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">inertiaSpin </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.9</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">inertiaTranslate </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.9</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">inertiaZoom </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="样式配置" tabindex="-1">样式配置 <a class="header-anchor" href="#样式配置" aria-label="Permalink to &quot;样式配置&quot;">​</a></h2><h3 id="自定义css样式" tabindex="-1">自定义CSS样式 <a class="header-anchor" href="#自定义css样式" aria-label="Permalink to &quot;自定义CSS样式&quot;">​</a></h3><div class="language-css"><button title="Copy Code" class="copy"></button><span class="lang">css</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">/* 隐藏Cesium信用信息 */</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">cesium-widget-credits</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">display</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> none </span><span style="color:#F78C6C;">!important</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">/* 自定义加载动画 */</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">cesium-viewer-loadingIndicator</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">background-color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">rgba</span><span style="color:#89DDFF;">(</span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">);</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">/* 自定义工具栏样式 */</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">cesium-viewer-toolbar</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">background-color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">rgba</span><span style="color:#89DDFF;">(</span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">);</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">border-radius</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5px</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">/* 自定义时间轴样式 */</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">cesium-viewer-timelineContainer</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">background-color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">rgba</span><span style="color:#89DDFF;">(</span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">);</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">/* 自定义动画控件样式 */</span></span>
<span class="line"><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">cesium-viewer-animationContainer</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">background-color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">rgba</span><span style="color:#89DDFF;">(</span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">42</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">);</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">border-radius</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5px</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="html容器配置" tabindex="-1">HTML容器配置 <a class="header-anchor" href="#html容器配置" aria-label="Permalink to &quot;HTML容器配置&quot;">​</a></h3><div class="language-html"><button title="Copy Code" class="copy"></button><span class="lang">html</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">id</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;"> </span><span style="color:#C792EA;">style</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">width: 100%; height: 100vh;</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">&gt;&lt;/</span><span style="color:#F07178;">div</span><span style="color:#89DDFF;">&gt;</span></span></code></pre></div><div class="language-css"><button title="Copy Code" class="copy"></button><span class="lang">css</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">#</span><span style="color:#F78C6C;">cesiumContainer</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">width</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">100%</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">height</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">100vh</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">margin</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">padding</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">overflow</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> hidden</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">font-family</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> sans-serif</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">/* 确保容器父元素也有正确的样式 */</span></span>
<span class="line"><span style="color:#FFCB6B;">html</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">body</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">width</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">100%</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">height</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">100%</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">margin</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#B2CCD6;">padding</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="常见问题" tabindex="-1">常见问题 <a class="header-anchor" href="#常见问题" aria-label="Permalink to &quot;常见问题&quot;">​</a></h2><h3 id="_1-黑屏或空白" tabindex="-1">1. 黑屏或空白 <a class="header-anchor" href="#_1-黑屏或空白" aria-label="Permalink to &quot;1. 黑屏或空白&quot;">​</a></h3><p><strong>问题原因：</strong></p><ul><li>Ion访问令牌未配置或无效</li><li>网络连接问题</li><li>容器尺寸为0</li></ul><p><strong>解决方案：</strong></p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 检查令牌是否设置</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Access Token:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Ion</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">defaultAccessToken)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 确保容器有正确的尺寸</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> container </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> document</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">getElementById</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Container size:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> container</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">offsetWidth</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> container</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">offsetHeight)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 监听viewer准备完成事件</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">tileLoadProgressEvent</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">value</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Tile load progress:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">value</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="_2-性能问题" tabindex="-1">2. 性能问题 <a class="header-anchor" href="#_2-性能问题" aria-label="Permalink to &quot;2. 性能问题&quot;">​</a></h3><p><strong>优化建议：</strong></p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 减少最大屏幕空间误差</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">maximumScreenSpaceError </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">4</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 启用请求渲染模式</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">requestRenderMode </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 禁用不必要的效果</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">fog</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enabled </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">skyAtmosphere</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="_3-跨域问题" tabindex="-1">3. 跨域问题 <a class="header-anchor" href="#_3-跨域问题" aria-label="Permalink to &quot;3. 跨域问题&quot;">​</a></h3><p><strong>解决方案：</strong></p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 配置代理或使用CORS友好的服务</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> imageryProvider </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">WebMapServiceImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">/api/proxy/wms</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 通过代理访问</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">layers</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-layer</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div>`,55),e=[o];function t(c,r,D,y,F,i){return a(),n("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
