# 全局指令

本章节介绍项目中可用的全局自定义指令，这些指令可以帮助简化开发流程，提高代码复用性，解决常见的DOM操作和UI交互问题。

## 指令使用方法

Vue自定义指令可以直接在模板中使用，语法为 `v-指令名称`。如果指令需要参数，可以使用 `v-指令名称:参数` 的形式。

```vue
<template>
  <!-- 基本用法 -->
  <div v-example></div>
  
  <!-- 带参数的用法 -->
  <div v-example:param="value"></div>
</template>
```

## 指令分类

### 表格相关指令

| 指令名称 | 功能描述 | 链接 |
|---------|---------|------|
| v-table-height | 使表格高度自适应容器高度 | [查看详情](./table-height) |

### 权限控制指令

| 指令名称 | 功能描述 | 链接 |
|---------|---------|------|
| v-permission | 根据用户权限控制元素显示/隐藏 | [查看详情](./permission) |

### 防抖节流指令

| 指令名称 | 功能描述 | 链接 |
|---------|---------|------|
| v-debounce | 为事件添加防抖功能 | [查看详情](./debounce) |
| v-throttle | 为事件添加节流功能 | [查看详情](./throttle) |

### 交互指令

| 指令名称 | 功能描述 | 链接 |
|---------|---------|------|
| v-drag-dialog | 让 Element UI 的 `el-dialog` 支持拖拽移动 | [查看详情](./drag-dialog) |
| v-copy | 提供一键复制文本功能 | [查看详情](./copy) |


## 指令注册

在项目中使用这些指令前，需要先进行注册。可以选择全局注册或局部注册：

### 全局注册

在项目的入口文件（如 `main.js`）中注册：

```js
import Vue from 'vue';
import tableHeight from '@/directives/table-height';
import Permission from '@/directives/permission';
import Debounce from '@/directives/debounce';
import Throttle from '@/directives/throttle';
import Copy from '@/directives/copy';
import DragDialog from '@/directives/drag-dialog';

// 注册全局指令
Vue.directive('table-height', tableHeight);
Vue.directive('permission', Permission);
Vue.directive('debounce', Debounce);
Vue.directive('throttle', Throttle);
Vue.directive('copy', Copy);
Vue.directive('drag-dialog', DragDialog);
```

### 局部注册

在组件中局部注册：

```vue
<script>
import tableHeight from '@/directives/table-height';

export default {
  directives: {
    'table-height': tableHeight
  }
}
</script>
```


## 自定义指令开发指南

如需开发新的自定义指令，请遵循以下步骤：

1. 在 `src/directives` 目录下创建新的指令文件
2. 实现指令的钩子函数（`bind`、`inserted`、`update`、`componentUpdated`、`unbind`）
3. 编写完整的文档和使用示例
4. 在全局指令注册文件中注册新指令

### 指令钩子函数说明

```js
// 指令钩子函数说明
export default {
  // 只调用一次，指令第一次绑定到元素时调用
  bind(el, binding, vnode) {
    // el: 指令所绑定的元素，可以用来直接操作 DOM
    // binding: 包含指令相关信息的对象，如:
    //   - name: 指令名，不包括 v- 前缀
    //   - value: 指令的绑定值
    //   - oldValue: 指令绑定的前一个值，仅在 update 和 componentUpdated 钩子中可用
    //   - expression: 字符串形式的指令表达式
    //   - arg: 传给指令的参数，可选
    //   - modifiers: 包含修饰符的对象
    // vnode: Vue 编译生成的虚拟节点
  },
  
  // 被绑定元素插入父节点时调用
  inserted(el, binding, vnode) {
    // 此时可以访问父节点
  },
  
  // 所在组件的 VNode 更新时调用，但可能发生在其子 VNode 更新之前
  update(el, binding, vnode, oldVnode) {
    // oldVnode: 上一个虚拟节点
  },
  
  // 指令所在组件的 VNode 及其子 VNode 全部更新后调用
  componentUpdated(el, binding, vnode, oldVnode) {
    // 此时所有子组件都已更新完毕
  },
  
  // 只调用一次，指令与元素解绑时调用
  unbind(el, binding, vnode) {
    // 清理工作，如移除事件监听器
  }
}
```

指令开发应遵循以下原则：

- 专注于DOM操作和行为控制
- 保持指令功能的单一性
- 提供合理的默认值和配置选项
- 注意内存泄漏问题，在`unbind`钩子中清理事件监听和定时器
