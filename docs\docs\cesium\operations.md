# 常用操作

本章介绍Cesium开发中的常用操作，包括相机控制、图层管理、实体操作、事件处理等实用功能。

## 相机控制

### 基本相机操作

```javascript
const camera = viewer.camera;

// 设置相机位置
camera.setView({
  destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 1500),
  orientation: {
    heading: Cesium.Math.toRadians(0),    // 航向角
    pitch: Cesium.Math.toRadians(-45),    // 俯仰角
    roll: 0.0                             // 翻滚角
  }
});

// 相机飞行到目标位置
camera.flyTo({
  destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 5000),
  orientation: {
    heading: Cesium.Math.toRadians(45),
    pitch: Cesium.Math.toRadians(-30),
    roll: 0.0
  },
  duration: 3.0 // 飞行时间（秒）
});
```

### 相机环绕

```javascript
// 环绕目标点
const center = Cesium.Cartesian3.fromDegrees(116.391, 39.904);
camera.lookAt(
  center,
  new Cesium.HeadingPitchRange(
    Cesium.Math.toRadians(0),     // heading
    Cesium.Math.toRadians(-45),   // pitch
    5000                          // range
  )
);

// 取消环绕模式
camera.lookAtTransform(Cesium.Matrix4.IDENTITY);
```

### 相机动画

```javascript
// 创建相机飞行路径
const flightPath = [
  { position: [116.391, 39.904, 1000], duration: 2 },
  { position: [116.395, 39.908, 1500], duration: 3 },
  { position: [116.400, 39.915, 2000], duration: 4 }
];

// 执行飞行动画
function flyToNextPosition(index) {
  if (index >= flightPath.length) return;
  
  const point = flightPath[index];
  camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(...point.position),
    duration: point.duration,
    complete: () => {
      // 飞行完成后继续下一个点
      setTimeout(() => flyToNextPosition(index + 1), 1000);
    }
  });
}

flyToNextPosition(0);
```

### 相机限制

```javascript
// 限制相机缩放范围
const controller = viewer.scene.screenSpaceCameraController;
controller.minimumZoomDistance = 100;   // 最小缩放距离
controller.maximumZoomDistance = 50000; // 最大缩放距离

// 限制相机移动范围
const rectangle = Cesium.Rectangle.fromDegrees(
  115.0, 38.0,  // 西南角
  118.0, 41.0   // 东北角
);
camera.constrainedAxis = Cesium.Cartesian3.UNIT_Z;
controller.enableCollisionDetection = false;
```

## 图层管理

### 影像图层操作

```javascript
// 添加影像图层
const imageryProvider = new Cesium.BingMapsImageryProvider({
  url: 'https://dev.virtualearth.net',
  key: 'your-bing-maps-key',
  mapStyle: Cesium.BingMapsStyle.AERIAL
});

const imageryLayer = viewer.imageryLayers.addImageryProvider(imageryProvider);

// 调整图层属性
imageryLayer.alpha = 0.8;       // 透明度
imageryLayer.brightness = 1.2;  // 亮度
imageryLayer.contrast = 1.1;    // 对比度
imageryLayer.saturation = 1.3;  // 饱和度
imageryLayer.gamma = 1.0;       // 伽马值

// 移除图层
viewer.imageryLayers.remove(imageryLayer);

// 图层排序
viewer.imageryLayers.raise(imageryLayer);    // 上移
viewer.imageryLayers.lower(imageryLayer);    // 下移
viewer.imageryLayers.raiseToTop(imageryLayer);  // 移到顶部
viewer.imageryLayers.lowerToBottom(imageryLayer); // 移到底部
```

### 多图层管理

```javascript
// 创建图层管理器
class LayerManager {
  constructor(viewer) {
    this.viewer = viewer;
    this.layers = new Map();
  }
  
  // 添加图层
  addLayer(id, provider, options = {}) {
    const layer = this.viewer.imageryLayers.addImageryProvider(provider);
    
    // 应用选项
    Object.assign(layer, options);
    
    this.layers.set(id, layer);
    return layer;
  }
  
  // 移除图层
  removeLayer(id) {
    const layer = this.layers.get(id);
    if (layer) {
      this.viewer.imageryLayers.remove(layer);
      this.layers.delete(id);
    }
  }
  
  // 显示/隐藏图层
  toggleLayer(id, visible) {
    const layer = this.layers.get(id);
    if (layer) {
      layer.show = visible;
    }
  }
  
  // 设置图层透明度
  setLayerAlpha(id, alpha) {
    const layer = this.layers.get(id);
    if (layer) {
      layer.alpha = alpha;
    }
  }
}

// 使用示例
const layerManager = new LayerManager(viewer);

// 添加多个图层
layerManager.addLayer('satellite', new Cesium.BingMapsImageryProvider({
  url: 'https://dev.virtualearth.net',
  key: 'your-key',
  mapStyle: Cesium.BingMapsStyle.AERIAL
}), { alpha: 0.8 });

layerManager.addLayer('roads', new Cesium.BingMapsImageryProvider({
  url: 'https://dev.virtualearth.net',
  key: 'your-key',
  mapStyle: Cesium.BingMapsStyle.ROAD
}), { alpha: 0.5 });
```

## 实体操作

### 批量创建实体

```javascript
// 创建大量点实体
function createRandomPoints(count) {
  const points = [];
  
  // 暂停事件以提高性能
  viewer.entities.suspendEvents();
  
  for (let i = 0; i < count; i++) {
    const longitude = 116 + Math.random() * 2;
    const latitude = 39 + Math.random() * 2;
    
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      point: {
        pixelSize: 8,
        color: Cesium.Color.fromRandom({ alpha: 0.8 }),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2
      },
      label: {
        text: `点${i + 1}`,
        font: '12pt sans-serif',
        pixelOffset: new Cesium.Cartesian2(0, -40),
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE
      }
    });
    
    points.push(entity);
  }
  
  // 恢复事件
  viewer.entities.resumeEvents();
  
  return points;
}

// 创建1000个随机点
const points = createRandomPoints(1000);
```

### 实体分组管理

```javascript
// 创建实体管理器
class EntityManager {
  constructor(viewer) {
    this.viewer = viewer;
    this.groups = new Map();
  }
  
  // 创建分组
  createGroup(groupId) {
    if (!this.groups.has(groupId)) {
      this.groups.set(groupId, []);
    }
  }
  
  // 添加实体到分组
  addToGroup(groupId, entity) {
    if (!this.groups.has(groupId)) {
      this.createGroup(groupId);
    }
    this.groups.get(groupId).push(entity);
  }
  
  // 显示/隐藏分组
  toggleGroup(groupId, visible) {
    const entities = this.groups.get(groupId);
    if (entities) {
      entities.forEach(entity => {
        entity.show = visible;
      });
    }
  }
  
  // 删除分组
  removeGroup(groupId) {
    const entities = this.groups.get(groupId);
    if (entities) {
      entities.forEach(entity => {
        this.viewer.entities.remove(entity);
      });
      this.groups.delete(groupId);
    }
  }
  
  // 获取分组实体
  getGroup(groupId) {
    return this.groups.get(groupId) || [];
  }
}

// 使用示例
const entityManager = new EntityManager(viewer);

// 创建不同类型的实体分组
entityManager.createGroup('hospitals');
entityManager.createGroup('schools');
entityManager.createGroup('parks');

// 添加医院标记
const hospital = viewer.entities.add({
  position: Cesium.Cartesian3.fromDegrees(116.391, 39.904),
  billboard: {
    image: 'images/hospital.png',
    scale: 0.5
  }
});
entityManager.addToGroup('hospitals', hospital);
```

### 实体样式动态更新

```javascript
// 创建动态样式实体
const dynamicEntity = viewer.entities.add({
  position: Cesium.Cartesian3.fromDegrees(116.391, 39.904),
  point: {
    pixelSize: 10,
    color: Cesium.Color.YELLOW
  }
});

// 创建样式动画
function animateEntityColor(entity) {
  let hue = 0;
  
  setInterval(() => {
    hue = (hue + 1) % 360;
    const color = Cesium.Color.fromHsl(hue / 360, 1.0, 0.5);
    entity.point.color = color;
  }, 50);
}

animateEntityColor(dynamicEntity);

// 根据数据动态更新实体样式
function updateEntityByData(entity, data) {
  // 根据数值设置颜色
  const value = data.value;
  let color;
  
  if (value > 80) {
    color = Cesium.Color.RED;
  } else if (value > 60) {
    color = Cesium.Color.ORANGE;
  } else if (value > 40) {
    color = Cesium.Color.YELLOW;
  } else {
    color = Cesium.Color.GREEN;
  }
  
  entity.point.color = color;
  entity.point.pixelSize = Math.max(5, value / 10);
  
  // 更新标签
  entity.label = {
    text: `${data.name}: ${value}`,
    font: '14pt sans-serif',
    fillColor: Cesium.Color.WHITE,
    outlineColor: Cesium.Color.BLACK,
    outlineWidth: 2,
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    pixelOffset: new Cesium.Cartesian2(0, -40)
  };
}
```

## 事件处理

### 鼠标事件处理

```javascript
// 创建事件处理器
const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

// 左键点击事件
handler.setInputAction(function(event) {
  const pickedObject = viewer.scene.pick(event.position);
  
  if (Cesium.defined(pickedObject)) {
    // 点击到实体
    console.log('点击实体:', pickedObject.id);
    
    // 高亮显示
    if (pickedObject.id instanceof Cesium.Entity) {
      pickedObject.id.point.color = Cesium.Color.RED;
      pickedObject.id.point.pixelSize = 15;
    }
  } else {
    // 点击地面
    const cartesian = viewer.camera.pickEllipsoid(
      event.position, 
      viewer.scene.globe.ellipsoid
    );
    
    if (cartesian) {
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);
      
      console.log(`点击位置: ${longitude.toFixed(6)}, ${latitude.toFixed(6)}`);
      
      // 在点击位置添加标记
      viewer.entities.add({
        position: cartesian,
        point: {
          pixelSize: 10,
          color: Cesium.Color.CYAN,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        }
      });
    }
  }
}, Cesium.ScreenSpaceEventType.LEFT_CLICK);

// 右键点击事件
handler.setInputAction(function(event) {
  const pickedObject = viewer.scene.pick(event.position);
  
  if (Cesium.defined(pickedObject) && pickedObject.id instanceof Cesium.Entity) {
    // 删除实体
    viewer.entities.remove(pickedObject.id);
  }
}, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

// 鼠标移动事件
handler.setInputAction(function(event) {
  const pickedObject = viewer.scene.pick(event.endPosition);
  
  // 重置所有实体样式
  viewer.entities.values.forEach(entity => {
    if (entity.point) {
      entity.point.scale = 1.0;
    }
  });
  
  // 高亮悬停的实体
  if (Cesium.defined(pickedObject) && pickedObject.id instanceof Cesium.Entity) {
    if (pickedObject.id.point) {
      pickedObject.id.point.scale = 1.5;
    }
    
    // 修改鼠标样式
    viewer.canvas.style.cursor = 'pointer';
  } else {
    viewer.canvas.style.cursor = 'default';
  }
}, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
```

### 键盘事件处理

```javascript
// 监听键盘事件
document.addEventListener('keydown', function(event) {
  switch(event.code) {
    case 'KeyH': // H键 - 回到初始位置
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 10000)
      });
      break;
      
    case 'KeyC': // C键 - 清除所有实体
      viewer.entities.removeAll();
      break;
      
    case 'KeyS': // S键 - 截图
      viewer.render();
      const canvas = viewer.scene.canvas;
      const image = canvas.toDataURL('image/png');
      
      // 下载截图
      const link = document.createElement('a');
      link.download = 'cesium-screenshot.png';
      link.href = image;
      link.click();
      break;
      
    case 'Digit1': // 1键 - 2D模式
      viewer.scene.morphTo2D();
      break;
      
    case 'Digit2': // 2键 - Columbus View模式
      viewer.scene.morphToColumbusView();
      break;
      
    case 'Digit3': // 3键 - 3D模式
      viewer.scene.morphTo3D();
      break;
  }
});
```

## 数据加载与显示

### GeoJSON数据加载

```javascript
// 加载GeoJSON数据
async function loadGeoJsonData(url) {
  try {
    const dataSource = await Cesium.GeoJsonDataSource.load(url, {
      stroke: Cesium.Color.HOTPINK,
      fill: Cesium.Color.PINK.withAlpha(0.5),
      strokeWidth: 3
    });
    
    viewer.dataSources.add(dataSource);
    
    // 飞行到数据范围
    viewer.flyTo(dataSource);
    
    return dataSource;
  } catch (error) {
    console.error('加载GeoJSON数据失败:', error);
  }
}

// 使用示例
loadGeoJsonData('data/boundaries.geojson');
```

### 动态数据更新

```javascript
// 模拟实时数据更新
class RealTimeDataManager {
  constructor(viewer) {
    this.viewer = viewer;
    this.entities = new Map();
    this.isRunning = false;
  }
  
  // 开始实时更新
  start() {
    if (this.isRunning) return;
    this.isRunning = true;
    this.update();
  }
  
  // 停止实时更新
  stop() {
    this.isRunning = false;
  }
  
  // 更新数据
  update() {
    if (!this.isRunning) return;
    
    // 模拟从服务器获取数据
    this.fetchData().then(data => {
      this.updateEntities(data);
      
      // 继续下次更新
      setTimeout(() => this.update(), 1000);
    });
  }
  
  // 模拟数据获取
  async fetchData() {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 生成模拟数据
    return Array.from({ length: 10 }, (_, i) => ({
      id: `device_${i}`,
      longitude: 116.391 + (Math.random() - 0.5) * 0.01,
      latitude: 39.904 + (Math.random() - 0.5) * 0.01,
      value: Math.random() * 100,
      status: Math.random() > 0.8 ? 'alarm' : 'normal'
    }));
  }
  
  // 更新实体
  updateEntities(data) {
    data.forEach(item => {
      let entity = this.entities.get(item.id);
      
      if (!entity) {
        // 创建新实体
        entity = this.viewer.entities.add({
          id: item.id,
          position: Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude),
          point: {
            pixelSize: 10,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          },
          label: {
            font: '12pt sans-serif',
            pixelOffset: new Cesium.Cartesian2(0, -40),
            fillColor: Cesium.Color.WHITE,
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 1,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE
          }
        });
        
        this.entities.set(item.id, entity);
      }
      
      // 更新位置和样式
      entity.position = Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude);
      entity.point.color = item.status === 'alarm' ? Cesium.Color.RED : Cesium.Color.GREEN;
      entity.label.text = `${item.id}: ${item.value.toFixed(1)}`;
    });
  }
}

// 使用示例
const dataManager = new RealTimeDataManager(viewer);
dataManager.start();

// 5分钟后停止更新
setTimeout(() => dataManager.stop(), 5 * 60 * 1000);
```

## 测量工具

### 距离测量

```javascript
class DistanceMeasure {
  constructor(viewer) {
    this.viewer = viewer;
    this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
    this.points = [];
    this.polyline = null;
    this.labels = [];
    this.isActive = false;
  }
  
  // 激活测量
  activate() {
    if (this.isActive) return;
    this.isActive = true;
    this.viewer.canvas.style.cursor = 'crosshair';
    
    this.handler.setInputAction((event) => {
      this.addPoint(event.position);
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
    
    this.handler.setInputAction(() => {
      this.finish();
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  
  // 添加测量点
  addPoint(screenPosition) {
    const cartesian = this.viewer.camera.pickEllipsoid(
      screenPosition,
      this.viewer.scene.globe.ellipsoid
    );
    
    if (!cartesian) return;
    
    this.points.push(cartesian);
    
    // 添加点标记
    this.viewer.entities.add({
      position: cartesian,
      point: {
        pixelSize: 8,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      }
    });
    
    // 更新线段
    this.updatePolyline();
    
    // 计算并显示距离
    if (this.points.length > 1) {
      this.updateDistanceLabels();
    }
  }
  
  // 更新线段
  updatePolyline() {
    if (this.polyline) {
      this.viewer.entities.remove(this.polyline);
    }
    
    if (this.points.length > 1) {
      this.polyline = this.viewer.entities.add({
        polyline: {
          positions: this.points,
          width: 2,
          material: Cesium.Color.YELLOW,
          clampToGround: true
        }
      });
    }
  }
  
  // 更新距离标签
  updateDistanceLabels() {
    // 清除旧标签
    this.labels.forEach(label => this.viewer.entities.remove(label));
    this.labels = [];
    
    let totalDistance = 0;
    
    for (let i = 1; i < this.points.length; i++) {
      const distance = Cesium.Cartesian3.distance(this.points[i - 1], this.points[i]);
      totalDistance += distance;
      
      // 计算中点位置
      const midpoint = Cesium.Cartesian3.midpoint(
        this.points[i - 1],
        this.points[i],
        new Cesium.Cartesian3()
      );
      
      // 添加距离标签
      const label = this.viewer.entities.add({
        position: midpoint,
        label: {
          text: `${(distance / 1000).toFixed(2)} km`,
          font: '14pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -20)
        }
      });
      
      this.labels.push(label);
    }
    
    // 显示总距离
    if (this.points.length > 1) {
      const lastPoint = this.points[this.points.length - 1];
      const totalLabel = this.viewer.entities.add({
        position: lastPoint,
        label: {
          text: `总距离: ${(totalDistance / 1000).toFixed(2)} km`,
          font: '16pt sans-serif',
          fillColor: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -40)
        }
      });
      
      this.labels.push(totalLabel);
    }
  }
  
  // 完成测量
  finish() {
    this.isActive = false;
    this.viewer.canvas.style.cursor = 'default';
    this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
    this.handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
  
  // 清除测量结果
  clear() {
    this.points = [];
    
    if (this.polyline) {
      this.viewer.entities.remove(this.polyline);
      this.polyline = null;
    }
    
    this.labels.forEach(label => this.viewer.entities.remove(label));
    this.labels = [];
    
    this.finish();
  }
}

// 使用示例
const distanceMeasure = new DistanceMeasure(viewer);

// 激活距离测量
distanceMeasure.activate();

// 清除测量结果
// distanceMeasure.clear();
```

## 下一步

- [核心概念](/cesium/concepts) - 回顾Cesium的核心概念和架构