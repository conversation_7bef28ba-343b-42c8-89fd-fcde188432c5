# 公司前端技术文档

基于VitePress构建的内部前端技术文档站点，用于规范前端开发流程、技术标准和组件使用。

## 简介

本文档站点包含以下内容：

- 开发指南 - 快速入门、项目结构和开发流程
- 组件库 - 基础组件、业务组件和自定义组件
- 最佳实践 - 性能优化、代码复用和状态管理
- 规范标准 - 编码规范、Git工作流和代码审查
- 工具配置 - ESLint、Prettier和Vscode等配置

## 安装与使用

### 环境要求

- Node.js v18+
- npm v9+

### 安装依赖

```bash
# 克隆仓库
git clone <repository-url>

# 进入项目目录
cd fe-docs

# 安装依赖
npm install
```

### 本地开发

```bash
# 启动开发服务器
npm run dev
```

开发服务器启动后，访问 http://localhost:5566 查看文档站点。

### 构建静态文件

```bash
# 构建静态站点
npm run build
```

构建完成后，静态文件将生成在 `docs/.vitepress/dist` 目录中。

### 预览构建结果

```bash
# 预览构建后的站点
npm run serve
```

## 文档维护

### 目录结构

```
docs/
├── .vitepress/       # VitePress配置
├── guide/            # 开发指南
├── components/       # 组件文档
├── best-practices/   # 最佳实践
├── standards/        # 规范标准
└── index.md          # 首页
```

### 新增文档

1. 在相应目录下创建Markdown文件
2. 在`.vitepress/config.js`中更新导航和侧边栏配置

### 编写规范

- 使用Markdown格式编写文档
- 文件名使用kebab-case（如`git-workflow.md`）
- 每个文档页面顶部使用一级标题(`# 标题`)
- 代码示例使用对应的语言标记（如` ```js`）

## 部署

本文档站点可以部署到公司内部服务器或使用GitLab/GitHub Pages进行托管。

### 服务器部署

1. 执行`npm run build`构建静态文件
2. 将`docs/.vitepress/dist`目录下的文件复制到Web服务器根目录

### GitLab Pages部署

在`.gitlab-ci.yml`中配置自动构建和部署流程。

## 贡献指南

1. 创建分支：`git checkout -b feature/your-feature-name`
2. 提交更改：`git commit -m 'feat: add some feature'`
3. 推送分支：`git push origin feature/your-feature-name`
4. 提交合并请求

## 联系方式

如有问题或建议，请联系前端团队负责人。
