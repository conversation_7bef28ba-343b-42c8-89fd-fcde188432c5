# v-debounce

为事件添加防抖功能，避免事件在短时间内多次触发。

## 使用场景

- 搜索输入框实时搜索
- 表单提交按钮防止重复点击
- 窗口大小调整事件处理

## 基本用法

```vue
<template>
  <el-input 
    v-model="searchText"
    v-debounce:input="{ fn: handleSearch, delay: 500 }"
    placeholder="请输入搜索内容"
  ></el-input>
</template>

<script>
export default {
  methods: {
    handleSearch() {
      console.log('搜索内容：', this.searchText);
      // 执行搜索逻辑
    }
  }
}
</script>
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| fn | Function | - | 需要防抖处理的函数 |
| delay | Number | 300 | 防抖延迟时间（单位：ms） |

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directives/debounce.js
export default {
  bind(el, binding, vnode) {
    // 获取事件类型
    const event = binding.arg || 'click';
    // 获取传入的配置
    const { fn, delay = 300 } = binding.value || {};
    
    if (typeof fn !== 'function') {
      console.warn(`v-debounce指令需要一个函数作为参数`);
      return;
    }
    
    // 存储定时器ID
    let timer = null;
    
    // 创建事件处理函数
    const handler = function(...args) {
      // 清除之前的定时器
      if (timer) clearTimeout(timer);
      
      // 设置新的定时器
      timer = setTimeout(() => {
        // 调用传入的函数，并保持this上下文
        fn.apply(vnode.context, args);
      }, delay);
    };
    
    // 保存事件处理函数，用于解绑
    el._debounceHandler = handler;
    
    // 绑定事件
    el.addEventListener(event, handler);
  },
  
  // 当传入的值变化时
  update(el, binding) {
    // 获取事件类型
    const event = binding.arg || 'click';
    // 获取新的配置
    const { fn, delay = 300 } = binding.value || {};
    
    // 如果函数变了，需要重新绑定
    if (el._debounceHandler && binding.oldValue && binding.oldValue.fn !== fn) {
      // 先解绑旧的
      el.removeEventListener(event, el._debounceHandler);
      
      // 创建新的处理函数
      let timer = null;
      const handler = function(...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, args);
        }, delay);
      };
      
      // 更新保存的处理函数
      el._debounceHandler = handler;
      
      // 重新绑定事件
      el.addEventListener(event, handler);
    }
  },
  
  // 指令与元素解绑时
  unbind(el, binding) {
    // 获取事件类型
    const event = binding.arg || 'click';
    
    // 如果存在处理函数，则解绑事件
    if (el._debounceHandler) {
      el.removeEventListener(event, el._debounceHandler);
      delete el._debounceHandler;
    }
  }
};
```
</details>
