# HTML编码规范

本文档定义了项目中HTML代码的编写规范，旨在保持代码的一致性、可读性和可维护性，同时确保良好的语义化和可访问性。

## 文档类型

- 使用HTML5文档类型
- 在文档开头使用 `<!DOCTYPE html>` 声明

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <title>页面标题</title>
  </head>
  <body>
    <!-- 页面内容 -->
  </body>
</html>
```

## 语言属性

- 为 `<html>` 标签添加 `lang` 属性
- 指定正确的语言代码（如中文页面使用 `zh-CN`）

```html
<html lang="zh-CN">
```

## 字符编码

- 使用 UTF-8 字符编码
- 在 `<head>` 中明确声明字符编码

```html
<meta charset="UTF-8">
```

## 元数据

- 添加适当的元数据标签
- 包括视口设置、页面描述和关键词

```html
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="页面描述">
<meta name="keywords" content="关键词1, 关键词2">
<meta name="author" content="作者">
```

## 缩进与格式

- 使用2个空格进行缩进
- 嵌套元素应当缩进一级
- 保持代码的清晰和一致性

```html
<div class="container">
  <header class="header">
    <h1>标题</h1>
    <nav>
      <ul>
        <li><a href="#">链接1</a></li>
        <li><a href="#">链接2</a></li>
      </ul>
    </nav>
  </header>
</div>
```

## 标签使用

### 标签闭合

- 所有HTML标签必须正确闭合
- 自闭合标签使用 `/>`（如 `<img />`, `<input />`, `<br />`）

```html
<!-- 好的做法 -->
<div>
  <p>段落内容</p>
</div>
<img src="image.jpg" alt="图片描述" />

<!-- 不好的做法 -->
<div>
  <p>段落内容
</div>
<img src="image.jpg">
```

### 标签小写

- 所有HTML标签使用小写
- 属性名使用小写

```html
<!-- 好的做法 -->
<div class="container">
  <a href="#" class="link">链接</a>
</div>

<!-- 不好的做法 -->
<DIV CLASS="container">
  <A HREF="#" CLASS="link">链接</A>
</DIV>
```

## 属性规范

### 属性顺序

属性应按以下顺序排列：

1. `id`
2. `class`
3. `name`
4. `data-*`
5. `src`, `href`, `for`, `type`
6. `title`, `alt`
7. `aria-*`, `role`
8. `required`, `readonly`, `disabled`

```html
<a id="link" class="button" data-toggle="modal" href="https://example.com" title="打开链接">链接</a>
```

### 属性值

- 属性值使用双引号（"）
- 布尔属性不需要指定值

```html
<input type="text" disabled />
<input type="checkbox" checked />
<select>
  <option selected>选项</option>
</select>
```

### 自定义属性

- 使用 `data-*` 前缀定义自定义属性
- 属性名使用小写，多个单词用连字符（-）分隔

```html
<div data-user-id="123" data-role="admin">用户信息</div>
```

## 语义化

### 使用语义化标签

- 使用HTML5语义化标签表达文档结构
- 根据内容的含义选择合适的标签

```html
<header>
  <h1>网站标题</h1>
  <nav>
    <ul>
      <li><a href="#">首页</a></li>
      <li><a href="#">关于</a></li>
    </ul>
  </nav>
</header>

<main>
  <article>
    <h2>文章标题</h2>
    <p>文章内容...</p>
    <section>
      <h3>章节标题</h3>
      <p>章节内容...</p>
    </section>
  </article>
  
  <aside>
    <h3>相关内容</h3>
    <ul>
      <li><a href="#">相关链接1</a></li>
      <li><a href="#">相关链接2</a></li>
    </ul>
  </aside>
</main>

<footer>
  <p>&copy; 2025 公司名称</p>
</footer>
```

### 标题层级

- 正确使用标题标签（h1-h6）
- 遵循层级顺序，不跳过层级
- 每个页面只使用一个h1标签

```html
<h1>网站标题</h1>
<h2>主要章节</h2>
<h3>子章节</h3>
<h3>另一个子章节</h3>
<h2>另一个主要章节</h2>
```

## 可访问性

### 图片替代文本

- 为所有图片提供 `alt` 属性
- 描述性图片使用有意义的替代文本
- 装饰性图片使用空的 `alt` 属性

```html
<!-- 有意义的图片 -->
<img src="logo.png" alt="公司Logo" />

<!-- 装饰性图片 -->
<img src="decoration.png" alt="" />
```

### 表单标签

- 使用 `<label>` 标签关联表单控件
- 为表单控件提供适当的 `name` 和 `id`

```html
<div class="form-group">
  <label for="username">用户名：</label>
  <input type="text" id="username" name="username" />
</div>

<div class="form-group">
  <label for="password">密码：</label>
  <input type="password" id="password" name="password" />
</div>
```

### ARIA属性

- 适当使用ARIA属性提高可访问性
- 为非语义元素添加适当的角色

```html
<div role="navigation" aria-label="主导航">
  <!-- 导航内容 -->
</div>

<button aria-expanded="false" aria-controls="menu">
  展开菜单
</button>
<div id="menu" aria-hidden="true">
  <!-- 菜单内容 -->
</div>
```

## 注释

- 使用注释标识代码的主要部分
- 在复杂的代码块前添加注释
- 保持注释的简洁和有用性

```html
<!-- 页头 -->
<header>
  <!-- 导航菜单 -->
  <nav>
    <!-- ... -->
  </nav>
</header>

<!-- 主要内容区域 -->
<main>
  <!-- 文章列表 -->
  <section class="article-list">
    <!-- ... -->
  </section>
  
  <!-- 侧边栏 -->
  <aside>
    <!-- ... -->
  </aside>
</main>

<!-- 页脚 -->
<footer>
  <!-- ... -->
</footer>
```

## 特殊字符

- 使用HTML实体表示特殊字符
- 常见的HTML实体包括：

```html
&lt; <!-- < -->
&gt; <!-- > -->
&amp; <!-- & -->
&quot; <!-- " -->
&apos; <!-- ' -->
&copy; <!-- © -->
&reg; <!-- ® -->
&trade; <!-- ™ -->
&nbsp; <!-- 不换行空格 -->
&mdash; <!-- — -->
&ndash; <!-- – -->
```

## 性能考虑

- 最小化HTTP请求数量
- 将CSS文件放在 `<head>` 中
- 将JavaScript文件放在 `</body>` 前
- 适当使用资源预加载

```html
<head>
  <!-- CSS放在头部 -->
  <link rel="stylesheet" href="styles.css">
  
  <!-- 预加载关键资源 -->
  <link rel="preload" href="critical-font.woff2" as="font" type="font/woff2" crossorigin>
</head>
<body>
  <!-- 页面内容 -->
  
  <!-- JavaScript放在底部 -->
  <script src="app.js"></script>
</body>
```

## 模板语法

在使用模板引擎（如Vue、React等）时：

- 保持模板的可读性
- 适当使用注释说明复杂的逻辑
- 遵循框架的最佳实践

Vue模板示例：

```html
<template>
  <div class="user-profile">
    <!-- 用户基本信息 -->
    <header class="profile-header">
      <h1>{{ user.name }}</h1>
      <p v-if="user.bio">{{ user.bio }}</p>
    </header>
    
    <!-- 用户统计信息 -->
    <section class="profile-stats">
      <div v-for="(value, key) in user.stats" :key="key" class="stat-item">
        <strong>{{ key }}:</strong> {{ value }}
      </div>
    </section>
  </div>
</template>
```

## 总结

遵循一致的HTML编码规范有助于提高代码质量、可读性和可维护性。每个开发人员都应该熟悉并遵循这些规范。如有任何问题或建议，请在团队会议中提出讨论。 