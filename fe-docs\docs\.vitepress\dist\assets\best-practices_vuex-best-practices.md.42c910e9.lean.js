import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const A=JSON.parse('{"title":"Vuex最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/vuex-best-practices.md","filePath":"best-practices/vuex-best-practices.md"}'),p={name:"best-practices/vuex-best-practices.md"},o=l("",107),e=[o];function t(c,r,F,y,D,i){return a(),n("div",null,e)}const E=s(p,[["render",t]]);export{A as __pageData,E as default};
