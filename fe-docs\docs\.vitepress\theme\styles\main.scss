// ===== 主样式文件 =====
@use 'sass:map';
@use './variables' as vars;
@use './mixins' as mixins;

// ===== CSS变量定义 =====
:root {
  // 主题色 - 可通过色彩选择器动态修改
  --vp-c-brand: #{map.get(vars.$brand-colors, primary)};
  --vp-c-brand-light: #{map.get(vars.$brand-colors, light)};
  --vp-c-brand-lighter: #{map.get(vars.$brand-colors, lighter)};
  --vp-c-brand-dark: #{map.get(vars.$brand-colors, dark)};
  --vp-c-brand-darker: #{map.get(vars.$brand-colors, darker)};

  // VitePress 品牌色彩的其他变体
  --vp-c-brand-1: #{map.get(vars.$brand-colors, primary)};
  --vp-c-brand-2: #{map.get(vars.$brand-colors, light)};
  --vp-c-brand-3: #{map.get(vars.$brand-colors, lighter)};
  --vp-c-brand-soft: rgba(37, 99, 235, 0.14);
  
  // 其他颜色变量
  --vp-c-bg-soft: rgba(244, 244, 245, 0.7);
  --vp-c-text-1: #213547;
  --vp-c-text-2: rgba(60, 60, 60, 0.7);
  --vp-c-divider: rgba(60, 60, 60, 0.12);
  --vp-font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

// 暗黑模式颜色变量
.dark {
  // 暗黑模式下的主题色会自动调亮
  --vp-c-brand: #{map.get(vars.$brand-colors, light)};
  --vp-c-brand-light: #{map.get(vars.$brand-colors, lighter)};
  --vp-c-brand-lighter: #93c5fd;
  --vp-c-brand-1: #{map.get(vars.$brand-colors, light)};
  --vp-c-brand-2: #{map.get(vars.$brand-colors, lighter)};
  --vp-c-brand-3: #93c5fd;
  --vp-c-brand-soft: rgba(59, 130, 246, 0.16);
  
  // 其他暗黑模式变量
  --vp-c-bg: #1e1e20;
  --vp-c-bg-soft: rgba(33, 33, 35, 0.7);
  --vp-c-text-1: rgba(255, 255, 255, 0.87);
  --vp-c-text-2: rgba(235, 235, 235, 0.6);
  --vp-c-divider: rgba(200, 200, 200, 0.12);
}

// ===== 导航栏增强 =====
.VPNavBarTitle .title {
  font-weight: map.get(vars.$font-weights, bold);
  font-size: map.get(vars.$font-sizes, lg);
  @include mixins.brand-gradient-text;
}

// ===== Hero区域样式 =====
.VPHome {
  background: linear-gradient(135deg, 
    rgba(37, 99, 235, 0.02) 0%, 
    rgba(59, 130, 246, 0.03) 50%, 
    rgba(14, 165, 233, 0.02) 100%);

  .dark & {
    background: linear-gradient(135deg, 
      rgba(59, 130, 246, 0.03) 0%, 
      rgba(96, 165, 250, 0.05) 50%, 
      rgba(14, 165, 233, 0.03) 100%);
  }
}

// Hero标题增强
.VPHomeHero {
  .name,
  .text {
    @include mixins.brand-gradient-text;
    font-weight: map.get(vars.$font-weights, extrabold);
  }

  // 按钮样式增强
  .action {
    &.brand {
      @include mixins.button-primary;
    }

    &.alt {
      @include mixins.button-secondary;
    }
  }
}

// ===== Hero图片样式 =====
.VPHomeHero .image {
  img,
  .image-src {
    border-radius: map.get(vars.$border-radius, 2xl);
    @include mixins.shadow(lg);
    transition: all map.get(vars.$transitions, normal);
    border: 1px solid rgba(37, 99, 235, 0.1);

    .dark & {
      border: 1px solid rgba(59, 130, 246, 0.1);
    }
  }

  &:hover {
    img,
    .image-src {
      @include mixins.hover-lift;
      @include mixins.shadow(xl);
    }
  }
}

// ===== 特性卡片样式 =====
.VPFeatures {
  padding: map.get(vars.$spacing, 4xl) map.get(vars.$spacing, lg);

  .items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: map.get(vars.$spacing, lg);
  }

  .item {
    @include mixins.card-base;
    padding: map.get(vars.$spacing, xl) map.get(vars.$spacing, lg);
    border-color: rgba(37, 99, 235, 0.1);

    .dark & {
      border-color: rgba(59, 130, 246, 0.2);
    }

    &:hover {
      @include mixins.card-hover;
      border-color: rgba(37, 99, 235, 0.2);

      .dark & {
        border-color: rgba(59, 130, 246, 0.3);
      }
    }
  }

  .icon {
    margin-bottom: map.get(vars.$spacing, md);
    font-size: 48px;
    line-height: 1;
  }

  .title {
    font-size: map.get(vars.$font-sizes, xl);
    font-weight: map.get(vars.$font-weights, semibold);
    margin-bottom: map.get(vars.$spacing, sm);
    color: var(--vp-c-text-1);
    line-height: 1.3;
  }

  .details {
    line-height: 1.6;
    color: var(--vp-c-text-2);
    font-size: map.get(vars.$font-sizes, sm);
  }
}

// ===== 首页内容区域 =====
.main-container {
  max-width: 1152px;
  margin: 0 auto;
  padding: 0 map.get(vars.$spacing, lg) map.get(vars.$spacing, 4xl);
}

.main-content {
  padding: map.get(vars.$spacing, 2xl) 0;
  border-top: 1px solid var(--vp-c-divider);

  h1 {
    font-size: map.get(vars.$font-sizes, 4xl);
    font-weight: map.get(vars.$font-weights, bold);
    line-height: 1.3;
    margin-bottom: map.get(vars.$spacing, lg);
    color: var(--vp-c-text-1);
  }

  h2 {
    font-size: map.get(vars.$font-sizes, 2xl);
    font-weight: map.get(vars.$font-weights, semibold);
    margin: map.get(vars.$spacing, xl) 0 map.get(vars.$spacing, md);
    line-height: 1.4;
    color: var(--vp-c-text-1);
    @include mixins.decorative-border(left);
    padding-left: map.get(vars.$spacing, md);
  }

  p {
    margin: map.get(vars.$spacing, md) 0;
    line-height: 1.7;
    color: var(--vp-c-text-2);
    font-size: map.get(vars.$font-sizes, base);
  }

  ul {
    margin: map.get(vars.$spacing, md) 0;
    padding-left: map.get(vars.$spacing, lg);
  }

  li {
    margin: map.get(vars.$spacing, xs) 0;
    font-size: map.get(vars.$font-sizes, base);
    line-height: 1.7;
    color: var(--vp-c-text-2);

    strong {
      font-weight: map.get(vars.$font-weights, semibold);
      color: var(--vp-c-text-1);
    }
  }

  a {
    font-weight: map.get(vars.$font-weights, medium);
    color: var(--vp-c-brand);
    text-decoration: none;
    transition: color map.get(vars.$transitions, fast);

    &:hover {
      color: var(--vp-c-brand-light);
    }
  }
}

// 提示框样式
.main-content .custom-block {
  margin: map.get(vars.$spacing, md) 0;
  padding: map.get(vars.$spacing, md) map.get(vars.$spacing, md) map.get(vars.$spacing, xs) map.get(vars.$spacing, md);
  border-radius: map.get(vars.$border-radius, md);
  background-color: var(--vp-c-bg-soft);
  border-left: 5px solid var(--vp-c-brand);

  p {
    margin: map.get(vars.$spacing, xs) 0;
    line-height: 1.6;
  }
}

// ===== 响应式设计 =====
@include mixins.respond-to(md) {
  .VPFeatures .items {
    grid-template-columns: 1fr;
  }

  .main-container {
    padding: 0 map.get(vars.$spacing, md) map.get(vars.$spacing, 3xl);
  }

  .main-content {
    h1 {
      font-size: map.get(vars.$font-sizes, 3xl);
    }

    h2 {
      font-size: map.get(vars.$font-sizes, xl);
    }
  }
}
