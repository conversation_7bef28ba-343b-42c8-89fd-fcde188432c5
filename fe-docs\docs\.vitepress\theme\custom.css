/* ===== 布局容器 ===== */
/* 页面容器宽度控制 */
.VPDoc {
  max-width: 100%;
}

.VPContent.has-sidebar {
  max-width: 100% !important;
}

.VPDoc.has-sidebar .container {
  max-width: 100% !important;
}

/* ===== Hero区域样式 ===== */
/* 首页布局和间距 */
.VPHome {
  padding-bottom: 0 !important;
}

.VPHero {
  padding: 10px;
}

.VPHero.has-image {
  padding-top: 100px;
  padding-bottom: 10px;
}

.VPHero.has-image .container {
  gap: 10px;
  /* 减少文字和图片之间的间距 */
}

/* Hero区域内部元素间距 */
.VPHomeHero .name,
.VPHomeHero .text {
  margin-bottom: 8px;
  font-size: 2.5rem;
  line-height: 1.2;
}

.VPHomeHero .tagline {
  margin-bottom: 16px;
}

.VPHomeHero .actions {
  margin-top: 16px;
}

/* ===== Hero图片样式 ===== */
.VPHomeHero .image {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px;
  width: 100%;
  max-width: 320px;
  height: auto;
  transform: translateZ(0);
  will-change: auto;
  backface-visibility: hidden;
}

/* 图片容器结构 */
.VPHomeHero .image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transform: none;
}

/* 图片背景 */
.VPHomeHero .image-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-image: linear-gradient(135deg,
      var(--vp-c-brand) 10%,
      var(--vp-c-brand-light) 100%);
  filter: blur(44px);
  opacity: 0.25;
  transform: none;
}

/* 图片样式统一 */
.VPHomeHero .image img,
.VPHomeHero .image-src {
  position: relative;
  z-index: 1;
  width: 100%;
  height: auto;
  max-width: 320px;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease, filter 0.3s ease !important;
  filter: brightness(1) contrast(1.05) saturate(1.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  object-fit: cover;
  display: block;
  will-change: auto;
  backface-visibility: hidden;
  animation: none !important;
}

/* 图片源位置修复 */
.VPHomeHero .image-src {
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
  margin: 0 auto;
}

/* 暗黑模式下的图片样式 */
.dark .VPHomeHero .image img,
.dark .VPHomeHero .image-src {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.08);
  filter: brightness(0.95) contrast(1.1) saturate(1.05);
}

/* 防止动画 */
.VPHomeHero .image,
.VPHomeHero .image img,
.VPHomeHero .image-container,
.VPHomeHero .image-bg,
.VPHomeHero .image-src {
  animation: none !important;
}

/* 悬停效果 */
.VPHomeHero .image img:hover,
.VPHomeHero .image-src:hover {
  transform: none !important;
  box-shadow: 0 12px 36px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.12);
  filter: brightness(1.05) contrast(1.1) saturate(1.15);
}

.dark .VPHomeHero .image img:hover,
.dark .VPHomeHero .image-src:hover {
  box-shadow: 0 12px 36px rgba(0, 0, 0, 0.5), 0 4px 16px rgba(0, 0, 0, 0.4);
  filter: brightness(1) contrast(1.15) saturate(1.1);
}

/* 图片光晕效果 */
.VPHomeHero .image::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 26px;
  background: linear-gradient(135deg,
      var(--vp-c-brand) 0%,
      var(--vp-c-brand-light) 50%,
      rgba(62, 175, 124, 0.6) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
  filter: blur(8px);
  pointer-events: none;
}

.VPHomeHero .image:hover::before {
  opacity: 0.2;
}

.dark .VPHomeHero .image:hover::before {
  opacity: 0.3;
}

/* ===== 首页内容区域 ===== */
.main-container {
  max-width: 1152px;
  margin: 20px auto;
  background-color: var(--vp-c-bg);
}

.main-content {
  padding: 48px 0;
  border-top: 1px solid var(--vp-c-divider);
  color: var(--vp-c-text-1);
  font-family: var(--vp-font-family-base);
}

/* 首页标题样式 */
.main-content h1 {
  font-size: 32px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 24px;
  letter-spacing: -0.02em;
  color: var(--vp-c-text-1);
}

.main-content h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 36px 0 16px;
  line-height: 1.4;
  letter-spacing: -0.02em;
  border-top: 1px solid var(--vp-c-divider);
  padding-top: 24px;
  color: var(--vp-c-text-1);
}

/* 段落和列表样式 */
.main-content p {
  margin: 16px 0;
  line-height: 1.7;
  color: var(--vp-c-text-2);
  font-size: 16px;
}

.main-content ul {
  margin: 16px 0;
  padding-left: 24px;
  line-height: 1.7;
  color: var(--vp-c-text-2);
}

.main-content li {
  margin: 8px 0;
  font-size: 16px;
}

.main-content li strong {
  font-weight: 600;
  color: var(--vp-c-text-1);
}

.main-content a {
  font-weight: 500;
  color: var(--vp-c-brand);
  text-decoration: none;
  transition: color 0.25s;
}

.main-content a:hover {
  color: var(--vp-c-brand-light);
}

/* 提示框样式 */
.main-content .custom-block {
  margin: 16px 0;
  padding: 16px 16px 8px 16px;
  border-radius: 8px;
  background-color: var(--vp-c-bg-soft);
  border-left: 5px solid var(--vp-c-brand);
  transition: border-color 0.25s;
}

.main-content .custom-block p {
  margin: 8px 0;
  line-height: 1.6;
}

.dark .main-content .custom-block {
  background-color: var(--vp-c-bg-soft);
  border-left: 5px solid var(--vp-c-brand);
}

/* ===== 特性卡片样式 ===== */
.VPFeatures {
  padding: 64px 24px;
  background-color: transparent;
  border: none;
}

.VPFeatures .box {
  display: flex;
  align-items: center;
}

.VPFeatures .container {
  margin: 0 auto;
  max-width: 1200px;
}

.VPFeatures .items {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 20px;
}

.VPFeatures .item {
  flex: 1;
  min-width: 220px;
  padding: 24px 20px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dark .VPFeatures .item {
  background-color: rgba(40, 40, 45, 0.7);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.VPFeatures .item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  background-color: rgba(255, 255, 255, 0.9);
}

.dark .VPFeatures .item:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  background-color: rgba(50, 50, 55, 0.9);
}

.VPFeatures .VPFeature {
  height: 100%;
}

.VPFeatures .icon {
  margin-bottom: 20px;
  font-size: 48px;
  line-height: 1;
  text-align: center;
  width: 100%;
  display: block;
  background: none;
}

.VPFeatures .title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--vp-c-text-1);
  line-height: 1.3;
}

.VPFeatures .details {
  line-height: 1.6;
  color: var(--vp-c-text-2);
  flex-grow: 1;
}

/* ===== 文档内容样式 ===== */
.vp-doc h1,
.vp-doc h2,
.vp-doc h3,
.vp-doc h4,
.vp-doc h5,
.vp-doc h6 {
  color: var(--vp-c-text-1);
  font-weight: 600;
  line-height: 1.4;
}

.vp-doc p {
  color: var(--vp-c-text-2);
  line-height: 1.7;
}

.vp-doc ul {
  color: var(--vp-c-text-2);
}

.vp-doc li {
  line-height: 1.7;
}

.vp-doc a {
  color: var(--vp-c-brand);
  font-weight: 500;
  text-decoration: none;
  transition: color 0.25s;
}

.vp-doc a:hover {
  color: var(--vp-c-brand-light);
}

/* ===== 响应式设计 ===== */
/* 特性卡片响应式 */
@media (max-width: 960px) {
  .VPFeatures .items {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 图片响应式 */
@media (max-width: 768px) {
  .VPHomeHero .image {
    max-width: 280px;
    margin-bottom: 24px;
  }

  .VPHomeHero .image img,
  .VPHomeHero .image-src {
    max-width: 280px;
    border-radius: 20px;
  }

  .VPHomeHero .image::before {
    border-radius: 22px;
  }
}

@media (max-width: 480px) {
  .VPHomeHero .image {
    max-width: 240px;
    margin-bottom: 20px;
  }

  .VPHomeHero .image img,
  .VPHomeHero .image-src {
    max-width: 240px;
    border-radius: 16px;
  }

  .VPHomeHero .image::before {
    border-radius: 18px;
  }
}