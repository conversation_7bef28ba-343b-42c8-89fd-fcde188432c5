# Husky 配置指南

Husky 是一个 Git hooks 工具，可以在 Git 操作（如提交、推送）时自动执行脚本，确保代码质量和规范。本文档提供了完整的 Husky 配置指南。

## 什么是 Git Hooks

Git Hooks 是 Git 在特定事件发生时自动执行的脚本，常用的 hooks 包括：

- `pre-commit`：提交前执行
- `commit-msg`：提交信息验证
- `pre-push`：推送前执行
- `post-merge`：合并后执行

## 安装和基础配置

### 1. 安装 Husky

```bash
# 安装 Husky
npm install --save-dev husky

# 启用 Git hooks
npx husky install

# 添加到 package.json 脚本中
npm pkg set scripts.prepare="husky install"
```

### 2. Husky v7+ 配置

创建 `.husky` 目录和 hooks：

```bash
# 创建 pre-commit hook
npx husky add .husky/pre-commit "npm run lint"

# 创建 commit-msg hook
npx husky add .husky/commit-msg "npx commitlint --edit $1"

# 创建 pre-push hook
npx husky add .husky/pre-push "npm run test"
```

### 3. 目录结构

```
.husky/
├── _/
│   ├── .gitignore
│   └── husky.sh
├── pre-commit
├── commit-msg
└── pre-push
```

## 常用 Hooks 配置

### 1. pre-commit Hook

`.husky/pre-commit` 文件：

```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 运行 lint-staged
npx lint-staged

# 或者直接运行命令
# npm run lint
# npm run format
```

### 2. commit-msg Hook

`.husky/commit-msg` 文件：

```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 验证提交信息格式
npx commitlint --edit $1
```

### 3. pre-push Hook

`.husky/pre-push` 文件：

```bash
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 运行测试
npm run test

# 运行构建检查
npm run build

# 检查分支名称
branch=$(git rev-parse --abbrev-ref HEAD)
if [[ "$branch" == "master" || "$branch" == "main" ]]; then
  echo "不能直接推送到主分支"
  exit 1
fi
```

## 与 lint-staged 集成

### 1. 安装 lint-staged

```bash
npm install --save-dev lint-staged
```

### 2. 配置 lint-staged

在 `package.json` 中配置：

```json
{
  "lint-staged": {
    "*.{js,vue}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{css,scss}": [
      "stylelint --fix",
      "prettier --write"
    ],
    "*.{json,md}": [
      "prettier --write"
    ]
  }
}
```

或创建 `.lintstagedrc.js` 文件：

```javascript
module.exports = {
  '*.{js,vue}': [
    'eslint --fix',
    'prettier --write'
  ],
  '*.{css,scss}': [
    'stylelint --fix',
    'prettier --write'
  ],
  '*.{json,md}': [
    'prettier --write'
  ],
  '*.{png,jpg,jpeg,gif,svg}': [
    'imagemin-lint-staged'
  ]
}
```

### 3. 高级 lint-staged 配置

```javascript
// .lintstagedrc.js
const path = require('path')

module.exports = {
  // 相对路径配置
  '*.{js,vue}': (filenames) =>
    filenames.map((filename) => `eslint --fix ${filename}`),
  
  // 条件执行
  '*.{js,vue}': (filenames) => {
    const commands = ['eslint --fix']
    
    // 只对 src 目录下的文件运行测试
    const srcFiles = filenames.filter(file => 
      file.startsWith('src/')
    )
    
    if (srcFiles.length > 0) {
      commands.push('npm run test:unit')
    }
    
    return commands
  },
  
  // 自定义函数
  '*.vue': (filenames) => {
    const commands = []
    
    // 检查组件命名
    filenames.forEach(filename => {
      const basename = path.basename(filename, '.vue')
      if (basename !== basename.charAt(0).toUpperCase() + basename.slice(1)) {
        throw new Error(`组件文件名应该使用 PascalCase: ${filename}`)
      }
    })
    
    commands.push(`eslint --fix ${filenames.join(' ')}`)
    return commands
  }
}
```

## 提交信息验证

### 1. 安装 commitlint

```bash
npm install --save-dev @commitlint/cli @commitlint/config-conventional
```

### 2. 配置 commitlint

创建 `commitlint.config.js` 文件：

```javascript
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复
        'docs',     // 文档
        'style',    // 格式
        'refactor', // 重构
        'perf',     // 性能优化
        'test',     // 测试
        'chore',    // 构建过程或辅助工具的变动
        'revert'    // 回滚
      ]
    ],
    'type-case': [2, 'always', 'lower-case'],
    'type-empty': [2, 'never'],
    'scope-empty': [2, 'never'],
    'scope-case': [2, 'always', 'lower-case'],
    'subject-empty': [2, 'never'],
    'subject-full-stop': [2, 'never', '.'],
    'subject-case': [2, 'always', 'lower-case'],
    'header-max-length': [2, 'always', 72]
  }
}
```

### 3. 自定义提交信息规则

```javascript
// commitlint.config.js
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 自定义类型
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复bug
        'docs',     // 文档更新
        'style',    // 代码风格调整
        'refactor', // 重构
        'perf',     // 性能优化
        'test',     // 测试
        'chore',    // 构建过程或辅助工具的变动
        'revert',   // 回滚
        'wip',      // 开发中
        'build',    // 构建系统
        'ci'        // CI配置
      ]
    ],
    
    // 自定义作用域
    'scope-enum': [
      2,
      'always',
      [
        'components', // 组件
        'utils',      // 工具
        'api',        // 接口
        'router',     // 路由
        'store',      // 状态管理
        'styles',     // 样式
        'config',     // 配置
        'deps'        // 依赖
      ]
    ],
    
    // 主题长度限制
    'subject-max-length': [2, 'always', 50],
    
    // 主题格式
    'subject-case': [
      2,
      'always',
      ['sentence-case', 'start-case', 'pascal-case', 'upper-case', 'lower-case']
    ]
  }
}
```

## 高级配置

### 1. 条件执行

```bash
# .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 检查是否在 CI 环境中
if [ "$CI" = "true" ]; then
  echo "跳过 pre-commit hooks (CI 环境)"
  exit 0
fi

# 检查分支
branch=$(git rev-parse --abbrev-ref HEAD)
if [[ "$branch" == "master" || "$branch" == "main" ]]; then
  echo "主分支需要更严格的检查"
  npm run test:all
  npm run lint:strict
else
  npm run lint
fi

npx lint-staged
```

### 2. 性能优化

```javascript
// .lintstagedrc.js
module.exports = {
  // 并行执行
  '*.{js,vue}': [
    'eslint --fix --cache',
    'prettier --write --cache'
  ],
  
  // 只检查修改的文件
  '*.js': (filenames) => [
    `eslint --fix ${filenames.join(' ')}`,
    `jest --findRelatedTests ${filenames.join(' ')}`
  ]
}
```

### 3. 错误处理

```bash
# .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 设置错误处理
set -e

echo "运行 pre-commit 检查..."

# 运行 lint-staged
if ! npx lint-staged; then
  echo "❌ lint-staged 检查失败"
  exit 1
fi

# 运行测试
if ! npm run test:changed; then
  echo "❌ 测试失败"
  exit 1
fi

echo "✅ 所有检查通过"
```

## 团队协作配置

### 1. 统一配置

在 `package.json` 中添加：

```json
{
  "scripts": {
    "prepare": "husky install",
    "postinstall": "husky install"
  },
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  }
}
```

### 2. 跳过 Hooks

```bash
# 跳过 pre-commit hook
git commit -m "fix: urgent fix" --no-verify

# 跳过所有 hooks
git commit -m "fix: urgent fix" -n

# 环境变量跳过
HUSKY=0 git commit -m "fix: urgent fix"
```

### 3. 调试 Hooks

```bash
# 调试模式
HUSKY_DEBUG=1 git commit -m "test"

# 查看 hook 执行过程
sh -x .husky/pre-commit
```

## 常见问题解决

### 1. Hooks 不执行

```bash
# 检查 husky 是否正确安装
ls -la .git/hooks/

# 重新安装 husky
rm -rf .git/hooks
npx husky install

# 检查权限
chmod +x .husky/pre-commit
```

### 2. Windows 兼容性

```bash
# .husky/pre-commit (Windows 兼容)
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 使用 npx 确保跨平台兼容
npx lint-staged
```

### 3. 性能问题

```javascript
// 优化 lint-staged 配置
module.exports = {
  // 使用缓存
  '*.{js,vue}': [
    'eslint --fix --cache',
    'prettier --write --cache'
  ],
  
  // 限制文件数量
  '*.js': (filenames) => {
    if (filenames.length > 10) {
      return 'npm run lint:all'
    }
    return `eslint --fix ${filenames.join(' ')}`
  }
}
```

## 最佳实践

### 1. 渐进式采用

- 从简单的 lint 检查开始
- 逐步添加更多检查
- 团队讨论和调整规则

### 2. 性能考虑

- 使用缓存加速检查
- 只检查修改的文件
- 合理设置超时时间

### 3. 团队协作

- 统一 hooks 配置
- 提供跳过机制
- 文档化配置说明

### 4. 错误处理

- 提供清晰的错误信息
- 给出修复建议
- 记录常见问题解决方案

通过合理配置 Husky，可以在代码提交前自动执行质量检查，确保代码库的健康和团队开发规范的执行。
