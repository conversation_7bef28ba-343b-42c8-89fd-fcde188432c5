# Vue项目结构最佳实践

一个良好的项目结构是构建可维护和可扩展Vue应用的基础。本文档将介绍Vue项目的目录结构组织最佳实践，适用于中大型Vue.js应用。

## 目录

[[toc]]

## 基础项目结构

### 标准Vue CLI生成的项目结构

使用Vue CLI创建的项目默认结构如下：

```
project-root/
├── public/                 # 静态资源目录
│   ├── favicon.ico         # 网站图标
│   └── index.html          # 主HTML模板
├── src/                    # 源代码目录
│   ├── assets/             # 资源文件(图片、字体等)
│   ├── components/         # 全局组件
│   ├── views/              # 页面组件
│   ├── App.vue             # 根组件
│   ├── main.js             # 入口文件
│   └── router.js           # 路由配置
├── .gitignore              # Git忽略文件
├── babel.config.js         # Babel配置
├── package.json            # 项目依赖和脚本
├── README.md               # 项目说明
└── vue.config.js           # Vue CLI配置文件
```

这个结构适合小型项目，但对于中大型项目，我们需要更细致的组织。

## 中大型项目推荐结构

对于中大型项目，推荐以下目录结构：

```
project-root/
├── public/                 # 静态资源目录
│   ├── favicon.ico         # 网站图标
│   └── index.html          # 主HTML模板
├── src/                    # 源代码目录
│   ├── api/                # API请求模块
│   │   ├── index.js        # API导出
│   │   ├── user.js         # 用户相关API
│   │   └── product.js      # 产品相关API
│   ├── assets/             # 资源文件
│   │   ├── images/         # 图片资源
│   │   ├── styles/         # 全局样式
│   │   └── fonts/          # 字体资源
│   ├── components/         # 全局通用组件
│   │   ├── common/         # 基础UI组件
│   │   └── business/       # 业务相关组件
│   ├── constants/          # 常量定义
│   │   ├── index.js        # 导出文件
│   │   ├── api.js          # API相关常量
│   │   └── enum.js         # 枚举常量
│   ├── directives/         # 全局指令
│   ├── filters/            # 全局过滤器
│   ├── icons/              # SVG图标
│   ├── layout/             # 布局组件
│   │   ├── components/     # 布局子组件
│   │   └── index.vue       # 主布局组件
│   ├── mixins/             # 全局混入
│   ├── plugins/            # 插件配置
│   │   ├── index.js        # 插件导出
│   │   └── element.js      # Element UI配置
│   ├── router/             # 路由配置
│   │   ├── index.js        # 路由导出
│   │   ├── routes.js       # 路由定义
│   │   └── guards.js       # 路由守卫
│   ├── services/           # 服务层
│   │   ├── http.js         # HTTP请求服务
│   │   └── auth.js         # 认证服务
│   ├── store/              # Vuex状态管理
│   │   ├── index.js        # Store导出
│   │   ├── getters.js      # 全局Getters
│   │   └── modules/        # Store模块
│   │       ├── user.js     # 用户模块
│   │       └── app.js      # 应用模块
│   ├── utils/              # 工具函数
│   │   ├── index.js        # 工具导出
│   │   ├── request.js      # 请求工具
│   │   └── storage.js      # 存储工具
│   ├── views/              # 页面组件
│   │   ├── home/           # 首页模块
│   │   │   ├── index.vue   # 首页
│   │   │   └── components/ # 首页专用组件
│   │   ├── user/           # 用户模块
│   │   │   ├── login.vue   # 登录页
│   │   │   └── profile.vue # 个人资料页
│   │   └── error/          # 错误页
│   │       ├── 404.vue     # 404页面
│   │       └── 500.vue     # 500页面
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── .env                    # 环境变量
├── .env.development        # 开发环境变量
├── .env.production         # 生产环境变量
├── .eslintrc.js            # ESLint配置
├── .gitignore              # Git忽略文件
├── babel.config.js         # Babel配置
├── package.json            # 项目依赖和脚本
├── README.md               # 项目说明
└── vue.config.js           # Vue CLI配置文件
```

## 关键目录详解

### src/api

API目录负责处理所有与后端API的通信：

```js
// src/api/user.js
import request from '@/utils/request';

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  });
}

export function getUserInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token }
  });
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  });
}
```

```js
// src/api/index.js
import * as user from './user';
import * as product from './product';

export default {
  user,
  product
};
```

### src/components

组件目录包含可在整个应用中重用的组件：

```
components/
├── common/          # 基础UI组件
│   ├── Button/
│   │   ├── index.vue
│   │   └── style.scss
│   ├── Input/
│   └── Modal/
└── business/        # 业务组件
    ├── UserList/
    ├── ProductCard/
    └── OrderForm/
```

每个组件可以是单个文件组件(SFC)，也可以是包含多个文件的目录。对于复杂组件，推荐使用目录结构：

```
Button/
├── index.vue        # 主组件文件
├── style.scss       # 组件样式
├── types.js         # 类型定义
└── utils.js         # 组件工具函数
```

### src/views

视图目录包含应用的页面组件，通常与路由对应：

```
views/
├── home/
│   ├── index.vue            # 首页
│   └── components/          # 首页专用组件
│       ├── Banner.vue
│       └── Featured.vue
├── user/
│   ├── login.vue            # 登录页
│   ├── register.vue         # 注册页
│   └── components/          # 用户模块专用组件
│       └── UserForm.vue
└── product/
    ├── list.vue             # 产品列表页
    ├── detail.vue           # 产品详情页
    └── components/          # 产品模块专用组件
        ├── ProductFilter.vue
        └── ProductDetail.vue
```

对于特定页面才使用的组件，应放在对应页面的components目录下，而不是全局components目录。

### src/router

路由目录包含路由相关配置：

```js
// src/router/routes.js
import Layout from '@/layout';

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/user/login'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'dashboard' }
      }
    ]
  }
];

export const asyncRoutes = [
  {
    path: '/user',
    component: Layout,
    meta: { title: '用户管理', icon: 'user', roles: ['admin'] },
    children: [
      {
        path: 'list',
        component: () => import('@/views/user/list'),
        name: 'UserList',
        meta: { title: '用户列表' }
      },
      {
        path: 'create',
        component: () => import('@/views/user/create'),
        name: 'UserCreate',
        meta: { title: '创建用户' }
      }
    ]
  }
];
```

```js
// src/router/index.js
import Vue from 'vue';
import Router from 'vue-router';
import { constantRoutes } from './routes';

Vue.use(Router);

const createRouter = () => new Router({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
});

const router = createRouter();

// 重置路由方法
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher;
}

export default router;
```

### src/store

Vuex状态管理目录：

```js
// src/store/modules/user.js
import { login, logout, getUserInfo } from '@/api/user';
import { getToken, setToken, removeToken } from '@/utils/auth';

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  roles: []
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles;
  }
};

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    const { username, password } = userInfo;
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password })
        .then(response => {
          const { data } = response;
          commit('SET_TOKEN', data.token);
          setToken(data.token);
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  
  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getUserInfo(state.token)
        .then(response => {
          const { data } = response;
          
          if (!data) {
            reject('验证失败，请重新登录');
          }
          
          const { roles, name, avatar } = data;
          
          if (!roles || roles.length <= 0) {
            reject('用户角色必须是非空数组!');
          }
          
          commit('SET_ROLES', roles);
          commit('SET_NAME', name);
          commit('SET_AVATAR', avatar);
          resolve(data);
        })
        .catch(error => {
          reject(error);
        });
    });
  },
  
  // 用户登出
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit('SET_TOKEN', '');
          commit('SET_ROLES', []);
          removeToken();
          
          // 重置路由
          resetRouter();
          
          resolve();
        })
        .catch(error => {
          reject(error);
        });
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
```

```js
// src/store/index.js
import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import app from './modules/app';
import user from './modules/user';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    user
  },
  getters
});

export default store;
```

### src/utils

工具函数目录：

```js
// src/utils/request.js
import axios from 'axios';
import store from '@/store';
import { getToken } from '@/utils/auth';
import { Message } from 'element-ui';

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 5000
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['Authorization'] = `Bearer ${getToken()}`;
    }
    return config;
  },
  error => {
    console.log(error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    
    // 根据自定义错误码处理错误
    if (res.code !== 200) {
      Message({
        message: res.message || '请求错误',
        type: 'error',
        duration: 5 * 1000
      });
      
      // 处理特定错误（例如认证错误）
      if (res.code === 401) {
        store.dispatch('user/logout').then(() => {
          location.reload();
        });
      }
      
      return Promise.reject(new Error(res.message || '请求错误'));
    } else {
      return res;
    }
  },
  error => {
    console.log('请求错误:', error);
    Message({
      message: error.message || '请求错误',
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default service;
```

## 高级项目结构

### 特性驱动的目录结构

对于非常大型的项目，可以考虑按特性/业务模块组织代码，而不是按技术类型：

```
src/
├── core/                # 核心模块
│   ├── api/             # 核心API
│   ├── components/      # 核心组件
│   ├── utils/           # 核心工具
│   └── store/           # 核心状态
├── features/            # 业务特性
│   ├── authentication/  # 认证模块
│   │   ├── api/         # 认证API
│   │   ├── components/  # 认证组件
│   │   ├── store/       # 认证状态
│   │   └── views/       # 认证页面
│   ├── products/        # 产品模块
│   │   ├── api/         # 产品API
│   │   ├── components/  # 产品组件
│   │   ├── store/       # 产品状态
│   │   └── views/       # 产品页面
│   └── users/           # 用户模块
│       ├── api/         # 用户API
│       ├── components/  # 用户组件
│       ├── store/       # 用户状态
│       └── views/       # 用户页面
└── shared/              # 共享资源
    ├── assets/          # 共享资产
    ├── styles/          # 共享样式
    └── constants/       # 共享常量
```

这种结构的优点是相关代码放在一起，便于团队协作和功能开发。

### 微前端架构

对于特别复杂的应用，可以考虑微前端架构：

```
project-root/
├── packages/             # 子应用包
│   ├── main/             # 主应用
│   ├── user-center/      # 用户中心子应用
│   ├── product-manager/  # 产品管理子应用
│   └── dashboard/        # 仪表盘子应用
├── shared/               # 共享库
│   ├── components/       # 共享组件
│   ├── utils/            # 共享工具
│   └── styles/           # 共享样式
└── platform/             # 平台配置
    ├── config/           # 全局配置
    └── scripts/          # 构建脚本
```

每个子应用可以有自己的Vue项目结构，并独立开发和部署。

## 多环境配置

### 环境变量文件

使用多个.env文件配置不同环境：

```
# .env                # 所有环境
VUE_APP_TITLE=我的应用

# .env.development    # 开发环境
NODE_ENV=development
VUE_APP_BASE_API=/dev-api
VUE_APP_MOCK=true

# .env.staging        # 预发布环境
NODE_ENV=production
VUE_APP_BASE_API=https://staging-api.example.com
VUE_APP_MOCK=false

# .env.production     # 生产环境
NODE_ENV=production
VUE_APP_BASE_API=https://api.example.com
VUE_APP_MOCK=false
```