# API请求封装

本文档提供Vue项目中API请求封装的最佳实践，帮助开发者构建可维护、安全和高效的前后端交互层。

## API封装的意义

良好的API请求封装可以带来以下好处：

1. **代码复用** - 避免在多个组件中重复编写请求逻辑
2. **统一处理** - 集中处理认证、错误、加载状态等
3. **接口规范** - 统一的接口格式和参数处理
4. **便于维护** - API变更时只需修改一处代码
5. **请求优化** - 集中实现缓存、节流等优化手段
6. **便于切换** - 底层HTTP库变更时不影响业务代码

## 基础架构

### 目录结构

推荐的API相关文件结构：

```
src/
  api/
    index.js          # API模块入口
    request.js        # 请求工具（axios配置等）
    interceptors.js   # 请求/响应拦截器
    config.js         # API配置（基础URL等）
    modules/          # 按业务模块组织的API
      user.js         # 用户相关API
      product.js      # 产品相关API
      order.js        # 订单相关API
    errorHandler.js   # 错误处理
```

### 请求库选择

在Vue项目中，常用的HTTP请求库有：

- **axios** - 功能齐全，拦截器支持好，使用广泛
- **fetch** - 浏览器原生API，需要额外处理错误和解析
- **umi-request** - 基于fetch的封装，功能丰富
- **vue-resource** - Vue官方曾推荐，现已不再维护

推荐使用axios作为基础请求库。

## 基础请求工具封装

### 创建请求实例

```js
// api/request.js
import axios from 'axios'
import { API_BASE_URL, TIMEOUT } from './config'
import { setupInterceptors } from './interceptors'

// 创建axios实例
const request = axios.create({
  baseURL: API_BASE_URL,
  timeout: TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 设置拦截器
setupInterceptors(request)

export default request
```

### 配置文件

```js
// api/config.js
export const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || '/api'
export const TIMEOUT = 10000
export const RETRY_COUNT = 3
export const RETRY_DELAY = 1000

// API响应码
export const API_CODES = {
  SUCCESS: 0,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  SERVER_ERROR: 500
}
```

### 拦截器设置

```js
// api/interceptors.js
import store from '@/store'
import router from '@/router'
import { API_CODES } from './config'
import { handleApiError } from './errorHandler'

export function setupInterceptors(request) {
  // 请求拦截器
  request.interceptors.request.use(
    config => {
      // 添加token
      const token = store.getters['auth/token']
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      // 添加时间戳，避免缓存
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
      
      return config
    },
    error => {
      return Promise.reject(error)
    }
  )
  
  // 响应拦截器
  request.interceptors.response.use(
    response => {
      const { code, data, message } = response.data
      
      // 业务码处理
      if (code !== API_CODES.SUCCESS) {
        return handleApiError(code, message, response)
      }
      
      // 只返回响应数据部分
      return data
    },
    error => {
      if (error.response) {
        const { status } = error.response
        
        // 处理HTTP错误
        switch (status) {
          case 401:
            // 未授权，清除用户信息并重定向到登录页
            store.dispatch('auth/logout')
            router.push('/login')
            break
          case 403:
            // 禁止访问
            router.push('/forbidden')
            break
          case 500:
            // 服务器错误
            console.error('Server error:', error)
            break
        }
      } else if (error.request) {
        // 请求发出但未收到响应
        console.error('Network error:', error)
      } else {
        // 请求配置出错
        console.error('Request error:', error)
      }
      
      return Promise.reject(error)
    }
  )
}
```

### 错误处理

```js
// api/errorHandler.js
import { Message } from 'element-ui'
import { API_CODES } from './config'

export function handleApiError(code, message, response) {
  switch (code) {
    case API_CODES.UNAUTHORIZED:
      // 处理为完成401跳转前的业务逻辑
      break
    case API_CODES.FORBIDDEN:
      // 处理无权限
      Message.error('无访问权限')
      break
    default:
      // 其他业务错误
      Message.error(message || '请求失败')
  }
  
  return Promise.reject({ code, message, response })
}

// 全局处理未捕获的Promise错误
window.addEventListener('unhandledrejection', event => {
  console.error('Unhandled Promise Rejection:', event.reason)
  
  // 可以在这里添加全局错误上报逻辑
  if (process.env.NODE_ENV === 'production') {
    // errorTracker.report(event.reason)
  }
  
  // 防止默认处理
  event.preventDefault()
})
```

## API模块化

### API入口文件

```js
// api/index.js
import user from './modules/user'
import product from './modules/product'
import order from './modules/order'

export default {
  user,
  product,
  order
}
```

### 业务模块API

```js
// api/modules/user.js
import request from '../request'

export default {
  /**
   * 用户登录
   * @param {Object} data - 登录信息
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @returns {Promise<Object>} 用户信息
   */
  login(data) {
    return request({
      url: '/auth/login',
      method: 'post',
      data
    })
  },
  
  /**
   * 获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 用户详细信息
   */
  getUserInfo(userId) {
    return request({
      url: `/users/${userId}`,
      method: 'get'
    })
  },
  
  /**
   * 更新用户信息
   * @param {string} userId - 用户ID
   * @param {Object} data - 用户信息
   * @returns {Promise<Object>} 更新后的用户信息
   */
  updateUser(userId, data) {
    return request({
      url: `/users/${userId}`,
      method: 'put',
      data
    })
  }
}
```

## 高级功能

### 请求取消

使用axios的取消功能避免重复请求或不必要的请求：

```js
// api/modules/search.js
import axios from 'axios'
import request from '../request'

let cancelToken = null

export default {
  /**
   * 搜索产品
   * @param {Object} params - 搜索参数
   * @returns {Promise<Array>} 搜索结果
   */
  searchProducts(params) {
    // 如果已有请求，取消它
    if (cancelToken) {
      cancelToken.cancel('新搜索请求覆盖了之前的请求')
    }
    
    // 创建新的取消令牌
    cancelToken = axios.CancelToken.source()
    
    return request({
      url: '/products/search',
      method: 'get',
      params,
      cancelToken: cancelToken.token
    })
  }
}
```

在组件中使用：

```vue
<script>
import api from '@/api'

export default {
  data() {
    return {
      keyword: '',
      results: [],
      isLoading: false
    }
  },
  watch: {
    keyword: {
      handler: 'searchProducts',
      immediate: false
    }
  },
  methods: {
    async searchProducts() {
      if (!this.keyword) {
        this.results = []
        return
      }
      
      this.isLoading = true
      
      try {
        this.results = await api.search.searchProducts({
          keyword: this.keyword
        })
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error('Search error:', error)
        }
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>
```

### 请求重试

实现请求失败自动重试：

```js
// api/retryInterceptor.js
import { RETRY_COUNT, RETRY_DELAY } from './config'

export function setupRetry(request) {
  request.interceptors.response.use(null, async error => {
    const config = error.config
    
    // 如果未设置重试配置，设置默认值
    if (!config || !config.retry) {
      config.retry = RETRY_COUNT
    }
    
    // 设置已重试次数
    config.__retryCount = config.__retryCount || 0
    
    // 检查是否已达最大重试次数
    if (config.__retryCount >= config.retry) {
      return Promise.reject(error)
    }
    
    // 增加重试计数
    config.__retryCount += 1
    
    // 创建新的Promise延迟重试
    const delay = new Promise(resolve => {
      setTimeout(resolve, config.retryDelay || RETRY_DELAY)
    })
    
    // 重试请求
    await delay
    return request(config)
  })
}
```

### 请求节流和防抖

对于频繁发起的请求，使用节流和防抖：

```js
// utils/request-helpers.js
import { debounce, throttle } from 'lodash-es'

export function createDebouncedRequest(apiFunc, wait = 300) {
  return debounce(apiFunc, wait, { leading: false, trailing: true })
}

export function createThrottledRequest(apiFunc, wait = 1000) {
  return throttle(apiFunc, wait, { leading: true, trailing: false })
}

// 在API模块中使用
import { createDebouncedRequest } from '@/utils/request-helpers'
import request from '../request'

const searchProductsApi = params => {
  return request({
    url: '/products/search',
    method: 'get',
    params
  })
}

// 创建防抖版本的API
export const debouncedSearchProducts = createDebouncedRequest(searchProductsApi)
```

### 请求缓存

实现简单的请求缓存：

```js
// api/cacheInterceptor.js
export function setupCache(request, defaultTTL = 60000) {
  const cache = new Map()
  
  request.interceptors.request.use(config => {
    // 仅对GET请求进行缓存
    if (config.method !== 'get' || config.noCache) {
      return config
    }
    
    // 生成缓存键
    const key = generateCacheKey(config)
    
    // 检查缓存
    const cachedItem = cache.get(key)
    if (cachedItem && Date.now() - cachedItem.time < (config.cacheTTL || defaultTTL)) {
      // 从缓存返回
      config.adapter = () => {
        return Promise.resolve({
          data: cachedItem.data,
          status: 200,
          statusText: 'OK',
          headers: cachedItem.headers,
          config,
          request: {}
        })
      }
    }
    
    return config
  })
  
  request.interceptors.response.use(response => {
    // 仅对GET请求进行缓存
    if (response.config.method !== 'get' || response.config.noCache) {
      return response
    }
    
    // 生成缓存键
    const key = generateCacheKey(response.config)
    
    // 存入缓存
    cache.set(key, {
      time: Date.now(),
      data: response.data,
      headers: response.headers
    })
    
    return response
  })
  
  // 生成缓存键
  function generateCacheKey(config) {
    return `${config.url}|${JSON.stringify(config.params || {})}`
  }
  
  // 清除缓存的方法
  request.clearCache = (url = null) => {
    if (url) {
      // 清除指定URL的缓存
      for (const key of cache.keys()) {
        if (key.startsWith(url)) {
          cache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      cache.clear()
    }
  }
}
```

## 与Vue组合使用

### 混入API调用

创建可复用的API调用混入：

```js
// mixins/api-call.js
export default {
  data() {
    return {
      apiLoading: false,
      apiError: null,
      apiData: null
    }
  },
  methods: {
    async callApi(apiMethod, ...args) {
      this.apiLoading = true
      this.apiError = null
      
      try {
        this.apiData = await apiMethod(...args)
        return this.apiData
      } catch (error) {
        this.apiError = error
        throw error
      } finally {
        this.apiLoading = false
      }
    }
  }
}

// 在组件中使用
import apiCallMixin from '@/mixins/api-call'
import api from '@/api'

export default {
  mixins: [apiCallMixin],
  async created() {
    try {
      await this.callApi(api.user.getUserInfo, this.$route.params.id)
    } catch (error) {
      console.error('Failed to load user data')
    }
  }
}
```

### 使用Composition API

在Vue 3或Vue 2 + Composition API中使用：

```js
// composables/useApi.js
import { ref, unref } from 'vue'
import axios from 'axios'

export function useApi(apiFunc) {
  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 用于取消请求
  let cancelTokenSource = null
  
  async function execute(...args) {
    // 取消之前的请求
    if (cancelTokenSource) {
      cancelTokenSource.cancel('Operation canceled by new request')
    }
    
    // 创建新的取消令牌
    cancelTokenSource = axios.CancelToken.source()
    
    loading.value = true
    error.value = null
    
    try {
      // 将 ref 解包
      const processedArgs = args.map(arg => unref(arg))
      
      // 添加取消令牌
      const lastArg = processedArgs[processedArgs.length - 1]
      if (typeof lastArg === 'object' && !Array.isArray(lastArg)) {
        processedArgs[processedArgs.length - 1] = {
          ...lastArg,
          cancelToken: cancelTokenSource.token
        }
      } else {
        processedArgs.push({ cancelToken: cancelTokenSource.token })
      }
      
      // 执行API调用
      const result = await apiFunc(...processedArgs)
      data.value = result
      return result
    } catch (e) {
      if (!axios.isCancel(e)) {
        error.value = e
        throw e
      }
    } finally {
      if (!axios.isCancel(e)) {
        loading.value = false
      }
    }
  }
  
  // 取消当前请求
  function cancel(message = 'Operation canceled by user') {
    if (cancelTokenSource) {
      cancelTokenSource.cancel(message)
      cancelTokenSource = null
    }
  }
  
  return {
    data,
    loading,
    error,
    execute,
    cancel
  }
}

// 在组件中使用
import { useApi } from '@/composables/useApi'
import api from '@/api'

export default {
  setup() {
    const userId = ref('123')
    const { data: user, loading, error, execute: fetchUser } = useApi(api.user.getUserInfo)
    
    // 初始加载
    onMounted(() => {
      fetchUser(userId.value)
    })
    
    // 当userId变化时重新获取
    watch(userId, newId => {
      fetchUser(newId)
    })
    
    return {
      user,
      loading,
      error,
      userId
    }
  }
}
```

## 最佳实践总结

### API设计原则

1. **一致性**
   - 保持API接口命名一致
   - 保持参数格式一致
   - 保持错误处理一致

2. **模块化**
   - 按业务领域组织API
   - 每个API函数只做一件事
   - 适当抽取共用逻辑

3. **健壮性**
   - 全面的错误处理
   - 超时和重试机制
   - 请求取消支持

4. **性能优化**
   - 合理使用缓存
   - 防抖和节流控制
   - 避免无用请求

### 常见陷阱

1. **缺乏错误处理**
   - 确保捕获和处理所有可能的错误
   - 提供友好的错误提示

2. **混合关注点**
   - 避免在API层处理UI逻辑
   - 避免在组件中直接使用axios

3. **重复代码**
   - 提取共用请求逻辑
   - 使用拦截器处理通用任务

4. **硬编码配置**
   - 使用环境变量存储API URL
   - 将超时等配置集中管理

5. **忽视安全**
   - 总是验证输入数据
   - 使用HTTPS
   - 妥善处理敏感信息

遵循以上最佳实践，可以构建出健壮、可维护的API请求层，为前端应用提供可靠的数据交互基础。 