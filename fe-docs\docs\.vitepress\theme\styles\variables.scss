// ===== SCSS变量定义 =====
@use 'sass:map';

// 主题色彩系统
$brand-colors: (
  primary: #2563eb,
  light: #3b82f6,
  lighter: #60a5fa,
  dark: #1d4ed8,
  darker: #1e40af
);

// 辅助色彩
$accent-colors: (
  cyan: #0ea5e9,
  success: #10b981,
  warning: #f59e0b,
  danger: #ef4444
);

// 预设主题色
$color-presets: (
  blue: (#2563eb, #3b82f6),
  cyan: (#0891b2, #06b6d4),
  green: (#059669, #10b981),
  purple: (#7c3aed, #8b5cf6),
  pink: (#db2777, #ec4899),
  orange: (#ea580c, #f97316),
  red: (#dc2626, #ef4444),
  gray: (#4b5563, #6b7280)
);

// 阴影系统
$shadows: (
  sm: (0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)),
  md: (0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)),
  lg: (0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)),
  xl: (0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04))
);

// 暗黑模式阴影
$dark-shadows: (
  sm: (0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)),
  md: (0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)),
  lg: (0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)),
  xl: (0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3))
);

// 间距系统
$spacing: (
  xs: 4px,
  sm: 8px,
  md: 16px,
  lg: 24px,
  xl: 32px,
  2xl: 48px,
  3xl: 64px,
  4xl: 80px
);

// 圆角系统
$border-radius: (
  sm: 4px,
  md: 8px,
  lg: 12px,
  xl: 16px,
  2xl: 20px,
  full: 50%
);

// 字体系统
$font-sizes: (
  xs: 0.75rem,
  sm: 0.875rem,
  base: 1rem,
  lg: 1.125rem,
  xl: 1.25rem,
  2xl: 1.5rem,
  3xl: 1.875rem,
  4xl: 2.25rem
);

$font-weights: (
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800
);

// 断点系统
$breakpoints: (
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px
);

// 过渡动画
$transitions: (
  fast: 0.15s ease,
  normal: 0.3s ease,
  slow: 0.5s ease
);

// Z-index层级
$z-index: (
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal-backdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070
);
