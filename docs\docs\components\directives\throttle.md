# v-throttle

为事件添加节流功能，限制事件触发频率。

## 使用场景

- 滚动事件处理
- 鼠标移动事件处理
- 频繁点击按钮的处理

## 基本用法

```vue
<template>
  <div 
    v-throttle:scroll="{ fn: handleScroll, delay: 200 }"
    class="scroll-container"
  >
    <!-- 滚动内容 -->
  </div>
</template>

<script>
export default {
  methods: {
    handleScroll(e) {
      console.log('滚动位置：', e.target.scrollTop);
      // 执行滚动逻辑
    }
  }
}
</script>
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
| fn | Function | - | 需要节流处理的函数 |
| delay | Number | 300 | 节流时间间隔（单位：ms） |

## 源码实现

<details>
<summary>点击查看源码</summary>

```js
// src/directives/throttle.js
export default {
  bind(el, binding, vnode) {
    // 获取事件类型
    const event = binding.arg || 'click';
    // 获取传入的配置
    const { fn, delay = 300 } = binding.value || {};
    
    if (typeof fn !== 'function') {
      console.warn(`v-throttle指令需要一个函数作为参数`);
      return;
    }
    
    // 上次执行的时间
    let lastExecTime = 0;
    
    // 创建事件处理函数
    const handler = function(...args) {
      const now = Date.now();
      
      // 如果距离上次执行超过了设定的时间间隔，则执行
      if (now - lastExecTime >= delay) {
        lastExecTime = now;
        fn.apply(vnode.context, args);
      }
    };
    
    // 保存事件处理函数，用于解绑
    el._throttleHandler = handler;
    
    // 绑定事件
    el.addEventListener(event, handler);
  },
  
  // 当传入的值变化时
  update(el, binding) {
    // 获取事件类型
    const event = binding.arg || 'click';
    // 获取新的配置
    const { fn, delay = 300 } = binding.value || {};
    
    // 如果函数变了，需要重新绑定
    if (el._throttleHandler && binding.oldValue && binding.oldValue.fn !== fn) {
      // 先解绑旧的
      el.removeEventListener(event, el._throttleHandler);
      
      // 创建新的处理函数
      let lastExecTime = 0;
      const handler = function(...args) {
        const now = Date.now();
        if (now - lastExecTime >= delay) {
          lastExecTime = now;
          fn.apply(this, args);
        }
      };
      
      // 更新保存的处理函数
      el._throttleHandler = handler;
      
      // 重新绑定事件
      el.addEventListener(event, handler);
    }
  },
  
  // 指令与元素解绑时
  unbind(el, binding) {
    // 获取事件类型
    const event = binding.arg || 'click';
    
    // 如果存在处理函数，则解绑事件
    if (el._throttleHandler) {
      el.removeEventListener(event, el._throttleHandler);
      delete el._throttleHandler;
    }
  }
};
```
</details>
