# 业务组件总览

本页面介绍项目中的业务组件。业务组件是在基础组件之上，结合具体业务场景开发的可复用组件。

## 📋 组件列表

### 🏷️ [字典标签组件 - DictTag](/components/business/dict-tag)

根据字典数据显示对应的标签，支持多种样式主题

### 🔽 [字典选择器 - dictSelect](/components/business/dict-select)

基于字典数据的下拉选择器，支持字典数据的增删改查

### 📎 [自定义文件上传 - CustomFileUpload](/components/business/custom-file-upload)

支持多种文件格式的上传组件，提供文件列表管理

### 🖼️ [图片上传组件 - ImageUpload](/components/business/image-upload)

专门用于图片上传的组件，支持预览和多图上传

### 🧮 [自定义数字输入框 - InputNumber](/components/business/input-number)

增强版的数字输入组件

### ✍️ [自定义输入框 - InputWord](/components/business/input-word)

带有特殊功能的文本输入组件

### 📄 [文件预览组件 - FilePreview](/components/business/file-preview)


---

## 🚀 快速开始

### 组件引入方式

```js
// 全局引入（已在main.js中配置）
Vue.component('DictTag', DictTag)
Vue.component('dictSelect', dictSelect)

// 局部引入
import DictTag from '@/components/DictTag'
import dictSelect from '@/components/dictSelect'

// 按需引入
import { DictTag, CustomFileUpload } from '@/components'
```
