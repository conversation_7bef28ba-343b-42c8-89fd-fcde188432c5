# CSS编码规范

本文档定义了项目中CSS/SCSS代码的编写规范，旨在保持样式代码的一致性、可维护性和可扩展性。

## 命名规范

### BEM命名约定

我们采用 [BEM (Block, Element, Modifier)](http://getbem.com/) 命名方法：

- **Block**: 独立的组件块
- **Element**: 组件的子元素，用 `__` 连接
- **Modifier**: 修饰符，表示状态或变体，用 `--` 连接

```css
/* Block */
.card {
  /* ... */
}

/* Element */
.card__title {
  /* ... */
}

/* Element */
.card__content {
  /* ... */
}

/* Modifier */
.card--featured {
  /* ... */
}

/* Element with Modifier */
.card__title--large {
  /* ... */
}
```

### 命名规则

- 使用小写字母
- 使用连字符（-）连接单词
- 使用有意义的名称，表达其用途而非外观
- 避免使用缩写，除非是广泛接受的缩写（如 nav, btn）

```css
/* 好的命名 */
.user-profile { /* ... */ }
.navigation-primary { /* ... */ }
.btn-primary { /* ... */ }

/* 不好的命名 */
.userProfile { /* ... */ } /* 驼峰命名 */
.red-box { /* ... */ } /* 描述外观而非用途 */
.s-box { /* ... */ } /* 不明确的缩写 */
```

## 代码组织

### 文件结构

- 按组件/模块组织样式文件
- 使用一致的文件命名约定
- 核心样式和变量放在单独的文件中

推荐的文件结构：

```
styles/
├── base/
│   ├── _reset.scss
│   ├── _typography.scss
│   └── _variables.scss
├── components/
│   ├── _buttons.scss
│   ├── _cards.scss
│   └── _forms.scss
├── layout/
│   ├── _header.scss
│   ├── _footer.scss
│   └── _grid.scss
├── pages/
│   ├── _home.scss
│   └── _about.scss
├── utils/
│   ├── _mixins.scss
│   └── _functions.scss
└── main.scss
```

### 代码顺序

属性声明的顺序：

1. 定位属性（position, top, right, z-index, display, float等）
2. 盒模型属性（width, height, padding, margin, border等）
3. 排版属性（font, line-height, text-align等）
4. 视觉属性（color, background, opacity, box-shadow等）
5. 其他属性（cursor, overflow, transition等）

```css
.element {
  /* 定位属性 */
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  display: flex;
  
  /* 盒模型属性 */
  width: 100px;
  height: 100px;
  padding: 10px;
  margin: 10px;
  border: 1px solid #ccc;
  
  /* 排版属性 */
  font-family: 'Arial', sans-serif;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  
  /* 视觉属性 */
  color: #333;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  /* 其他属性 */
  cursor: pointer;
  transition: all 0.3s ease;
}
```

## 编码规范

### 基本规则

- 使用2个空格进行缩进
- 类名选择器使用连字符（-）分隔
- 每个声明块的左花括号前添加一个空格
- 每个属性与值之间使用冒号（:）后跟一个空格
- 每个声明以分号（;）结尾，包括最后一个声明
- 多个选择器时，每个选择器独占一行
- 颜色值使用十六进制小写形式，可缩写时尽量缩写
- 值为0时省略单位

```css
/* 好的风格 */
.selector-1,
.selector-2 {
  margin: 0;
  padding: 15px;
  color: #333;
  background-color: #f5f5f5;
}

/* 不好的风格 */
.selector-1, .selector-2{
  margin:0px;
  padding:15px;
  color:#333333;
  background-color: #F5F5F5
}
```

### 选择器

- 避免使用标签选择器（如 `div`, `span`）
- 避免使用ID选择器
- 避免选择器嵌套超过3层
- 避免过度使用 `!important`

```css
/* 好的选择器 */
.header__nav {
  /* ... */
}

/* 不好的选择器 */
header div ul li a {
  /* ... */
}

#header {
  /* ... */
}

.header .nav .list .item .link {
  /* ... */
}
```

## 预处理器使用（SCSS/SASS）

### 变量

- 使用有意义的变量名
- 相关变量应当分组
- 使用连字符（-）分隔单词

```scss
// 颜色变量
$color-primary: #3498db;
$color-secondary: #2ecc71;
$color-text: #333;
$color-background: #f5f5f5;

// 字体变量
$font-family-base: 'Arial', sans-serif;
$font-size-base: 16px;
$font-size-large: 18px;
$font-size-small: 14px;

// 间距变量
$spacing-unit: 8px;
$spacing-small: $spacing-unit;
$spacing-medium: $spacing-unit * 2;
$spacing-large: $spacing-unit * 3;
```

### 嵌套

- 避免嵌套超过3层
- 使用嵌套表示组件的结构
- 使用 `&` 符号引用父选择器

```scss
.card {
  padding: $spacing-medium;
  background-color: #fff;
  
  &__header {
    margin-bottom: $spacing-small;
  }
  
  &__title {
    font-size: $font-size-large;
    color: $color-text;
    
    &--highlighted {
      color: $color-primary;
    }
  }
  
  &__content {
    line-height: 1.5;
  }
  
  &--featured {
    border-left: 3px solid $color-primary;
  }
}
```

### 混合宏（Mixins）

- 用于重用代码块
- 参数命名清晰
- 提供默认值

```scss
@mixin flex-center($direction: row) {
  display: flex;
  flex-direction: $direction;
  justify-content: center;
  align-items: center;
}

@mixin truncate-text($width: 100%) {
  max-width: $width;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card__title {
  @include truncate-text(200px);
}

.modal__content {
  @include flex-center(column);
}
```

## 响应式设计

### 媒体查询

- 使用变量定义断点
- 移动优先设计
- 避免过多的媒体查询

```scss
// 断点变量
$breakpoint-small: 576px;
$breakpoint-medium: 768px;
$breakpoint-large: 992px;
$breakpoint-xlarge: 1200px;

// 媒体查询混合宏
@mixin media-breakpoint-up($breakpoint) {
  @if $breakpoint == small {
    @media (min-width: $breakpoint-small) { @content; }
  } @else if $breakpoint == medium {
    @media (min-width: $breakpoint-medium) { @content; }
  } @else if $breakpoint == large {
    @media (min-width: $breakpoint-large) { @content; }
  } @else if $breakpoint == xlarge {
    @media (min-width: $breakpoint-xlarge) { @content; }
  }
}

.card {
  padding: $spacing-small;
  
  @include media-breakpoint-up(medium) {
    padding: $spacing-medium;
  }
  
  @include media-breakpoint-up(large) {
    padding: $spacing-large;
  }
}
```

## 性能优化

- 避免使用通配符选择器（*）
- 避免使用CSS表达式
- 避免使用@import（SCSS中除外）
- 合并小图标为雪碧图或使用字体图标/SVG
- 压缩CSS文件

## 注释

- 使用块注释说明组件或部分
- 使用行注释说明特定属性
- 保持注释简洁明了

```scss
/* ==========================================================================
   主导航样式
   ========================================================================== */
.main-nav {
  /* ... */
}

/* 导航项 */
.main-nav__item {
  display: inline-block;
  margin-right: 10px; /* 项目间距 */
}
```

## 工具配置

所有项目统一使用 Stylelint 进行样式代码规范检查，相关配置文件：

- `.stylelintrc.js`

建议在编辑器中配置保存时自动格式化。

## 总结

遵循一致的CSS编码规范有助于提高代码质量、可读性和可维护性。每个开发人员都应该熟悉并遵循这些规范。如有任何问题或建议，请在团队会议中提出讨论。 