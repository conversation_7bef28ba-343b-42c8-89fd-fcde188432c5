<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>常用操作 | 前端技术开发文档</title>
    <meta name="description" content="A VitePress site">
    <link rel="preload stylesheet" href="/assets/style.5ccb9172.css" as="style">
    <script type="module" src="/assets/app.473eac78.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.2ed14f66.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="modulepreload" href="/assets/chunks/framework.3d729ebc.js">
  <link rel="modulepreload" href="/assets/chunks/theme.533e1ce4.js">
  <link rel="modulepreload" href="/assets/cesium_operations.md.4af21b3f.lean.js">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="apple-touch-icon" href="/logo.png">
  <meta name="theme-color" content="#0ea5e9">
  <script id="check-dark-light">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-21678b25 data-v-b2cf3e0b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-c8616af1></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-c8616af1> Skip to content </a><!--]--><!----><header class="VPNav" data-v-b2cf3e0b data-v-7e5bc4a5><div class="VPNavBar has-sidebar" data-v-7e5bc4a5 data-v-94c81dcc><div class="container" data-v-94c81dcc><div class="title" data-v-94c81dcc><div class="VPNavBarTitle has-sidebar" data-v-94c81dcc data-v-f4ef19a3><a class="title" href="/" data-v-f4ef19a3><!--[--><!--]--><!--[--><img class="VPImage logo" src="/logo.jpeg" alt data-v-6db2186b><!--]--><!--[-->前端技术开发文档<!--]--><!--[--><!--]--></a></div></div><div class="content" data-v-94c81dcc><div class="curtain" data-v-94c81dcc></div><div class="content-body" data-v-94c81dcc><!--[--><!--]--><div class="VPNavBarSearch search" style="--vp-meta-key:&#39;Meta&#39;;" data-v-94c81dcc><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><svg class="DocSearch-Search-Icon" width="20" height="20" viewBox="0 0 20 20" aria-label="search icon"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-94c81dcc data-v-7f418b0f><span id="main-nav-aria-label" class="visually-hidden" data-v-7f418b0f>Main Navigation</span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->首页<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/guide/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->指南<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/components/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->组件<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/best-practices/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->最佳实践<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/standards/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->规范标准<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/tools/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->工具配置<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/cesium/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->Cesium<!--]--><!----></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-94c81dcc data-v-f6a63727><label title="toggle dark mode" data-v-f6a63727 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div><!----><div class="VPFlyout VPNavBarExtra extra" data-v-94c81dcc data-v-40855f84 data-v-764effdf><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-764effdf><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="icon" data-v-764effdf><circle cx="12" cy="12" r="2"></circle><circle cx="19" cy="12" r="2"></circle><circle cx="5" cy="12" r="2"></circle></svg></button><div class="menu" data-v-764effdf><div class="VPMenu" data-v-764effdf data-v-e7ea1737><!----><!--[--><!--[--><!----><div class="group" data-v-40855f84><div class="item appearance" data-v-40855f84><p class="label" data-v-40855f84>Appearance</p><div class="appearance-action" data-v-40855f84><label title="toggle dark mode" data-v-40855f84 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div></div></div><!----><!--]--><!--]--></div></div></div><!--[--><!--[--><!--[--><div class="nav-color-picker" data-v-21678b25><div class="color-picker" data-v-21678b25 data-v-917787cc><button class="color-picker-trigger" title="打开色彩选择器" data-v-917787cc><div class="palette-icon" data-v-917787cc><div class="palette-circle" data-v-917787cc></div><div class="palette-colors" data-v-917787cc><div class="color-dot color-1" data-v-917787cc></div><div class="color-dot color-2" data-v-917787cc></div><div class="color-dot color-3" data-v-917787cc></div><div class="color-dot color-4" data-v-917787cc></div></div></div></button><!----><!----></div></div><!--]--><!--]--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-94c81dcc data-v-e5dd9c1c><span class="container" data-v-e5dd9c1c><span class="top" data-v-e5dd9c1c></span><span class="middle" data-v-e5dd9c1c></span><span class="bottom" data-v-e5dd9c1c></span></span></button></div></div></div></div><!----></header><div class="VPLocalNav" data-v-b2cf3e0b data-v-392e1bf8><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-392e1bf8><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="menu-icon" data-v-392e1bf8><path d="M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"></path><path d="M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"></path><path d="M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"></path><path d="M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"></path></svg><span class="menu-text" data-v-392e1bf8>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-392e1bf8 data-v-079b16a8><button data-v-079b16a8>Return to top</button><!----></div></div><aside class="VPSidebar" data-v-b2cf3e0b data-v-af16598e><div class="curtain" data-v-af16598e></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-af16598e><span class="visually-hidden" id="sidebar-aria-label" data-v-af16598e> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 has-active" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>Cesium 3D地图引擎</h2><!----></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>简介</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/basics.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>基础配置</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/concepts.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>核心概念</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link is-active has-active" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/operations.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>常用操作</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 collapsible" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>实战案例</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-c4656e6d><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="caret-icon" data-v-c4656e6d><path d="M9,19c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l5.3-5.3L8.3,6.7c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l6,6c0.4,0.4,0.4,1,0,1.4l-6,6C9.5,18.9,9.3,19,9,19z"></path></svg></div></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/cesium/examples/water-monitor.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>智慧水务监控</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-b2cf3e0b data-v-a494bd1d><div class="VPDoc has-sidebar has-aside" data-v-a494bd1d data-v-c4b0d3cf><!--[--><!--]--><div class="container" data-v-c4b0d3cf><div class="aside" data-v-c4b0d3cf><div class="aside-curtain" data-v-c4b0d3cf></div><div class="aside-container" data-v-c4b0d3cf><div class="aside-content" data-v-c4b0d3cf><div class="VPDocAside" data-v-c4b0d3cf data-v-3f215769><!--[--><!--]--><!--[--><!--]--><div class="VPDocAsideOutline" data-v-3f215769 data-v-ff0f39c8><div class="content" data-v-ff0f39c8><div class="outline-marker" data-v-ff0f39c8></div><div class="outline-title" data-v-ff0f39c8>On this page</div><nav aria-labelledby="doc-outline-aria-label" data-v-ff0f39c8><span class="visually-hidden" id="doc-outline-aria-label" data-v-ff0f39c8> Table of Contents for current page </span><ul class="root" data-v-ff0f39c8 data-v-9a431c33><!--[--><!--]--></ul></nav></div></div><!--[--><!--]--><div class="spacer" data-v-3f215769></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-c4b0d3cf><div class="content-container" data-v-c4b0d3cf><!--[--><!--]--><!----><main class="main" data-v-c4b0d3cf><div style="position:relative;" class="vp-doc _cesium_operations" data-v-c4b0d3cf><div><h1 id="常用操作" tabindex="-1">常用操作 <a class="header-anchor" href="#常用操作" aria-label="Permalink to &quot;常用操作&quot;">​</a></h1><p>本章介绍Cesium开发中的常用操作，包括相机控制、图层管理、实体操作、事件处理等实用功能。</p><h2 id="相机控制" tabindex="-1">相机控制 <a class="header-anchor" href="#相机控制" aria-label="Permalink to &quot;相机控制&quot;">​</a></h2><h3 id="基本相机操作" tabindex="-1">基本相机操作 <a class="header-anchor" href="#基本相机操作" aria-label="Permalink to &quot;基本相机操作&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> camera </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 设置相机位置</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setView</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">destination</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1500</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">orientation</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">heading</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 航向角</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pitch</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">45</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 俯仰角</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">roll</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.0</span><span style="color:#BABED8;">                             </span><span style="color:#676E95;font-style:italic;">// 翻滚角</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 相机飞行到目标位置</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">flyTo</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">destination</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5000</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">orientation</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">heading</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">45</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pitch</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">30</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">roll</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.0</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">duration</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">3.0</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 飞行时间（秒）</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="相机环绕" tabindex="-1">相机环绕 <a class="header-anchor" href="#相机环绕" aria-label="Permalink to &quot;相机环绕&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 环绕目标点</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> center </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">lookAt</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  center</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">HeadingPitchRange</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">    Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// heading</span></span>
<span class="line"><span style="color:#BABED8;">    Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toRadians</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">45</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">   </span><span style="color:#676E95;font-style:italic;">// pitch</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F78C6C;">5000</span><span style="color:#BABED8;">                          </span><span style="color:#676E95;font-style:italic;">// range</span></span>
<span class="line"><span style="color:#BABED8;">  )</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 取消环绕模式</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">lookAtTransform</span><span style="color:#BABED8;">(Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Matrix4</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">IDENTITY)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="相机动画" tabindex="-1">相机动画 <a class="header-anchor" href="#相机动画" aria-label="Permalink to &quot;相机动画&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建相机飞行路径</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> flightPath </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">duration</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">116.395</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.908</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1500</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">duration</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">3</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">116.400</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.915</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">2000</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">duration</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">4</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 执行飞行动画</span></span>
<span class="line"><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">flyToNextPosition</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">index</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">index</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">flightPath</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">point</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">flightPath</span><span style="color:#F07178;">[</span><span style="color:#BABED8;">index</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">flyTo</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    destination</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">...</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    duration</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">duration</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#82AAFF;">complete</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 飞行完成后继续下一个点</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#82AAFF;">setTimeout</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">flyToNextPosition</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">index</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#82AAFF;">flyToNextPosition</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="相机限制" tabindex="-1">相机限制 <a class="header-anchor" href="#相机限制" aria-label="Permalink to &quot;相机限制&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 限制相机缩放范围</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> controller </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">screenSpaceCameraController</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">controller</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">minimumZoomDistance </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">100</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">   </span><span style="color:#676E95;font-style:italic;">// 最小缩放距离</span></span>
<span class="line"><span style="color:#BABED8;">controller</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">maximumZoomDistance </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">50000</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 最大缩放距离</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 限制相机移动范围</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> rectangle </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Rectangle</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F78C6C;">115.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">38.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 西南角</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F78C6C;">118.0</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">41.0</span><span style="color:#BABED8;">   </span><span style="color:#676E95;font-style:italic;">// 东北角</span></span>
<span class="line"><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">constrainedAxis </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">UNIT_Z</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">controller</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">enableCollisionDetection </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="图层管理" tabindex="-1">图层管理 <a class="header-anchor" href="#图层管理" aria-label="Permalink to &quot;图层管理&quot;">​</a></h2><h3 id="影像图层操作" tabindex="-1">影像图层操作 <a class="header-anchor" href="#影像图层操作" aria-label="Permalink to &quot;影像图层操作&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 添加影像图层</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> imageryProvider </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">BingMapsImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://dev.virtualearth.net</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">key</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-bing-maps-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mapStyle</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BingMapsStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">AERIAL</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> imageryLayer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addImageryProvider</span><span style="color:#BABED8;">(imageryProvider)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 调整图层属性</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">alpha </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">       </span><span style="color:#676E95;font-style:italic;">// 透明度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">brightness </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.2</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 亮度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">contrast </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.1</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 对比度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">saturation </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.3</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 饱和度</span></span>
<span class="line"><span style="color:#BABED8;">imageryLayer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">gamma </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">       </span><span style="color:#676E95;font-style:italic;">// 伽马值</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 移除图层</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#BABED8;">(imageryLayer)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 图层排序</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">raise</span><span style="color:#BABED8;">(imageryLayer)</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 上移</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">lower</span><span style="color:#BABED8;">(imageryLayer)</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 下移</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">raiseToTop</span><span style="color:#BABED8;">(imageryLayer)</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;">  </span><span style="color:#676E95;font-style:italic;">// 移到顶部</span></span>
<span class="line"><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">lowerToBottom</span><span style="color:#BABED8;">(imageryLayer)</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 移到底部</span></span></code></pre></div><h3 id="多图层管理" tabindex="-1">多图层管理 <a class="header-anchor" href="#多图层管理" aria-label="Permalink to &quot;多图层管理&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建图层管理器</span></span>
<span class="line"><span style="color:#C792EA;">class</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">LayerManager</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C792EA;">constructor</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">viewer</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">layers</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Map</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 添加图层</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">addLayer</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">id</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">provider</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">options</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{})</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">layer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addImageryProvider</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">provider</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 应用选项</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">Object</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">assign</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">layer</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">options</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">layers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">set</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">layer</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">layer</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 移除图层</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">removeLayer</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">id</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">layer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">layers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">layer</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">imageryLayers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">layer</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">layers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">delete</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 显示/隐藏图层</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">toggleLayer</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">id</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">visible</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">layer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">layers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">layer</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">layer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">visible</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 设置图层透明度</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">setLayerAlpha</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">id</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">alpha</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">layer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">layers</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">layer</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">layer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">alpha</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">alpha</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> layerManager </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">LayerManager</span><span style="color:#BABED8;">(viewer)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 添加多个图层</span></span>
<span class="line"><span style="color:#BABED8;">layerManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addLayer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">satellite</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">BingMapsImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://dev.virtualearth.net</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">key</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mapStyle</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BingMapsStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">AERIAL</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">alpha</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#BABED8;">layerManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addLayer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">roads</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">BingMapsImageryProvider</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">url</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">https://dev.virtualearth.net</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">key</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your-key</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">mapStyle</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BingMapsStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ROAD</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">alpha</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.5</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="实体操作" tabindex="-1">实体操作 <a class="header-anchor" href="#实体操作" aria-label="Permalink to &quot;实体操作&quot;">​</a></h2><h3 id="批量创建实体" tabindex="-1">批量创建实体 <a class="header-anchor" href="#批量创建实体" aria-label="Permalink to &quot;批量创建实体&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建大量点实体</span></span>
<span class="line"><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">createRandomPoints</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">count</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">points</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 暂停事件以提高性能</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">suspendEvents</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">for</span><span style="color:#F07178;"> (</span><span style="color:#C792EA;">let</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">i</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">i</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">count</span><span style="color:#89DDFF;">;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">i</span><span style="color:#89DDFF;">++</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">longitude</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">116</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">latitude</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">entity</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">longitude</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">latitude</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      point</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">8</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromRandom</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> alpha</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#F07178;">      label</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        text</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">点</span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">i </span><span style="color:#89DDFF;">+</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span><span style="color:#89DDFF;">}`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">12pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">40</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">entity</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 恢复事件</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">resumeEvents</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 创建1000个随机点</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> points </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">createRandomPoints</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">1000</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="实体分组管理" tabindex="-1">实体分组管理 <a class="header-anchor" href="#实体分组管理" aria-label="Permalink to &quot;实体分组管理&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建实体管理器</span></span>
<span class="line"><span style="color:#C792EA;">class</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">EntityManager</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C792EA;">constructor</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">viewer</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Map</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 创建分组</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">createGroup</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">groupId</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">has</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">set</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> [])</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 添加实体到分组</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">addToGroup</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">groupId</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">entity</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">has</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">createGroup</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">entity</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 显示/隐藏分组</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">toggleGroup</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">groupId</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">visible</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">entities</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">entities</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">entity</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">show</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">visible</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 删除分组</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">removeGroup</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">groupId</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">entities</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">entities</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">entity</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">entity</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">delete</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 获取分组实体</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">getGroup</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">groupId</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">groups</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">groupId</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">||</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> entityManager </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">EntityManager</span><span style="color:#BABED8;">(viewer)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 创建不同类型的实体分组</span></span>
<span class="line"><span style="color:#BABED8;">entityManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createGroup</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">hospitals</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">entityManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createGroup</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">schools</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">entityManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createGroup</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">parks</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 添加医院标记</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> hospital </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">billboard</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">image</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">images/hospital.png</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">scale</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0.5</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">entityManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addToGroup</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">hospitals</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> hospital)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="实体样式动态更新" tabindex="-1">实体样式动态更新 <a class="header-anchor" href="#实体样式动态更新" aria-label="Permalink to &quot;实体样式动态更新&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建动态样式实体</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> dynamicEntity </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">position</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">point</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#F07178;">color</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 创建样式动画</span></span>
<span class="line"><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">animateEntityColor</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">entity</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">let</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">hue</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#82AAFF;">setInterval</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">hue</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">hue</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">%</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">360</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromHsl</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">hue</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">/</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">360</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.5</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">color</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">50</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#82AAFF;">animateEntityColor</span><span style="color:#BABED8;">(dynamicEntity)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 根据数据动态更新实体样式</span></span>
<span class="line"><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">updateEntityByData</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">entity</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 根据数值设置颜色</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">value</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">value</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">let</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">color</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">value</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">80</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">else</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">value</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">60</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ORANGE</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">else</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">value</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">40</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">else</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">GREEN</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">color</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">pixelSize</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">max</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">5</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">value</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">/</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 更新标签</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">label</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    text</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`${</span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">name</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">: </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">value</span><span style="color:#89DDFF;">}`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">14pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">    pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">40</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">};</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="事件处理" tabindex="-1">事件处理 <a class="header-anchor" href="#事件处理" aria-label="Permalink to &quot;事件处理&quot;">​</a></h2><h3 id="鼠标事件处理" tabindex="-1">鼠标事件处理 <a class="header-anchor" href="#鼠标事件处理" aria-label="Permalink to &quot;鼠标事件处理&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 创建事件处理器</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> handler </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">ScreenSpaceEventHandler</span><span style="color:#BABED8;">(viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 左键点击事件</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">pickedObject</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pick</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">defined</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">pickedObject</span><span style="color:#F07178;">)) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 点击到实体</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">点击实体:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 高亮显示</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">instanceof</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">Entity</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">pixelSize</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">15</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">else</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 点击地面</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">cartesian</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pickEllipsoid</span><span style="color:#F07178;">(</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ellipsoid</span></span>
<span class="line"><span style="color:#F07178;">    )</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">cartesian</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">cartographic</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromCartesian</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">cartesian</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">longitude</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDegrees</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">longitude</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">latitude</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDegrees</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">cartographic</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">latitude</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">log</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">点击位置: </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">longitude</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toFixed</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">6</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">, </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">latitude</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toFixed</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">6</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}`</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 在点击位置添加标记</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">cartesian</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        point</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CYAN</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LEFT_CLICK)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 右键点击事件</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">pickedObject</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pick</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">defined</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">pickedObject</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">&amp;&amp;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">instanceof</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">Entity</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 删除实体</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RIGHT_CLICK)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 鼠标移动事件</span></span>
<span class="line"><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#BABED8;">(</span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">pickedObject</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pick</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">endPosition</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 重置所有实体样式</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">values</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">entity</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scale</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 高亮悬停的实体</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">defined</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">pickedObject</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">&amp;&amp;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">instanceof</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#FFCB6B;">Entity</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">pickedObject</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scale</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1.5</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 修改鼠标样式</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">style</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">cursor</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">pointer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">else</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">style</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">cursor</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">default</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">},</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">MOUSE_MOVE)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="键盘事件处理" tabindex="-1">键盘事件处理 <a class="header-anchor" href="#键盘事件处理" aria-label="Permalink to &quot;键盘事件处理&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 监听键盘事件</span></span>
<span class="line"><span style="color:#BABED8;">document</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">addEventListener</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">keydown</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">switch</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">code</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">case</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">KeyH</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// H键 - 回到初始位置</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">flyTo</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        destination</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">116.391</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10000</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">break</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">case</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">KeyC</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// C键 - 清除所有实体</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">removeAll</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">break</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">case</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">KeyS</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// S键 - 截图</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">render</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">canvas</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">image</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">canvas</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toDataURL</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">image/png</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 下载截图</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">link</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">document</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createElement</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">a</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">link</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">download</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesium-screenshot.png</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">link</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">href</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">image</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">link</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">click</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">break</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">case</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Digit1</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// 1键 - 2D模式</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">morphTo2D</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">break</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">case</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Digit2</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// 2键 - Columbus View模式</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">morphToColumbusView</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">break</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">case</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">Digit3</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">// 3键 - 3D模式</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">morphTo3D</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">break</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="数据加载与显示" tabindex="-1">数据加载与显示 <a class="header-anchor" href="#数据加载与显示" aria-label="Permalink to &quot;数据加载与显示&quot;">​</a></h2><h3 id="geojson数据加载" tabindex="-1">GeoJSON数据加载 <a class="header-anchor" href="#geojson数据加载" aria-label="Permalink to &quot;GeoJSON数据加载&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 加载GeoJSON数据</span></span>
<span class="line"><span style="color:#C792EA;">async</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">function</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">loadGeoJsonData</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">url</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;font-style:italic;">try</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">dataSource</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">await</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">GeoJsonDataSource</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">load</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">url</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      stroke</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">HOTPINK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      fill</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">PINK</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">withAlpha</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0.5</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      strokeWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">3</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">dataSources</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">dataSource</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 飞行到数据范围</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">flyTo</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">dataSource</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">dataSource</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;font-style:italic;">catch</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">error</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">console</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">error</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">加载GeoJSON数据失败:</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">error</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#82AAFF;">loadGeoJsonData</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">data/boundaries.geojson</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h3 id="动态数据更新" tabindex="-1">动态数据更新 <a class="header-anchor" href="#动态数据更新" aria-label="Permalink to &quot;动态数据更新&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 模拟实时数据更新</span></span>
<span class="line"><span style="color:#C792EA;">class</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">RealTimeDataManager</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C792EA;">constructor</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">viewer</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">entities</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Map</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isRunning</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 开始实时更新</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">start</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isRunning</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isRunning</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">update</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 停止实时更新</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">stop</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isRunning</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 更新数据</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">update</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!this.</span><span style="color:#BABED8;">isRunning</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 模拟从服务器获取数据</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">fetchData</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">then</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">updateEntities</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">data</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 继续下次更新</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#82AAFF;">setTimeout</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">update</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 模拟数据获取</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C792EA;">async</span><span style="color:#BABED8;"> </span><span style="color:#F07178;">fetchData</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 模拟网络延迟</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">await</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#FFCB6B;">Promise</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">resolve</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">setTimeout</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">resolve</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">100</span><span style="color:#F07178;">))</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 生成模拟数据</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Array</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">from</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span><span style="color:#F07178;"> length</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">},</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">_</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;font-style:italic;">i</span><span style="color:#89DDFF;">)</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      id</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">device_</span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">i</span><span style="color:#89DDFF;">}`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      longitude</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">116.391</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">-</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.5</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.01</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      latitude</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">39.904</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">-</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.5</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.01</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      value</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">*</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">100</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      status</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Math</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">random</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0.8</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">?</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">alarm</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">normal</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">))</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 更新实体</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">updateEntities</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">data</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">data</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">item</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">let</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">entity</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">get</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;">entity</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">        </span><span style="color:#676E95;font-style:italic;">// 创建新实体</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#BABED8;">entity</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          id</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">longitude</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">latitude</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          point</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">            pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">            heightReference</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">HeightReference</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">CLAMP_TO_GROUND</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#F07178;">          label</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">            font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">12pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">            pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">40</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">            fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">            outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">            outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">            style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span></span>
<span class="line"><span style="color:#F07178;">          </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">        </span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">set</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">entity</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 更新位置和样式</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">fromDegrees</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">longitude</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">latitude</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">point</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">color</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">status</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">===</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">alarm</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">?</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RED</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">GREEN</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">entity</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">label</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">text</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`${</span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">id</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;">: </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">item</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">value</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toFixed</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">1</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}`</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> dataManager </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">RealTimeDataManager</span><span style="color:#BABED8;">(viewer)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#BABED8;">dataManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">start</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 5分钟后停止更新</span></span>
<span class="line"><span style="color:#82AAFF;">setTimeout</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> dataManager</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">stop</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">5</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">*</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">60</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">*</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="测量工具" tabindex="-1">测量工具 <a class="header-anchor" href="#测量工具" aria-label="Permalink to &quot;测量工具&quot;">​</a></h2><h3 id="距离测量" tabindex="-1">距离测量 <a class="header-anchor" href="#距离测量" aria-label="Permalink to &quot;距离测量&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">class</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">DistanceMeasure</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#C792EA;">constructor</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">viewer</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">handler</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">ScreenSpaceEventHandler</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">null;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isActive</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 激活测量</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">activate</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isActive</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isActive</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">style</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">cursor</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">crosshair</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">event</span><span style="color:#89DDFF;">)</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">addPoint</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">event</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">position</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">},</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LEFT_CLICK</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">setInputAction</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">()</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">finish</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">},</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RIGHT_CLICK</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 添加测量点</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">addPoint</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">screenPosition</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">cartesian</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">camera</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">pickEllipsoid</span><span style="color:#F07178;">(</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">screenPosition</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">scene</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">globe</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ellipsoid</span></span>
<span class="line"><span style="color:#F07178;">    )</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;">cartesian</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">cartesian</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 添加点标记</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">cartesian</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">      point</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        pixelSize</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">8</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        color</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 更新线段</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">updatePolyline</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 计算并显示距离</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">updateDistanceLabels</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 更新线段</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">updatePolyline</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        polyline</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          positions</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          width</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          material</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          clampToGround</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 更新距离标签</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">updateDistanceLabels</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 清除旧标签</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">label</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">label</span><span style="color:#F07178;">))</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">let</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">totalDistance</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">for</span><span style="color:#F07178;"> (</span><span style="color:#C792EA;">let</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">i</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#89DDFF;">;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">i</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&lt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#89DDFF;">;</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">i</span><span style="color:#89DDFF;">++</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">distance</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">distance</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;">[</span><span style="color:#BABED8;">i</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;">[</span><span style="color:#BABED8;">i</span><span style="color:#F07178;">])</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">totalDistance</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">+=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">distance</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 计算中点位置</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">midpoint</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Cartesian3</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">midpoint</span><span style="color:#F07178;">(</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;">[</span><span style="color:#BABED8;">i</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;">[</span><span style="color:#BABED8;">i</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian3</span><span style="color:#F07178;">()</span></span>
<span class="line"><span style="color:#F07178;">      )</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">// 添加距离标签</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">label</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">midpoint</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        label</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          text</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`${</span><span style="color:#BABED8;">(distance </span><span style="color:#89DDFF;">/</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toFixed</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">2</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;"> km</span><span style="color:#89DDFF;">`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">14pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">WHITE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">20</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">label</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 显示总距离</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">lastPoint</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;">[</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">]</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">totalLabel</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">add</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        position</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">lastPoint</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">        label</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">          text</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">总距离: </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">(totalDistance </span><span style="color:#89DDFF;">/</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1000</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toFixed</span><span style="color:#BABED8;">(</span><span style="color:#F78C6C;">2</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}</span><span style="color:#C3E88D;"> km</span><span style="color:#89DDFF;">`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          font</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">16pt sans-serif</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          fillColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">YELLOW</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          outlineColor</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Color</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">BLACK</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          outlineWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          style</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LabelStyle</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">FILL_AND_OUTLINE</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">          pixelOffset</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Cartesian2</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">-</span><span style="color:#F78C6C;">40</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">totalLabel</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 完成测量</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">finish</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">isActive</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">canvas</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">style</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">cursor</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">default</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">removeInputAction</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">LEFT_CLICK</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">handler</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">removeInputAction</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">ScreenSpaceEventType</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">RIGHT_CLICK</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 清除测量结果</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">clear</span><span style="color:#89DDFF;">()</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">points</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">polyline</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">null;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">label</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">viewer</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">entities</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">remove</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">label</span><span style="color:#F07178;">))</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#BABED8;">labels</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">this.</span><span style="color:#82AAFF;">finish</span><span style="color:#F07178;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 使用示例</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> distanceMeasure </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">DistanceMeasure</span><span style="color:#BABED8;">(viewer)</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 激活距离测量</span></span>
<span class="line"><span style="color:#BABED8;">distanceMeasure</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">activate</span><span style="color:#BABED8;">()</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 清除测量结果</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// distanceMeasure.clear();</span></span></code></pre></div><h2 id="下一步" tabindex="-1">下一步 <a class="header-anchor" href="#下一步" aria-label="Permalink to &quot;下一步&quot;">​</a></h2><ul><li><a href="/cesium/concepts.html">核心概念</a> - 回顾Cesium的核心概念和架构</li></ul></div></div></main><footer class="VPDocFooter" data-v-c4b0d3cf data-v-face870a><!--[--><!--]--><!----><div class="prev-next" data-v-face870a><div class="pager" data-v-face870a><a class="pager-link prev" href="/cesium/concepts.html" data-v-face870a><span class="desc" data-v-face870a>Previous page</span><span class="title" data-v-face870a>核心概念</span></a></div><div class="has-prev pager" data-v-face870a><a class="pager-link next" href="/cesium/examples/water-monitor.html" data-v-face870a><span class="desc" data-v-face870a>Next page</span><span class="title" data-v-face870a>智慧水务监控</span></a></div></div></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-b2cf3e0b data-v-2f86ebd2><div class="container" data-v-2f86ebd2><!----><p class="copyright" data-v-2f86ebd2>Copyright © 2025 智洋上水</p></div></footer><!--[--><!--]--></div></div>
    <script>__VP_HASH_MAP__ = JSON.parse("{\"best-practices_index.md\":\"38aee9d5\",\"components_directives_permission.md\":\"72d28b46\",\"best-practices_component-design.md\":\"d570a27f\",\"index.md\":\"3e96beb2\",\"cesium_index.md\":\"440ac056\",\"standards_code-review.md\":\"04c78cff\",\"tools_vscode.md\":\"71a1eb8e\",\"tools_husky.md\":\"586bda1d\",\"components_charts.md\":\"1b67c1bd\",\"tools_debugging.md\":\"506e7f52\",\"components_directives_table-height.md\":\"1acd3698\",\"components_business_common-dialog-box.md\":\"605964f5\",\"cesium_concepts.md\":\"608db15b\",\"best-practices_vuex-best-practices.md\":\"42c910e9\",\"cesium_operations.md\":\"4af21b3f\",\"tools_index.md\":\"6738f00c\",\"components_directives_index.md\":\"e6ec9d5d\",\"best-practices_project-structure.md\":\"1dfc19f7\",\"guide_index.md\":\"a218fcb9\",\"best-practices_performance.md\":\"495f9c35\",\"best-practices_async-data.md\":\"d6f4dbd8\",\"standards_vue-standard.md\":\"204374d5\",\"guide_project-structure.md\":\"83547606\",\"standards_css-standard.md\":\"43da9895\",\"standards_html-standard.md\":\"87257590\",\"components_directives_throttle.md\":\"b30fc5ee\",\"components_directives_loading.md\":\"0bd89f42\",\"cesium_examples_water-monitor.md\":\"432751c3\",\"standards_js-standard.md\":\"e5f97f25\",\"tools_eslint.md\":\"eea2bb46\",\"components_directives_copy.md\":\"1f572838\",\"standards_documentation.md\":\"4ebedf27\",\"components_directives_debounce.md\":\"6c3b5296\",\"standards_git-workflow.md\":\"a7004f93\",\"best-practices_modular-development.md\":\"ce72d502\",\"components_index.md\":\"06a93849\",\"components_business_dict-select.md\":\"5f3747e9\",\"components_business_custom-file-upload.md\":\"40d498f0\",\"components_business_map-visualization.md\":\"11e90cc6\",\"tools_prettier.md\":\"dc0ae721\",\"best-practices_charts.md\":\"3ff7afe8\",\"components_form.md\":\"a7aefeb8\",\"components_business_dict-tag.md\":\"f87374af\",\"components_business_input-number.md\":\"186c78fb\",\"best-practices_state-management.md\":\"6cde5f2d\",\"best-practices_api-request.md\":\"32a5c3df\",\"components_business_input-word.md\":\"28d03cd2\",\"best-practices_utils.md\":\"08da2e9d\",\"guide_development-process.md\":\"7d0d3459\",\"tools_package-manager.md\":\"7c6faea2\",\"components_screen.md\":\"a126a925\",\"components_business_file-preview.md\":\"f077827f\",\"components_directives_drag-dialog.md\":\"fc009f97\",\"cesium_basics.md\":\"18bfba98\",\"components_business.md\":\"********\",\"standards_index.md\":\"e5fe1d60\",\"standards_git-commit.md\":\"4953e1ed\",\"best-practices_error-handling.md\":\"37d5b8d1\",\"best-practices_i18n.md\":\"83c49ca7\",\"components_table.md\":\"9742235f\",\"best-practices_reuse.md\":\"d4c27921\",\"best-practices_component-communication.md\":\"da381d67\",\"best-practices_routing.md\":\"a83fde5f\",\"guide_admin-development.md\":\"b638fd3e\",\"components_business_image-upload.md\":\"f2d49e5f\"}")
__VP_SITE_DATA__ = JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"前端技术开发文档\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"appearance\":true,\"themeConfig\":{\"logo\":\"/logo.jpeg\",\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"指南\",\"link\":\"/guide/\"},{\"text\":\"组件\",\"link\":\"/components/\"},{\"text\":\"最佳实践\",\"link\":\"/best-practices/\"},{\"text\":\"规范标准\",\"link\":\"/standards/\"},{\"text\":\"工具配置\",\"link\":\"/tools/\"},{\"text\":\"Cesium\",\"link\":\"/cesium/\"}],\"search\":{\"provider\":\"local\",\"options\":{\"translations\":{\"button\":{\"buttonText\":\"搜索文档\",\"buttonAriaLabel\":\"搜索文档\"},\"modal\":{\"noResultsText\":\"无法找到相关结果\",\"resetButtonTitle\":\"清除查询条件\",\"footer\":{\"selectText\":\"选择\",\"navigateText\":\"切换\",\"closeText\":\"关闭\"}}}}},\"sidebar\":{\"/guide/\":[{\"text\":\"开发指南\",\"items\":[{\"text\":\"快速开始\",\"link\":\"/guide/\"},{\"text\":\"项目结构\",\"link\":\"/guide/project-structure\"},{\"text\":\"开发流程\",\"link\":\"/guide/development-process\"},{\"text\":\"后台管理开发\",\"link\":\"/guide/admin-development\"}]}],\"/components/\":[{\"text\":\"组件库\",\"items\":[{\"text\":\"组件概览\",\"link\":\"/components/\"},{\"text\":\"业务组件\",\"collapsed\":false,\"items\":[{\"text\":\"业务组件总览\",\"link\":\"/components/business\"},{\"text\":\"字典标签组件\",\"link\":\"/components/business/dict-tag\"},{\"text\":\"字典选择器\",\"link\":\"/components/business/dict-select\"},{\"text\":\"自定义文件上传\",\"link\":\"/components/business/custom-file-upload\"},{\"text\":\"图片上传组件\",\"link\":\"/components/business/image-upload\"},{\"text\":\"自定义数字输入框\",\"link\":\"/components/business/input-number\"},{\"text\":\"自定义文本输入框\",\"link\":\"/components/business/input-word\"},{\"text\":\"文件预览组件\",\"link\":\"/components/business/file-preview\"},{\"text\":\"地图可视化组件\",\"link\":\"/components/business/map-visualization\"}]},{\"text\":\"表单组件\",\"link\":\"/components/form\"},{\"text\":\"表格组件\",\"link\":\"/components/table\"},{\"text\":\"图表组件\",\"link\":\"/components/charts\"},{\"text\":\"全局指令\",\"collapsed\":false,\"items\":[{\"text\":\"指令概览\",\"link\":\"/components/directives/index\"},{\"text\":\"表格高度\",\"link\":\"/components/directives/table-height\"},{\"text\":\"权限控制\",\"link\":\"/components/directives/permission\"},{\"text\":\"弹窗拖拽\",\"link\":\"/components/directives/drag-dialog\"},{\"text\":\"防抖处理\",\"link\":\"/components/directives/debounce\"},{\"text\":\"节流处理\",\"link\":\"/components/directives/throttle\"},{\"text\":\"一键复制\",\"link\":\"/components/directives/copy\"}]},{\"text\":\"大屏开发\",\"link\":\"/components/screen\"}]}],\"/best-practices/\":[{\"text\":\"最佳实践\",\"items\":[{\"text\":\"概述\",\"link\":\"/best-practices/\"},{\"text\":\"性能优化\",\"link\":\"/best-practices/performance\"},{\"text\":\"代码复用\",\"link\":\"/best-practices/reuse\"},{\"text\":\"状态管理\",\"link\":\"/best-practices/state-management\"},{\"text\":\"路由管理\",\"link\":\"/best-practices/routing\"},{\"text\":\"组件通信\",\"link\":\"/best-practices/component-communication\"},{\"text\":\"异步数据处理\",\"link\":\"/best-practices/async-data\"},{\"text\":\"模块化开发\",\"link\":\"/best-practices/modular-development\"},{\"text\":\"错误处理\",\"link\":\"/best-practices/error-handling\"},{\"text\":\"国际化实现\",\"link\":\"/best-practices/i18n\"},{\"text\":\"Vue组件设计\",\"link\":\"/best-practices/component-design\"},{\"text\":\"Vuex最佳实践\",\"link\":\"/best-practices/vuex-best-practices\"},{\"text\":\"Vue项目结构\",\"link\":\"/best-practices/project-structure\"},{\"text\":\"API请求封装\",\"link\":\"/best-practices/api-request\"},{\"text\":\"工具函数\",\"link\":\"/best-practices/utils\"}]}],\"/standards/\":[{\"text\":\"规范标准\",\"items\":[{\"text\":\"规范概述\",\"link\":\"/standards/\"},{\"text\":\"编码规范\",\"collapsed\":false,\"items\":[{\"text\":\"JavaScript规范\",\"link\":\"/standards/js-standard\"},{\"text\":\"CSS规范\",\"link\":\"/standards/css-standard\"},{\"text\":\"HTML规范\",\"link\":\"/standards/html-standard\"},{\"text\":\"Vue开发规范\",\"link\":\"/standards/vue-standard\"}]},{\"text\":\"Git提交规范\",\"link\":\"/standards/git-commit\"},{\"text\":\"Git工作流\",\"link\":\"/standards/git-workflow\"},{\"text\":\"代码审查\",\"link\":\"/standards/code-review\"},{\"text\":\"文档规范\",\"link\":\"/standards/documentation\"}]}],\"/tools/\":[{\"text\":\"开发工具配置\",\"items\":[{\"text\":\"工具概览\",\"link\":\"/tools/\"},{\"text\":\"VS Code配置\",\"link\":\"/tools/vscode\"},{\"text\":\"调试工具\",\"link\":\"/tools/debugging\"},{\"text\":\"包管理工具\",\"link\":\"/tools/package-manager\"}]},{\"text\":\"代码质量工具\",\"collapsed\":false,\"items\":[{\"text\":\"ESLint配置\",\"link\":\"/tools/eslint\"},{\"text\":\"Prettier配置\",\"link\":\"/tools/prettier\"},{\"text\":\"Husky配置\",\"link\":\"/tools/husky\"}]}],\"/cesium/\":[{\"text\":\"Cesium 3D地图引擎\",\"items\":[{\"text\":\"简介\",\"link\":\"/cesium/\"},{\"text\":\"基础配置\",\"link\":\"/cesium/basics\"},{\"text\":\"核心概念\",\"link\":\"/cesium/concepts\"},{\"text\":\"常用操作\",\"link\":\"/cesium/operations\"}]},{\"text\":\"实战案例\",\"collapsed\":false,\"items\":[{\"text\":\"智慧水务监控\",\"link\":\"/cesium/examples/water-monitor\"}]}]},\"footer\":{\"copyright\":\"Copyright © 2025 智洋上水\"}},\"locales\":{},\"scrollOffset\":90,\"cleanUrls\":false}")</script>
    
  </body>
</html>