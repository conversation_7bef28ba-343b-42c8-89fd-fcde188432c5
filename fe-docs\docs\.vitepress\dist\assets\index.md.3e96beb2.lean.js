import{_ as e,o as t,c as a,V as i}from"./chunks/framework.3d729ebc.js";const u=JSON.parse('{"title":"前端技术开发文档","description":"","frontmatter":{"layout":"home","title":"前端技术开发文档","hero":{"name":"前端技术开发文档","image":{"src":"/logo.jpeg","alt":"前端技术文档"},"actions":[{"theme":"brand","text":"快速开始","link":"/guide/"},{"theme":"alt","text":"组件库","link":"/components/"},{"theme":"alt","text":"规范标准","link":"/standards/"}]},"features":[{"icon":"📋","title":"全面的开发指南","details":"包含项目初始化、目录结构、开发流程等全面指南"},{"icon":"🧩","title":"丰富的组件库","details":"提供基础组件、业务组件及自定义组件的详细说明与使用示例"},{"icon":"🚀","title":"最佳实践","details":"前端性能优化、代码复用、状态管理等最佳实践指南"},{"icon":"📐","title":"规范标准","details":"编码规范、Git工作流、代码审查、文档规范等标准化指南"},{"icon":"🔧","title":"工具配置","details":"VS Code配置、调试工具、包管理、代码质量工具等开发环境配置"},{"icon":"🌏","title":"Cesium 3D地图引擎","details":"强大的3D地球可视化引擎，支持地理空间数据展示与交互"}]},"headers":[],"relativePath":"index.md","filePath":"index.md"}'),l={name:"index.md"},s=i("",1),o=[s];function n(c,d,r,h,m,_){return t(),a("div",null,o)}const f=e(l,[["render",n]]);export{u as __pageData,f as default};
