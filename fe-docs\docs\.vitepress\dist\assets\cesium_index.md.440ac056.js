import{_ as s,o as a,c as t,V as n}from"./chunks/framework.3d729ebc.js";const u=JSON.parse('{"title":"Cesium 3D地图引擎","description":"","frontmatter":{},"headers":[],"relativePath":"cesium/index.md","filePath":"cesium/index.md"}'),l={name:"cesium/index.md"},e=n(`<h1 id="cesium-3d地图引擎" tabindex="-1">Cesium 3D地图引擎 <a class="header-anchor" href="#cesium-3d地图引擎" aria-label="Permalink to &quot;Cesium 3D地图引擎&quot;">​</a></h1><div class="tip custom-block"><p class="custom-block-title">简介</p><p>Cesium是一个开源的JavaScript库，用于创建高性能、跨平台、跨浏览器的3D地球和地图。它使用WebGL进行硬件加速图形渲染，提供了精确的地理空间数据可视化能力。</p></div><h2 id="什么是cesium" tabindex="-1">什么是Cesium？ <a class="header-anchor" href="#什么是cesium" aria-label="Permalink to &quot;什么是Cesium？&quot;">​</a></h2><p>Cesium是一个基于JavaScript的开源地理空间可视化库，专为创建动态、交互式的3D地球和地图而设计。它提供了一个完整的虚拟地球和地图系统，可以在网页浏览器中无需插件地运行。</p><h2 id="核心特性" tabindex="-1">核心特性 <a class="header-anchor" href="#核心特性" aria-label="Permalink to &quot;核心特性&quot;">​</a></h2><ul><li><strong>高精度地理数据渲染</strong>：支持WGS84坐标系，提供精确的地理位置表示</li><li><strong>3D地形渲染</strong>：支持高精度地形数据的加载与渲染</li><li><strong>影像图层</strong>：支持多种格式的卫星影像、航拍图像等</li><li><strong>矢量数据可视化</strong>：支持GeoJSON、KML、CZML等格式的矢量数据</li><li><strong>时间动态数据</strong>：支持基于时间的动态数据展示</li><li><strong>3D模型支持</strong>：支持3D Tiles、glTF等3D模型格式</li><li><strong>强大的相机控制</strong>：提供灵活的视角控制和飞行动画</li><li><strong>跨平台兼容</strong>：适用于桌面和移动设备的主流浏览器</li></ul><h2 id="与其他地图引擎的比较" tabindex="-1">与其他地图引擎的比较 <a class="header-anchor" href="#与其他地图引擎的比较" aria-label="Permalink to &quot;与其他地图引擎的比较&quot;">​</a></h2><table><thead><tr><th>特性</th><th>Cesium</th><th>Leaflet</th><th>Google Maps</th><th>OpenLayers</th></tr></thead><tbody><tr><td>3D地球</td><td>✓</td><td>✗</td><td>部分支持</td><td>✗</td></tr><tr><td>地形渲染</td><td>✓</td><td>✗</td><td>部分支持</td><td>✗</td></tr><tr><td>3D模型</td><td>✓</td><td>✗</td><td>部分支持</td><td>✗</td></tr><tr><td>开源</td><td>✓</td><td>✓</td><td>✗</td><td>✓</td></tr><tr><td>性能</td><td>高(WebGL)</td><td>高(2D)</td><td>高</td><td>高(2D)</td></tr><tr><td>学习曲线</td><td>陡峭</td><td>平缓</td><td>中等</td><td>中等</td></tr></tbody></table><h2 id="快速开始" tabindex="-1">快速开始 <a class="header-anchor" href="#快速开始" aria-label="Permalink to &quot;快速开始&quot;">​</a></h2><h3 id="安装" tabindex="-1">安装 <a class="header-anchor" href="#安装" aria-label="Permalink to &quot;安装&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># npm安装</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cesium</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 或使用yarn</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">cesium</span></span></code></pre></div><h3 id="基础使用" tabindex="-1">基础使用 <a class="header-anchor" href="#基础使用" aria-label="Permalink to &quot;基础使用&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 引入Cesium</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">*</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">as</span><span style="color:#BABED8;"> Cesium </span><span style="color:#89DDFF;font-style:italic;">from</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesium</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">import</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesium/Build/Cesium/Widgets/widgets.css</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 设置Cesium ion令牌</span></span>
<span class="line"><span style="color:#BABED8;">Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">Ion</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">defaultAccessToken </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">your_access_token</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;">// 创建Cesium Viewer</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> viewer </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">new</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">Viewer</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">cesiumContainer</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">terrainProvider</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> Cesium</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">createWorldTerrain</span><span style="color:#BABED8;">()</span></span>
<span class="line"><span style="color:#89DDFF;">}</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">;</span></span></code></pre></div><h2 id="下一步" tabindex="-1">下一步 <a class="header-anchor" href="#下一步" aria-label="Permalink to &quot;下一步&quot;">​</a></h2><ul><li><a href="/cesium/basics.html">基础配置</a> - 学习Cesium的基础配置和初始化</li><li><a href="/cesium/concepts.html">核心概念</a> - 了解Cesium的核心概念和架构</li><li><a href="/cesium/operations.html">常用操作</a> - 掌握地图操作、图层管理等基本功能</li></ul><h2 id="参考资源" tabindex="-1">参考资源 <a class="header-anchor" href="#参考资源" aria-label="Permalink to &quot;参考资源&quot;">​</a></h2><ul><li><a href="https://cesium.com/docs/" target="_blank" rel="noreferrer">Cesium官方文档</a></li><li><a href="https://sandcastle.cesium.com/" target="_blank" rel="noreferrer">Cesium Sandcastle示例</a></li><li><a href="https://github.com/CesiumGS/cesium" target="_blank" rel="noreferrer">Cesium GitHub仓库</a></li><li><a href="https://community.cesium.com/" target="_blank" rel="noreferrer">Cesium社区论坛</a></li></ul>`,17),o=[e];function r(p,i,c,d,D,y){return a(),t("div",null,o)}const m=s(l,[["render",r]]);export{u as __pageData,m as default};
