# Vue.js 代码复用最佳实践

代码复用是提高开发效率、减少维护成本的关键实践。在Vue.js项目中，合理的代码复用可以显著减少重复劳动，提高代码质量。本文档将介绍Vue.js项目中代码复用的各种策略和实践。

## 目录

[[toc]]

## 组件复用策略

### 1. 设计通用组件

设计可复用组件时的核心原则：

- **单一职责**: 每个组件只做一件事，但做好这件事
- **接口明确**: 通过清晰的props和events定义组件接口
- **低耦合**: 减少对外部环境的依赖
- **高内聚**: 相关功能应集中在一个组件内

示例 - 设计一个通用按钮组件:

```vue
<!-- BaseButton.vue -->
<template>
  <button 
    class="base-button" 
    :class="[typeClass, sizeClass, { 'is-disabled': disabled }]"
    :disabled="disabled"
    @click="handleClick"
  >
    <i v-if="icon" :class="icon"></i>
    <span v-if="$slots.default"><slot></slot></span>
  </button>
</template>

<script>
export default {
  name: 'BaseButton',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    icon: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    typeClass() {
      return this.type ? `base-button--${this.type}` : '';
    },
    sizeClass() {
      return this.size ? `base-button--${this.size}` : '';
    }
  },
  methods: {
    handleClick(event) {
      if (this.disabled) return;
      this.$emit('click', event);
    }
  }
}
</script>
```

### 2. 组件注册与使用

全局组件与局部组件的选择策略：

- **全局组件**: 适用于整个应用频繁使用的基础组件
- **局部组件**: 适用于特定功能或页面的组件

全局组件注册示例:

```js
// main.js
import Vue from 'vue';
import BaseButton from '@/components/base/BaseButton.vue';
import BaseInput from '@/components/base/BaseInput.vue';
import BaseSelect from '@/components/base/BaseSelect.vue';

// 注册全局组件
Vue.component('BaseButton', BaseButton);
Vue.component('BaseInput', BaseInput);
Vue.component('BaseSelect', BaseSelect);
```

更高效的全局组件自动注册:

```js
// registerBaseComponents.js
import Vue from 'vue';

// 自动注册 components/base 目录下的所有组件
const requireComponent = require.context(
  './components/base',
  false,
  /Base[A-Z]\w+\.(vue|js)$/
);

requireComponent.keys().forEach(fileName => {
  const componentConfig = requireComponent(fileName);
  const componentName = fileName
    .split('/')
    .pop()
    .replace(/\.\w+$/, '');
  
  Vue.component(
    componentName,
    componentConfig.default || componentConfig
  );
});
```

### 3. 使用组件组合

利用Vue的组件组合机制创建复杂UI:

```vue
<!-- 复杂表单组件 -->
<template>
  <div class="user-form">
    <BaseInput v-model="user.name" label="用户名" />
    <BaseSelect v-model="user.role" :options="roleOptions" label="角色" />
    <BaseButton type="primary" @click="saveUser">保存</BaseButton>
  </div>
</template>
```

### 4. 使用插槽扩展组件功能

通过插槽机制使组件更加灵活:

```vue
<!-- 卡片组件 -->
<template>
  <div class="card">
    <div class="card-header">
      <slot name="header">默认标题</slot>
    </div>
    <div class="card-body">
      <slot>默认内容</slot>
    </div>
    <div class="card-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>
```

使用示例:

```vue
<Card>
  <template v-slot:header>
    <h3>用户信息</h3>
  </template>
  
  <p>用户详细信息内容...</p>
  
  <template v-slot:footer>
    <BaseButton>编辑</BaseButton>
    <BaseButton type="danger">删除</BaseButton>
  </template>
</Card>
```

## 逻辑复用策略

### 1. 使用Mixins

Mixin适用于跨组件共享逻辑:

```js
// pagination-mixin.js
export default {
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  methods: {
    handlePageChange(newPage) {
      this.currentPage = newPage;
      this.fetchData();
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      this.fetchData();
    }
  }
}
```

使用Mixin:

```vue
<script>
import paginationMixin from '@/mixins/pagination-mixin';

export default {
  mixins: [paginationMixin],
  methods: {
    fetchData() {
      // 实现具体的数据获取逻辑
    }
  }
}
</script>
```

::: warning 注意
虽然Mixin是Vue 2中常用的复用方式，但它存在以下问题：
- 属性来源不明确，可能导致命名冲突
- 多个Mixin可能产生复杂的依赖关系
- 在Vue 3中，推荐使用Composition API代替Mixins
:::

### 2. 使用函数式组件

对于纯展示类的简单组件，使用函数式组件可以提高性能:

```js
// FunctionalButton.js
export default {
  functional: true,
  props: {
    type: String,
    text: String
  },
  render(h, { props, listeners, data }) {
    return h('button', {
      ...data,
      class: ['btn', `btn-${props.type || 'default'}`],
      on: {
        ...listeners
      }
    }, props.text);
  }
}
```

### 3. 高阶组件模式(HOC)

通过高阶组件为已有组件添加新功能:

```js
// withLoading.js - 高阶组件示例
import LoadingSpinner from './LoadingSpinner.vue';

export default function withLoading(Component) {
  return {
    props: ['isLoading', ...Component.props],
    render(h) {
      return h('div', [
        this.isLoading && h(LoadingSpinner),
        !this.isLoading && h(Component, {
          props: this.$props,
          on: this.$listeners,
          scopedSlots: this.$scopedSlots
        })
      ]);
    }
  };
}
```

使用高阶组件:

```js
import withLoading from '@/hoc/withLoading';
import UserList from '@/components/UserList.vue';

const UserListWithLoading = withLoading(UserList);

export default {
  components: {
    UserListWithLoading
  }
}
```

### 4. 使用插件封装复杂功能

对于需要全局共享的功能，可以封装为Vue插件:

```js
// toast-plugin.js
import ToastComponent from './Toast.vue';

const Toast = {
  install(Vue, options = {}) {
    // 创建构造器
    const ToastConstructor = Vue.extend(ToastComponent);
    
    // 创建实例方法
    Vue.prototype.$toast = function(message, type = 'info', duration = 3000) {
      const instance = new ToastConstructor({
        propsData: {
          message,
          type
        }
      });
      
      // 挂载实例
      instance.$mount();
      document.body.appendChild(instance.$el);
      
      // 自动关闭
      setTimeout(() => {
        instance.close();
      }, duration);
      
      return instance;
    };
  }
};

export default Toast;
```

使用插件:

```js
// main.js
import Vue from 'vue';
import Toast from '@/plugins/toast-plugin';

Vue.use(Toast);

// 在组件中使用
this.$toast('操作成功', 'success');
```

## 工具函数复用

### 1. 设计通用工具函数

```js
// utils/date.js
export function formatDate(date, format = 'YYYY-MM-DD') {
  // 日期格式化实现
}

export function dateFromNow(date) {
  // 计算相对时间
}

// utils/string.js
export function truncate(str, length = 30) {
  return str.length > length ? str.substring(0, length) + '...' : str;
}

// utils/validate.js
export function isEmail(value) {
  // 邮箱验证
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
}
```

### 2. 按功能组织工具函数

```
src/
  utils/
    date.js       # 日期相关
    string.js     # 字符串处理
    number.js     # 数字格式化
    validate.js   # 数据验证
    storage.js    # 本地存储
    request.js    # 网络请求
    index.js      # 入口文件
```

入口文件导出所有工具:

```js
// utils/index.js
export * from './date';
export * from './string';
export * from './number';
export * from './validate';
export * from './storage';
export * from './request';
```

### 3. 将常用逻辑封装为Hook函数

在Vue 2项目中，可以采用类似Vue 3 Composition API的风格封装逻辑:

```js
// hooks/useWindowSize.js (需要 @vue/composition-api)
import { ref, onMounted, onUnmounted } from '@vue/composition-api';

export function useWindowSize() {
  const width = ref(window.innerWidth);
  const height = ref(window.innerHeight);

  function handleResize() {
    width.value = window.innerWidth;
    height.value = window.innerHeight;
  }

  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  return { width, height };
}
```

## CSS代码复用

### 1. 设计变量系统

```scss
// styles/variables.scss
// 颜色系统
$color-primary: #1890ff;
$color-success: #52c41a;
$color-warning: #faad14;
$color-danger: #f5222d;
$color-info: #1890ff;

// 字体系统
$font-size-small: 12px;
$font-size-base: 14px;
$font-size-large: 16px;
$font-size-xlarge: 18px;

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
```

### 2. 创建通用样式类

```scss
// styles/common.scss
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mt-1 { margin-top: $spacing-xs; }
.mt-2 { margin-top: $spacing-sm; }
.mt-3 { margin-top: $spacing-md; }
// ...更多间距类
```

### 3. 使用Mixins封装复杂样式逻辑

```scss
// styles/mixins.scss
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin multi-line-ellipsis($lines) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@mixin responsive($breakpoint) {
  @if $breakpoint == phone {
    @media (max-width: 767px) { @content; }
  } @else if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1199px) { @content; }
  } @else if $breakpoint == desktop {
    @media (min-width: 1200px) { @content; }
  }
}
```

使用Mixins:

```scss
.product-title {
  @include text-ellipsis;
  font-weight: bold;
}

.product-description {
  @include multi-line-ellipsis(3);
}

.sidebar {
  width: 250px;
  
  @include responsive(phone) {
    width: 100%;
  }
}
```

## 最佳实践总结

### 代码复用的原则

1. **DRY原则** (Don't Repeat Yourself): 避免重复代码
2. **KISS原则** (Keep It Simple, Stupid): 保持简单设计
3. **YAGNI原则** (You Aren't Gonna Need It): 不要过度设计
4. **关注点分离**: 将不同功能拆分到合理的层次

### 复用层次

根据复杂度和使用频率，从低到高选择合适的复用层次:

1. **工具函数**: 最基础的逻辑复用
2. **组件**: UI和交互的复用
3. **Mixins/Hooks**: 跨组件行为逻辑复用
4. **插件**: 全局功能复用
5. **库/框架**: 抽象成独立包

### 避免过度复用

过度复用也会带来问题:
- 抽象层次过多导致代码难以理解
- 过度通用化导致性能损失
- 不必要的间接层增加心智负担

在决定是否抽象复用前，请考虑:
- 该功能是否至少会被使用3次以上?
- 抽象后是否真的更易于理解和维护?
- 复用带来的好处是否大于复杂性增加的成本?

## 参考资料

- [Vue.js官方风格指南](https://v2.vuejs.org/v2/style-guide/)
- [可复用性与组合 - Vue.js文档](https://v2.vuejs.org/v2/guide/mixins.html)
- [Evan You关于Vue组件设计的演讲](https://www.youtube.com/watch?v=7lpemgMhi0k) 