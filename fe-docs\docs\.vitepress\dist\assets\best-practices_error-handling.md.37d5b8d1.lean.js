import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"Vue.js错误处理最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/error-handling.md","filePath":"best-practices/error-handling.md"}'),p={name:"best-practices/error-handling.md"},o=l("",72),e=[o];function t(c,r,F,D,y,i){return n(),a("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
