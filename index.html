
<!DOCTYPE html>
<html>

<head>
  <title>我的第一个3D地球</title>
  <script src="https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Cesium.js"></script>
  <link href="https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
  <style>
    #cesiumContainer {
      width: 100%;
      height: 100vh;
      margin: 0;
      padding: 0;
    }
  </style>
</head>

<body>
  <div id="cesiumContainer"></div>

  <script>
    // 设置访问令牌（免费获取）
    Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.XcKpgANiY19MC4bdFUXMVEBToBmqS8kuYpUlxJHYZxY';

    // 创建3D地球
    const viewer = new Cesium.Viewer('cesiumContainer', {
      terrainProvider: Cesium.createWorldTerrain(),
      homeButton: true,
      sceneModePicker: true,
      baseLayerPicker: true,
      navigationHelpButton: true,
      animation: false,
      timeline: false
    });

    // 添加一个标记点（北京天安门）
    viewer.entities.add({
      name: '北京天安门',
      position: Cesium.Cartesian3.fromDegrees(116.391, 39.904),
      point: {
        pixelSize: 10,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '北京天安门',
        font: '14pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -40)
      }
    });

    // 飞行到北京
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 10000),
      duration: 3
    });
  </script>
</body>

</html>