# VS Code 配置指南

Visual Studio Code 是我们推荐的前端开发编辑器。本文档提供了完整的 VS Code 配置指南，包括插件推荐、配置文件和使用技巧。

## 必装插件

### Vue 开发插件

#### 1. Vetur
- **功能**：Vue 文件语法高亮、智能提示、格式化
- **安装**：`ext install octref.vetur`
- **配置**：
```json
{
  "vetur.format.defaultFormatter.html": "js-beautify-html",
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.js": "prettier",
  "vetur.validation.template": false
}
```

#### 2. Vue Language Features (Volar)
- **功能**：Vue 3 官方语言支持（如果使用 Vue 3）
- **安装**：`ext install Vue.volar`
- **注意**：与 Vetur 二选一使用

### JavaScript/TypeScript 插件

#### 3. ESLint
- **功能**：JavaScript/Vue 代码规范检查
- **安装**：`ext install dbaeumer.vscode-eslint`
- **配置**：
```json
{
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue"
  ],
  "eslint.options": {
    "extensions": [".js", ".vue"]
  }
}
```

#### 4. Prettier - Code formatter
- **功能**：代码格式化
- **安装**：`ext install esbenp.prettier-vscode`
- **配置**：
```json
{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

### 开发效率插件

#### 5. Auto Rename Tag
- **功能**：自动重命名配对的HTML/XML标签
- **安装**：`ext install formulahendry.auto-rename-tag`

#### 6. Bracket Pair Colorizer 2
- **功能**：括号配对着色
- **安装**：`ext install CoenraadS.bracket-pair-colorizer-2`

#### 7. Path Intellisense
- **功能**：文件路径自动补全
- **安装**：`ext install christian-kohler.path-intellisense`

#### 8. GitLens
- **功能**：增强Git功能，显示代码作者、提交历史等
- **安装**：`ext install eamodio.gitlens`

### CSS/SCSS 插件

#### 9. SCSS IntelliSense
- **功能**：SCSS 语法支持和智能提示
- **安装**：`ext install mrmlnc.vscode-scss`

#### 10. CSS Peek
- **功能**：快速查看CSS定义
- **安装**：`ext install pranaygp.vscode-css-peek`

## 推荐插件

### 代码质量

#### 11. SonarLint
- **功能**：代码质量检查
- **安装**：`ext install SonarSource.sonarlint-vscode`

#### 12. Code Spell Checker
- **功能**：拼写检查
- **安装**：`ext install streetsidesoftware.code-spell-checker`

### 主题和图标

#### 13. Material Icon Theme
- **功能**：Material Design 风格的文件图标
- **安装**：`ext install PKief.material-icon-theme`

#### 14. One Dark Pro
- **功能**：流行的暗色主题
- **安装**：`ext install zhuangtongfa.Material-theme`

### 实用工具

#### 15. Live Server
- **功能**：本地开发服务器
- **安装**：`ext install ritwickdey.LiveServer`

#### 16. REST Client
- **功能**：在VS Code中测试API
- **安装**：`ext install humao.rest-client`

#### 17. Import Cost
- **功能**：显示导入包的大小
- **安装**：`ext install wix.vscode-import-cost`

## VS Code 配置文件

### settings.json

在项目根目录创建 `.vscode/settings.json` 文件：

```json
{
  // 编辑器设置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  
  // 文件设置
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  
  // Vue 相关设置
  "vetur.format.defaultFormatter.html": "js-beautify-html",
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.js": "prettier",
  "vetur.validation.template": false,
  
  // ESLint 设置
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue"
  ],
  
  // Prettier 设置
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  
  // 搜索设置
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.git": true
  },
  
  // 文件关联
  "files.associations": {
    "*.vue": "vue"
  }
}
```

### extensions.json

在项目根目录创建 `.vscode/extensions.json` 文件，推荐团队成员安装的插件：

```json
{
  "recommendations": [
    "octref.vetur",
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "formulahendry.auto-rename-tag",
    "CoenraadS.bracket-pair-colorizer-2",
    "christian-kohler.path-intellisense",
    "eamodio.gitlens",
    "mrmlnc.vscode-scss",
    "pranaygp.vscode-css-peek",
    "PKief.material-icon-theme",
    "ritwickdey.LiveServer",
    "humao.rest-client",
    "wix.vscode-import-cost"
  ]
}
```

### launch.json

调试配置文件 `.vscode/launch.json`：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:8080",
      "webRoot": "${workspaceFolder}/src",
      "breakOnLoad": true,
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/*"
      }
    },
    {
      "name": "Attach to Chrome",
      "type": "chrome",
      "request": "attach",
      "port": 9222,
      "webRoot": "${workspaceFolder}/src"
    }
  ]
}
```

### tasks.json

任务配置文件 `.vscode/tasks.json`：

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "npm: serve",
      "type": "npm",
      "script": "serve",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "npm: build",
      "type": "npm",
      "script": "build",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": []
    },
    {
      "label": "npm: lint",
      "type": "npm",
      "script": "lint",
      "group": "test",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      },
      "problemMatcher": ["$eslint-stylish"]
    }
  ]
}
```

## 代码片段 (Snippets)

### Vue 组件模板

创建 `.vscode/vue.code-snippets` 文件：

```json
{
  "Vue Component": {
    "prefix": "vue",
    "body": [
      "<template>",
      "  <div class=\"$1\">",
      "    $2",
      "  </div>",
      "</template>",
      "",
      "<script>",
      "export default {",
      "  name: '$3',",
      "  components: {},",
      "  props: {},",
      "  data() {",
      "    return {",
      "      $4",
      "    }",
      "  },",
      "  computed: {},",
      "  watch: {},",
      "  created() {},",
      "  mounted() {},",
      "  methods: {",
      "    $5",
      "  }",
      "}",
      "</script>",
      "",
      "<style lang=\"scss\" scoped>",
      ".$1 {",
      "  $6",
      "}",
      "</style>"
    ],
    "description": "Vue component template"
  },
  "Vue Method": {
    "prefix": "vmethod",
    "body": [
      "$1() {",
      "  $2",
      "},"
    ],
    "description": "Vue method"
  },
  "Vue Computed": {
    "prefix": "vcomputed",
    "body": [
      "$1() {",
      "  return $2",
      "},"
    ],
    "description": "Vue computed property"
  }
}
```

## 使用技巧

### 1. 快捷键

- `Ctrl + Shift + P`：命令面板
- `Ctrl + P`：快速打开文件
- `Ctrl + Shift + F`：全局搜索
- `Alt + Shift + F`：格式化代码
- `F12`：跳转到定义
- `Shift + F12`：查找所有引用

### 2. 多光标编辑

- `Alt + Click`：添加光标
- `Ctrl + Alt + Down/Up`：在上下行添加光标
- `Ctrl + Shift + L`：选择所有匹配项

### 3. 代码折叠

- `Ctrl + Shift + [`：折叠代码块
- `Ctrl + Shift + ]`：展开代码块
- `Ctrl + K, Ctrl + 0`：折叠所有代码

### 4. 集成终端

- `Ctrl + ``：打开/关闭集成终端
- `Ctrl + Shift + ``：新建终端

## 常见问题

### Q: Vetur 和 ESLint 冲突怎么办？

A: 在 settings.json 中添加：
```json
{
  "vetur.validation.template": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false
}
```

### Q: 如何禁用某个插件的自动格式化？

A: 在 settings.json 中设置：
```json
{
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

### Q: 如何设置项目特定的配置？

A: 在项目根目录创建 `.vscode/settings.json` 文件，配置会覆盖全局设置。

## 团队协作

### 1. 统一配置

团队成员应该使用相同的 VS Code 配置，确保代码风格一致。

### 2. 插件同步

使用 Settings Sync 插件同步配置和插件列表。

### 3. 配置版本控制

将 `.vscode` 目录加入版本控制，确保团队配置同步。

通过合理配置 VS Code，可以大大提高前端开发效率和代码质量。建议团队成员定期更新插件和配置，保持开发环境的最新状态。
```
