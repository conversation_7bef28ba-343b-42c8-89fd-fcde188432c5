# 基础配置

本章将介绍如何在项目中集成Cesium，包括安装、初始化和基础配置。

## 安装与引入

### NPM安装

```bash
# 使用npm安装
npm install cesium

# 使用yarn安装
yarn add cesium

# 使用pnpm安装
pnpm add cesium
```

### CDN引入

```html
<!-- 引入Cesium CSS -->
<link href="https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Widgets/widgets.css" rel="stylesheet">

<!-- 引入Cesium JavaScript -->
<script src="https://cesium.com/downloads/cesiumjs/releases/1.110/Build/Cesium/Cesium.js"></script>
```

### ES6模块导入

```javascript
// 引入Cesium库
import * as Cesium from 'cesium';

// 引入Cesium样式
import 'cesium/Build/Cesium/Widgets/widgets.css';
```

## Ion访问令牌配置

Cesium Ion是Cesium的云端平台，提供全球高质量的3D数据。使用前需要配置访问令牌：

```javascript
// 设置Cesium Ion默认访问令牌
Cesium.Ion.defaultAccessToken = 'your_access_token_here';
```

::: tip 获取访问令牌
1. 访问 [Cesium Ion官网](https://cesium.com/ion/)
2. 注册或登录账户
3. 在控制台中获取访问令牌
4. 免费账户每月有一定的数据配额
:::

## 基础初始化

### 最简单的初始化

```javascript
// 创建最基本的Cesium Viewer
const viewer = new Cesium.Viewer('cesiumContainer');
```

### 完整的初始化配置

```javascript
const viewer = new Cesium.Viewer('cesiumContainer', {
  // 地形数据提供者
  terrainProvider: Cesium.createWorldTerrain(),
  
  // 影像图层选择器
  baseLayerPicker: true,
  
  // 地理编码搜索
  geocoder: true,
  
  // 主页按钮
  homeButton: true,
  
  // 场景模式选择器
  sceneModePicker: true,
  
  // 导航帮助按钮
  navigationHelpButton: true,
  
  // 动画控件
  animation: false,
  
  // 时间轴
  timeline: false,
  
  // 全屏按钮
  fullscreenButton: true,
  
  // VR按钮
  vrButton: false,
  
  // 信息框
  infoBox: true,
  
  // 选择指示器
  selectionIndicator: true,
  
  // 仅3D模式
  scene3DOnly: false,
  
  // 阴影
  shadows: false,
  
  // 是否显示渲染循环错误
  showRenderLoopErrors: true,
  
  // 地图模式（2D、3D、Columbus View）
  sceneMode: Cesium.SceneMode.SCENE3D,
  
  // 地图投影
  mapProjection: new Cesium.WebMercatorProjection()
});
```

## 配置选项详解

### 控件配置

```javascript
const viewer = new Cesium.Viewer('cesiumContainer', {
  // 隐藏所有默认控件
  animation: false,           // 动画控件
  baseLayerPicker: false,     // 图层选择器
  fullscreenButton: false,    // 全屏按钮
  vrButton: false,            // VR按钮
  geocoder: false,            // 搜索框
  homeButton: false,          // 主页按钮
  infoBox: false,             // 信息框
  sceneModePicker: false,     // 场景模式选择器
  selectionIndicator: false,  // 选择指示器
  timeline: false,            // 时间轴
  navigationHelpButton: false, // 导航帮助按钮
  navigationInstructionsInitiallyVisible: false
});
```

### 地形配置

```javascript
// 使用Cesium World Terrain
const viewer = new Cesium.Viewer('cesiumContainer', {
  terrainProvider: Cesium.createWorldTerrain({
    requestWaterMask: true,     // 请求水体遮罩
    requestVertexNormals: true  // 请求顶点法线
  })
});

// 使用自定义地形
const viewer = new Cesium.Viewer('cesiumContainer', {
  terrainProvider: new Cesium.CesiumTerrainProvider({
    url: 'your-terrain-server-url'
  })
});

// 不使用地形（平面）
const viewer = new Cesium.Viewer('cesiumContainer', {
  terrainProvider: new Cesium.EllipsoidTerrainProvider()
});
```

### 影像图层配置

```javascript
// 使用Bing Maps
const viewer = new Cesium.Viewer('cesiumContainer', {
  imageryProvider: new Cesium.BingMapsImageryProvider({
    url: 'https://dev.virtualearth.net',
    key: 'your-bing-maps-key',
    mapStyle: Cesium.BingMapsStyle.AERIAL_WITH_LABELS
  })
});

// 使用OpenStreetMap
const viewer = new Cesium.Viewer('cesiumContainer', {
  imageryProvider: new Cesium.OpenStreetMapImageryProvider({
    url: 'https://a.tile.openstreetmap.org/'
  })
});

// 使用天地图
const viewer = new Cesium.Viewer('cesiumContainer', {
  imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
    url: 'http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={TileMatrix}&TILEROW={TileRow}&TILECOL={TileCol}&tk=your-tianditu-key',
    layer: 'img',
    style: 'default',
    format: 'tiles',
    tileMatrixSetID: 'w'
  })
});
```

## 初始视角设置

### 设置初始相机位置

```javascript
// 创建viewer后设置初始位置
viewer.camera.setView({
  destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 1500), // 经度、纬度、高度
  orientation: {
    heading: Cesium.Math.toRadians(0),    // 航向角
    pitch: Cesium.Math.toRadians(-45),    // 俯仰角
    roll: 0.0                             // 翻滚角
  }
});

// 或者使用flyTo实现平滑过渡
viewer.camera.flyTo({
  destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 1500),
  orientation: {
    heading: Cesium.Math.toRadians(0),
    pitch: Cesium.Math.toRadians(-45),
    roll: 0.0
  },
  duration: 3 // 飞行时间（秒）
});
```

### 设置默认主页位置

```javascript
// 设置主页按钮的默认位置
viewer.homeButton.viewModel.command.beforeExecute.addEventListener(function(e) {
  e.cancel = true;
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(116.391, 39.904, 10000)
  });
});
```

## 场景配置

### 光照配置

```javascript
// 启用阴影
viewer.shadows = true;

// 配置太阳光照
viewer.scene.globe.enableLighting = true;

// 配置环境光
viewer.scene.globe.atmosphere.brightnessShift = 0.4;
```

### 性能配置

```javascript
// 启用抗锯齿
viewer.scene.postProcessStages.fxaa.enabled = true;

// 配置最大屏幕空间误差
viewer.scene.globe.maximumScreenSpaceError = 2;

// 配置瓦片缓存大小
viewer.scene.globe.tileCacheSize = 1000;

// 启用渐进式渲染
viewer.scene.requestRenderMode = true;
viewer.scene.maximumRenderTimeChange = Infinity;
```

### 相机控制配置

```javascript
// 限制相机缩放范围
viewer.scene.screenSpaceCameraController.minimumZoomDistance = 1000;
viewer.scene.screenSpaceCameraController.maximumZoomDistance = 50000;

// 限制相机俯仰角
viewer.scene.screenSpaceCameraController.constrainedAxis = Cesium.Cartesian3.UNIT_Z;

// 禁用地形碰撞检测
viewer.scene.screenSpaceCameraController.enableCollisionDetection = false;

// 配置惯性
viewer.scene.screenSpaceCameraController.inertiaSpin = 0.9;
viewer.scene.screenSpaceCameraController.inertiaTranslate = 0.9;
viewer.scene.screenSpaceCameraController.inertiaZoom = 0.8;
```

## 样式配置

### 自定义CSS样式

```css
/* 隐藏Cesium信用信息 */
.cesium-widget-credits {
  display: none !important;
}

/* 自定义加载动画 */
.cesium-viewer-loadingIndicator {
  background-color: rgba(42, 42, 42, 0.8);
}

/* 自定义工具栏样式 */
.cesium-viewer-toolbar {
  background-color: rgba(42, 42, 42, 0.8);
  border-radius: 5px;
}

/* 自定义时间轴样式 */
.cesium-viewer-timelineContainer {
  background-color: rgba(42, 42, 42, 0.8);
}

/* 自定义动画控件样式 */
.cesium-viewer-animationContainer {
  background-color: rgba(42, 42, 42, 0.8);
  border-radius: 5px;
}
```

### HTML容器配置

```html
<div id="cesiumContainer" style="width: 100%; height: 100vh;"></div>
```

```css
#cesiumContainer {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: sans-serif;
}

/* 确保容器父元素也有正确的样式 */
html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
```

## 常见问题

### 1. 黑屏或空白

**问题原因：**
- Ion访问令牌未配置或无效
- 网络连接问题
- 容器尺寸为0

**解决方案：**
```javascript
// 检查令牌是否设置
console.log('Access Token:', Cesium.Ion.defaultAccessToken);

// 确保容器有正确的尺寸
const container = document.getElementById('cesiumContainer');
console.log('Container size:', container.offsetWidth, container.offsetHeight);

// 监听viewer准备完成事件
viewer.scene.globe.tileLoadProgressEvent.addEventListener(function(value) {
  console.log('Tile load progress:', value);
});
```

### 2. 性能问题

**优化建议：**
```javascript
// 减少最大屏幕空间误差
viewer.scene.globe.maximumScreenSpaceError = 4;

// 启用请求渲染模式
viewer.scene.requestRenderMode = true;

// 禁用不必要的效果
viewer.scene.fog.enabled = false;
viewer.scene.skyAtmosphere.show = false;
```

### 3. 跨域问题

**解决方案：**
```javascript
// 配置代理或使用CORS友好的服务
const imageryProvider = new Cesium.WebMapServiceImageryProvider({
  url: '/api/proxy/wms',  // 通过代理访问
  layers: 'your-layer'
});
``` 