{"name": "fe-docs", "version": "1.0.0", "description": "公司前端技术文档", "main": "index.js", "scripts": {"dev": "vitepress dev docs", "build": "vitepress build docs", "docs:serve": "vitepress serve docs", "docs:preview": "vitepress preview docs", "prepare": "husky", "lint": "eslint . --ext .js,.vue", "lint:fix": "eslint . --ext .js,.vue --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "keywords": ["前端", "文档", "VitePress"], "author": "", "license": "ISC", "dependencies": {"vitepress": "^1.0.0-alpha.75", "vue": "^3.3.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "sass": "^1.90.0", "vue-eslint-parser": "^10.2.0"}, "lint-staged": {"*.{js,vue}": ["eslint --fix", "prettier --write"], "*.{md,json,css,scss}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}