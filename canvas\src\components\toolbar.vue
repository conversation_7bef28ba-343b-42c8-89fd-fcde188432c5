<!--工具栏 -->
<template>
	<div class="">
		<el-card>
			<el-radio-group v-model="mouseMode" @input="changeMouse">
				<el-radio label="MOVE_MODE">移动</el-radio>
				<el-radio label="LINE_MODE">画笔</el-radio>
				<el-radio label="ERASER_MODE">橡皮檫</el-radio>
			</el-radio-group>
			<div>
				<el-color-picker v-model="color"></el-color-picker>
			</div>

			<div class="block">
				<span class="demonstration">自定义初始值</span>
				<el-slider min="0.1" max="2" step="0.1" v-model="slider"></el-slider>
			</div>
		</el-card>
	</div>
</template>

<script>
export default {
	components: {},
	data() {
		return {
			color: "#3d6",
			slider: 1,
		};
	},
	computed: {},
	watch: {},
	methods: {},
	created() {},
	mounted() {},
	updated() {},
	destroyed() {},
};
</script>
<style scoped lang="less">
.a {
	background: #fff;
}
</style>
