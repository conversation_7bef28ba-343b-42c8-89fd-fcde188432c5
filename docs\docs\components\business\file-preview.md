# FilePreview 文件预览组件

支持多种文件格式在线预览的组件，集成了图片预览、文档预览和通用文件预览功能。

## 功能特性

- 📄 支持多种文档格式：doc, docx, xls, xlsx, ppt, pptx, pdf 等
- 🖼️ 支持图片格式：jpg, jpeg, png, gif 等
- 🔍 图片支持放大预览和预览列表
- 📝 集成在线文档编辑器
- 🌐 不支持的格式通过iframe方式预览
- 📏 自适应高度和响应式设计

## 基础用法

### 图片预览

```vue
<template>
  <div>
    <file-preview
      name="example.jpg"
      url="/uploads/example.jpg"
      :height="400"
      :preview-src-list="true"
    />
  </div>
</template>
```

### 文档预览

```vue
<template>
  <div>
    <file-preview
      name="document.pdf"
      url="/uploads/document.pdf"
      :height="600"
      @close="handleClose"
    />
  </div>
</template>

<script>
export default {
  methods: {
    handleClose() {
      console.log('文档预览关闭')
    }
  }
}
</script>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 | 是否必填 |
|------|------|------|-------|--------|----------|
| name | 文件名称（用于判断文件类型） | String | — | — | 是 |
| url | 文件地址 | String | — | — | 是 |
| height | 预览区域高度 | Number/String | — | 600 | 否 |
| relativePath | 是否为相对路径 | Boolean | true/false | true | 否 |
| previewSrcList | 图片是否支持预览列表功能 | Boolean | true/false | false | 否 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| close | 文档编辑器关闭时触发 | — |

### 支持的文件类型

#### 图片类型
`jpg`, `jpeg`, `png`, `gif`

#### 文档类型
`csv`, `djvu`, `doc`, `docm`, `docx`, `docxf`, `dot`, `dotm`, `dotx`, `epub`, `fb2`, `fodp`, `fods`, `fodt`, `htm`, `html`, `mht`, `odp`, `ods`, `odt`, `oform`, `otp`, `ots`, `ott`, `oxps`, `pdf`, `pot`, `potm`, `potx`, `pps`, `ppsm`, `ppsx`, `ppt`, `pptm`, `pptx`, `rtf`, `txt`, `xls`, `xlsb`, `xlsm`, `xlsx`, `xlt`, `xltm`, `xltx`, `xml`, `xps`

## 使用示例

### 文件管理列表

```vue
<template>
  <div>
    <el-table :data="fileList" @row-click="previewFile">
      <el-table-column prop="name" label="文件名" />
      <el-table-column prop="size" label="大小" />
      <el-table-column prop="uploadTime" label="上传时间" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" @click.stop="previewFile(scope.row)">预览</el-button>
          <el-button size="mini" @click.stop="downloadFile(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-dialog 
      :visible.sync="previewVisible" 
      :title="currentFile.name"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <file-preview
        v-if="currentFile.name"
        :name="currentFile.name"
        :url="currentFile.url"
        :height="500"
        @close="handlePreviewClose"
      />
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      previewVisible: false,
      currentFile: {},
      fileList: [
        { 
          name: 'report.pdf', 
          url: '/files/report.pdf', 
          size: '2.3MB', 
          uploadTime: '2024-01-15 10:30' 
        },
        { 
          name: 'image.jpg', 
          url: '/files/image.jpg', 
          size: '1.5MB', 
          uploadTime: '2024-01-14 16:20' 
        },
        { 
          name: 'document.docx', 
          url: '/files/document.docx', 
          size: '856KB', 
          uploadTime: '2024-01-13 09:15' 
        }
      ]
    }
  },
  methods: {
    previewFile(file) {
      this.currentFile = file
      this.previewVisible = true
    },
    
    downloadFile(file) {
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.name
      link.click()
    },
    
    handlePreviewClose() {
      this.previewVisible = false
      this.currentFile = {}
    }
  }
}
</script>
```

### 图片展示墙

```vue
<template>
  <div>
    <div class="image-gallery">
      <div 
        v-for="(image, index) in images" 
        :key="index"
        class="image-item"
        @click="previewImage(image, index)"
      >
        <img :src="getThumbUrl(image.url)" :alt="image.name" />
        <div class="image-name">{{ image.name }}</div>
      </div>
    </div>
    
    <el-dialog 
      :visible.sync="imagePreviewVisible" 
      :title="currentImage.name"
      width="70%"
    >
      <file-preview
        v-if="currentImage.name"
        :name="currentImage.name"
        :url="currentImage.url"
        :height="400"
        :preview-src-list="true"
      />
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      imagePreviewVisible: false,
      currentImage: {},
      images: [
        { name: 'photo1.jpg', url: '/images/photo1.jpg' },
        { name: 'photo2.png', url: '/images/photo2.png' },
        { name: 'photo3.gif', url: '/images/photo3.gif' }
      ]
    }
  },
  methods: {
    previewImage(image, index) {
      this.currentImage = image
      this.imagePreviewVisible = true
    },
    
    getThumbUrl(url) {
      // 生成缩略图URL
      return url.replace(/\.(jpg|jpeg|png|gif)$/i, '_thumb.$1')
    }
  }
}
</script>

<style scoped>
.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  padding: 20px;
}

.image-item {
  text-align: center;
  cursor: pointer;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  transition: transform 0.2s;
}

.image-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.image-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.image-name {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}
</style>
```

### 合同文档管理

```vue
<template>
  <div>
    <el-card header="合同文档管理">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card>
            <div slot="header">待审核合同</div>
            <el-list>
              <el-list-item 
                v-for="contract in pendingContracts" 
                :key="contract.id"
                @click="reviewContract(contract)"
              >
                <div class="contract-item">
                  <i class="el-icon-document"></i>
                  <span>{{ contract.name }}</span>
                  <el-tag size="mini" type="warning">待审核</el-tag>
                </div>
              </el-list-item>
            </el-list>
          </el-card>
        </el-col>
        
        <el-col :span="16">
          <el-card>
            <div slot="header">文档预览</div>
            <file-preview
              v-if="selectedContract.name"
              :name="selectedContract.name"
              :url="selectedContract.url"
              :height="600"
            />
            <div v-else class="empty-preview">
              请选择要预览的合同文档
            </div>
            
            <div class="review-actions" v-if="selectedContract.id">
              <el-button type="success" @click="approveContract">批准</el-button>
              <el-button type="danger" @click="rejectContract">拒绝</el-button>
              <el-button @click="addComment">添加备注</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedContract: {},
      pendingContracts: [
        { 
          id: 1, 
          name: '采购合同-001.pdf', 
          url: '/contracts/purchase-001.pdf',
          status: 'pending'
        },
        { 
          id: 2, 
          name: '服务协议-002.docx', 
          url: '/contracts/service-002.docx',
          status: 'pending'
        }
      ]
    }
  },
  methods: {
    reviewContract(contract) {
      this.selectedContract = contract
    },
    
    approveContract() {
      this.$confirm('确认批准此合同？', '提示').then(() => {
        console.log('合同已批准:', this.selectedContract)
        this.$message.success('合同已批准')
        this.updateContractStatus('approved')
      })
    },
    
    rejectContract() {
      this.$confirm('确认拒绝此合同？', '提示').then(() => {
        console.log('合同已拒绝:', this.selectedContract)
        this.$message.success('合同已拒绝')
        this.updateContractStatus('rejected')
      })
    },
    
    addComment() {
      this.$prompt('请输入备注信息', '添加备注').then(({ value }) => {
        console.log('添加备注:', value)
        this.$message.success('备注已添加')
      })
    },
    
    updateContractStatus(status) {
      // 更新合同状态
      this.selectedContract.status = status
      // 从待审核列表中移除
      const index = this.pendingContracts.findIndex(c => c.id === this.selectedContract.id)
      if (index > -1) {
        this.pendingContracts.splice(index, 1)
      }
      this.selectedContract = {}
    }
  }
}
</script>

<style scoped>
.contract-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
}

.contract-item:hover {
  background: #f5f7fa;
}

.contract-item i {
  margin-right: 10px;
  color: #409eff;
}

.contract-item span {
  flex: 1;
  margin-right: 10px;
}

.empty-preview {
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  background: #f5f7fa;
}

.review-actions {
  margin-top: 20px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #eee;
}
</style>
```

## 配置说明

### 文档服务器配置

组件依赖外部文档服务器进行文档预览，需要在项目配置中设置：

```js
// config.js
export const systemDefault = {
  other: {
    documentServer: 'https://your-document-server.com'
  }
}
```

### 用户信息配置

文档编辑功能需要用户信息，确保Vuex中有正确的用户状态：

```js
// store/user.js
const state = {
  nickName: '用户昵称',
  name: '用户名',
  avatar: '头像地址'
}
```

## 注意事项

1. **文档服务器**：需要配置支持的文档预览服务器
2. **文件路径**：确保文件路径正确且服务器可访问
3. **权限控制**：大文件预览时注意权限和带宽限制
4. **浏览器兼容**：部分文档格式可能存在浏览器兼容性问题
5. **安全考虑**：预览外部文件时要注意安全性

## 常见问题

### Q: 为什么文档无法预览？

A: 检查以下几点：
- 文档服务器是否正常运行
- 文件路径是否正确
- 文件格式是否支持
- 网络连接是否正常

### Q: 如何添加新的文件类型支持？

A: 在组件的 `documentTypes` 或 `imageTypes` 数组中添加新的文件扩展名。

### Q: 大文件预览很慢怎么办？

A: 可以考虑：
- 使用文件缩略图
- 实现分页预览
- 优化文档服务器性能
- 使用CDN加速

## 源码实现

<details>
<summary>📄 查看完整源码</summary>

```vue
<template>
  <div :style="'width: 100%; height: '+ boxHeight" v-loading="loading">
    <el-image
      v-if="type === 'image'"
      :src="src"
      :preview-src-list="this.previewSrcList ? [this.src] : null"
      fit="contain"
      style="width: 100%; height: 100%;display: block;"
      @load="loading = false"
      @error="loading = false"
    >
      <div slot="error" class="image-slot">
        <i class="el-icon-picture-outline"></i>
      </div>
    </el-image>
    <document-editor
      v-else-if="type === 'document' && key"
      ref="documentEditor"
      action="view"
      :config="config"
      :document-server="documentServer"
      :identifier="key"
      :name="name"
      :secret="secret"
      :url="src"
      :callback-url="callbackUrl"
      :username="username"
      @onAppReady="onDocumentReady"
      @onError="onDocumentError"
    />
    <iframe
      v-else-if="type === 'unknown'"
      ref="iframe"
      :src="src"
      :title="name"
      width="100%"
      height="100%"
    />
  </div>
</template>

<script>
import DocumentEditor from "@zhy/document-editor";
import {getFileIdentifier} from "@/api/file";
import {systemDefault} from '/public/static/config.js';

export default {
  name: 'FilePreview',
  components: {DocumentEditor},
  props: {
    name: {
      type: String,
      default: null,
      required: true
    },
    url: {
      type: String,
      default: null,
      required: true
    },
    height: {
      type: [Number,String],
      default: 600
    },
    relativePath: {
      type: Boolean,
      default: true
    },
    previewSrcList: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: true,
      documentTypes: ['.csv', '.djvu', '.doc', '.docm', '.docx', '.docxf', '.dot', '.dotm', '.dotx',
        '.epub', '.fb2', '.fodp', '.fods', '.fodt', '.htm', '.html', '.mht', '.odp', '.ods', '.odt',
        '.oform', '.otp', '.ots', '.ott', '.oxps', '.pdf', '.pot', '.potm', '.potx', '.pps', '.ppsm',
        '.ppsx', '.ppt', '.pptm', '.pptx', '.rtf', '.txt', '.xls', '.xlsb', '.xlsm', '.xlsx', '.xlt',
        '.xltm', '.xltx', '.xml', '.xps'],
      imageTypes: ['.jpg', '.jpeg', '.png', '.gif'],
      documentServer: systemDefault.other.documentServer,
      key: null,
      secret: "zhy",
      config: {
        editorConfig: {
          mode: "view",
          customization: {
            close: {
              visible: true,
              text: "关闭文件"
            },
            customer: {
              address: "山东省淄博高新区仪器仪表产业园10号楼",
              logo: "https://www.zhywater.com:40001/web-apps/logo/logo2.svg",
              mail: "<EMAIL>",
              name: "智洋创新科技股份有限公司",
              phone: "0533-3586816",
              www: "http://www.zhiyang.com.cn"
            },
            hideRightMenu: true,
            logo: {
              image: "https://www.zhywater.com:40001/web-apps/logo/logo.svg",
              url: "http://www.zhiyang.com.cn"
            },
            uiTheme: "theme-classic-light"
          }
        },
        events: {
          onRequestEditRights: this.onRequestEditRights,
          onRequestClose: this.onRequestClose,
          onRequestUsers: this.onRequestUsers
        }
      }
    }
  },
  computed: {
    type() {
      let ext = this.name.substring(this.name.lastIndexOf('.')).toLowerCase()
      if (this.imageTypes.includes(ext)) {
        return 'image'
      } else if (this.documentTypes.includes(ext)) {
        return 'document'
      } else {
        return 'unknown'
      }
    },
    username() {
      let nickName = this.$store.state.user.nickName;
      let username = this.$store.state.user.name;
      return nickName || username || "匿名";
    },
    src() {
      if (!this.relativePath) {
        return this.url
      }
      let host = window.location.origin + process.env.VUE_APP_BASE_API
      return host + '/file/static' + this.url
    },
    callbackUrl() {
      let host = window.location.origin + process.env.VUE_APP_BASE_API
      return host + "/file/callback?name=" + this.name + "&url=" + this.url;
    },
    change() {
      return this.name + this.url + this.height;
    },
    boxHeight() {
      if(typeof this.height === 'number') {
        return `${this.height}px`
      } else {
        return this.height
      }
    }
  },
  watch: {
    change: {
      handler() {
        this.key = null;
        this.getFileKey(this.url);
      },
      immediate: true
    }
  },
  mounted() {
    if (this.type === 'unknown') {
      const iframe = this.$refs.iframe
      iframe.onload = () => {
        this.loading = false;
      }
      iframe.onerror = () => {
        this.loading = false;
      }
    }
  },
  methods: {
    async getFileKey(url) {
      const response = await getFileIdentifier({url: url});
      this.key = response.data;
    },
    onDocumentReady() {
      this.loading = false;
    },
    onDocumentError() {
      this.loading = false;
    },
    onRequestEditRights() {
      this.config.editorConfig.mode = "edit";
    },
    onRequestClose() {
      this.$emit("close");
    },
    onRequestUsers(event) {
      let data = event.data;
      let userId = require("md5")(this.username);

      if (data.c === "info" && data.id.indexOf(userId) !== -1) {
        let imageUrl = this.$store.state.user.avatar.startsWith(window.location.origin)
          ? this.$store.state.user.avatar
          : window.location.origin + this.$store.state.user.avatar;
        this.convertImageToBase64(imageUrl, (image) => {
          this.$refs.documentEditor.docEditor.setUsers({
            c: data.c,
            users: [{id: userId, image: image}]
          });
        });
      }
    },
    convertImageToBase64(imageUrl, callback) {
      fetch(imageUrl)
        .then(response => response.blob())
        .then(blob => {
          const reader = new FileReader();
          reader.onloadend = function () {
            callback(reader.result);
          };
          reader.readAsDataURL(blob);
        })
        .catch(_ => {
          callback(null);
        });
    }
  }
}
</script>
```

</details>
