// ESLint v9 Flat Config
// Replace legacy .eslintrc.* and .eslintignore

const vue = require('eslint-plugin-vue')
const vueParser = require('vue-eslint-parser')

/** @type {import('eslint').Linter.FlatConfig[]} */
module.exports = [
    // Ignored files (migrated from .eslintignore / .prettierignore)
    {
        ignores: [
            'node_modules/**',
            'dist/**',
            'docs/.vitepress/dist/**',
            'coverage/**',
            '.vscode/**',
            '.idea/**',
            '**/*.min.js',
            '**/*.d.ts',
            'package-lock.json',
            'yarn.lock',
            'pnpm-lock.yaml',
        ],
    },

    // Base JS/Vue rules
    {
        files: ['**/*.js', '**/*.vue'],
        languageOptions: {
            parser: vueParser,
            parserOptions: {
                ecmaVersion: 2021,
                sourceType: 'module',
            },
            ecmaVersion: 2021,
            sourceType: 'module',
        },
        plugins: { vue },
        rules: {
            'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
            'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',

            // Vue recommended baseline
            'vue/multi-word-component-names': 'off',
            'vue/no-v-html': 'off',
        },
    },
]
