import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const E=JSON.parse('{"title":"Vue.js组件设计最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/component-design.md","filePath":"best-practices/component-design.md"}'),p={name:"best-practices/component-design.md"},o=l("",121),e=[o];function t(c,r,D,F,y,i){return a(),n("div",null,e)}const A=s(p,[["render",t]]);export{E as __pageData,A as default};
