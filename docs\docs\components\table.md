# 表格组件

基于 Element UI Table 封装的通用表格组件，提供开箱即用的表格功能，支持分页、排序、筛选、多选等常用操作。

## 设计理念

- **简单易用**：一个配置对象即可完成复杂表格
- **功能全面**：支持分页、排序、筛选、多选、操作列等
- **高度可配置**：支持自定义渲染、插槽扩展
- **性能优化**：支持虚拟滚动、懒加载等性能优化方案

## 基础用法

```vue
<template>
  <CommonTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    @refresh="handleRefresh"
  />
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: [
        {
          id: 1,
          name: '张三',
          age: 25,
          address: '北京市朝阳区',
          status: 1
        },
        {
          id: 2,
          name: '李四',
          age: 30,
          address: '上海市浦东新区',
          status: 0
        }
      ],
      columns: [
        {
          prop: 'name',
          label: '姓名',
          width: 120
        },
        {
          prop: 'age',
          label: '年龄',
          width: 80,
          sortable: true
        },
        {
          prop: 'address',
          label: '地址',
          minWidth: 200
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: (h, { row }) => {
            return h('el-tag', {
              props: {
                type: row.status === 1 ? 'success' : 'danger'
              }
            }, row.status === 1 ? '正常' : '禁用')
          }
        }
      ]
    }
  },
  methods: {
    handleRefresh() {
      this.loading = true
      // 刷新数据逻辑
      setTimeout(() => {
        this.loading = false
      }, 1000)
    }
  }
}
</script>
```

## 带分页的表格

```vue
<template>
  <CommonTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    :pagination="pagination"
    @refresh="handleRefresh"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: [],
      columns: [
        // 列配置
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        showTotal: true,
        showSizeChanger: true,
        pageSizes: [10, 20, 50, 100]
      }
    }
  },
  methods: {
    handleRefresh() {
      this.loadData()
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadData()
    },
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          size: this.pagination.pageSize
        }
        const res = await this.$api.getTableData(params)
        this.tableData = res.data.list
        this.pagination.total = res.data.total
      } catch (error) {
        this.$message.error('数据加载失败')
      } finally {
        this.loading = false
      }
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>
```

## 带操作列的表格

```vue
<template>
  <CommonTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    selection
    @selection-change="handleSelectionChange"
  >
    <!-- 操作列插槽 -->
    <template #actions="{ row, index }">
      <el-button
        type="text"
        size="small"
        @click="handleEdit(row, index)"
      >
        编辑
      </el-button>
      <el-button
        type="text"
        size="small"
        style="color: #f56c6c"
        @click="handleDelete(row, index)"
      >
        删除
      </el-button>
      <el-dropdown @command="handleCommand">
        <el-button type="text" size="small">
          更多<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="`view-${row.id}`">查看详情</el-dropdown-item>
          <el-dropdown-item :command="`copy-${row.id}`">复制</el-dropdown-item>
          <el-dropdown-item :command="`export-${row.id}`">导出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>

    <!-- 状态列插槽 -->
    <template #status="{ row }">
      <el-switch
        v-model="row.status"
        :active-value="1"
        :inactive-value="0"
        active-text="启用"
        inactive-text="禁用"
        @change="handleStatusChange(row)"
      />
    </template>

    <!-- 头像列插槽 -->
    <template #avatar="{ row }">
      <el-avatar
        :size="40"
        :src="row.avatar"
        :alt="row.name"
      >
        {{ row.name.charAt(0) }}
      </el-avatar>
    </template>

    <!-- 工具栏左侧插槽 -->
    <template #toolbar-left>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleAdd"
      >
        新增用户
      </el-button>
      <el-button
        type="danger"
        size="small"
        icon="el-icon-delete"
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </template>

    <!-- 工具栏右侧插槽 -->
    <template #toolbar-right>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索用户"
        size="small"
        style="width: 200px; margin-right: 8px"
        clearable
        @input="handleSearch"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-download"
        @click="handleExport"
      >
        导出
      </el-button>
    </template>
  </CommonTable>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: [
        {
          id: 1,
          name: '张三',
          age: 25,
          address: '北京市朝阳区',
          status: 1,
          avatar: 'https://example.com/avatar1.jpg'
        },
        {
          id: 2,
          name: '李四',
          age: 30,
          address: '上海市浦东新区',
          status: 0,
          avatar: 'https://example.com/avatar2.jpg'
        }
      ],
      selectedRows: [],
      searchKeyword: '',
      columns: [
        {
          prop: 'avatar',
          label: '头像',
          width: 80,
          slot: 'avatar'
        },
        {
          prop: 'name',
          label: '姓名',
          width: 120
        },
        {
          prop: 'age',
          label: '年龄',
          width: 80
        },
        {
          prop: 'address',
          label: '地址',
          minWidth: 200
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          slot: 'status'
        },
        {
          label: '操作',
          width: 200,
          fixed: 'right',
          slot: 'actions'
        }
      ]
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    handleEdit(row, index) {
      console.log('编辑', row, index)
    },
    handleDelete(row, index) {
      this.$confirm('确认删除该条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData.splice(index, 1)
        this.$message.success('删除成功')
      })
    },
    handleCommand(command) {
      const [action, id] = command.split('-')
      console.log(`执行${action}操作，ID: ${id}`)
    },
    handleStatusChange(row) {
      console.log('状态改变', row)
      this.$message.success(`用户${row.name}状态已更新`)
    },
    handleAdd() {
      console.log('新增用户')
    },
    handleBatchDelete() {
      console.log('批量删除', this.selectedRows)
    },
    handleSearch() {
      console.log('搜索', this.searchKeyword)
    },
    handleExport() {
      console.log('导出数据')
    }
  }
}
</script>
```

## 插槽使用详解

### 列插槽

表格组件支持为每一列自定义内容，只需在列配置中指定 `slot` 属性，然后在模板中使用对应的插槽：

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 自定义状态列 -->
    <template #status="{ row, column, index }">
      <el-tag :type="row.status === 1 ? 'success' : 'danger'">
        {{ row.status === 1 ? '正常' : '禁用' }}
      </el-tag>
    </template>

    <!-- 自定义操作列 -->
    <template #actions="{ row, column, index }">
      <el-button-group>
        <el-button size="mini" @click="handleView(row)">查看</el-button>
        <el-button size="mini" type="primary" @click="handleEdit(row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="handleDelete(row, index)">删除</el-button>
      </el-button-group>
    </template>

    <!-- 自定义图片列 -->
    <template #image="{ row }">
      <el-image
        style="width: 50px; height: 50px"
        :src="row.imageUrl"
        :preview-src-list="[row.imageUrl]"
        fit="cover"
      />
    </template>

    <!-- 自定义进度列 -->
    <template #progress="{ row }">
      <el-progress
        :percentage="row.progress"
        :status="row.progress === 100 ? 'success' : null"
        :stroke-width="8"
      />
    </template>

    <!-- 自定义评分列 -->
    <template #rating="{ row }">
      <el-rate
        v-model="row.rating"
        disabled
        show-score
        text-color="#ff9900"
      />
    </template>
  </CommonTable>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          prop: 'name',
          label: '姓名',
          width: 120
        },
        {
          prop: 'imageUrl',
          label: '头像',
          width: 80,
          slot: 'image'
        },
        {
          prop: 'progress',
          label: '完成进度',
          width: 150,
          slot: 'progress'
        },
        {
          prop: 'rating',
          label: '评分',
          width: 150,
          slot: 'rating'
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          slot: 'status'
        },
        {
          label: '操作',
          width: 200,
          fixed: 'right',
          slot: 'actions'
        }
      ],
      tableData: [
        {
          id: 1,
          name: '张三',
          imageUrl: 'https://example.com/avatar1.jpg',
          progress: 75,
          rating: 4.5,
          status: 1
        }
      ]
    }
  }
}
</script>
```

### 工具栏插槽

工具栏提供了左右两个插槽，用于自定义操作按钮：

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 工具栏左侧 - 主要操作 -->
    <template #toolbar-left>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">
        新增
      </el-button>
      <el-button 
        type="danger" 
        size="small" 
        icon="el-icon-delete"
        :disabled="selectedRows.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
      <el-upload
        action="/api/upload"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-success="onUploadSuccess"
      >
        <el-button size="small" icon="el-icon-upload2">
          批量导入
        </el-button>
      </el-upload>
    </template>

    <!-- 工具栏右侧 - 辅助功能 -->
    <template #toolbar-right>
      <el-select
        v-model="statusFilter"
        placeholder="状态筛选"
        size="small"
        clearable
        style="width: 120px; margin-right: 8px"
        @change="handleFilter"
      >
        <el-option label="全部" value="" />
        <el-option label="启用" :value="1" />
        <el-option label="禁用" :value="0" />
      </el-select>
      
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        size="small"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 240px; margin-right: 8px"
        @change="handleDateFilter"
      />
      
      <el-input
        v-model="searchKeyword"
        placeholder="搜索关键词"
        size="small"
        style="width: 200px; margin-right: 8px"
        clearable
        @input="handleSearch"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      
      <el-dropdown @command="handleExport">
        <el-button size="small" icon="el-icon-download">
          导出<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
          <el-dropdown-item command="csv">导出CSV</el-dropdown-item>
          <el-dropdown-item command="pdf">导出PDF</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
  </CommonTable>
</template>
```

### 展开行插槽

对于需要显示详细信息的表格，可以使用展开行插槽：

```vue
<template>
  <CommonTable 
    :columns="columns" 
    :data="tableData" 
    expand
  >
    <!-- 展开行内容 -->
    <template #expand="{ row, index }">
      <div style="padding: 20px;">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>基本信息</h4>
            <p><strong>用户ID：</strong>{{ row.id }}</p>
            <p><strong>邮箱：</strong>{{ row.email }}</p>
            <p><strong>电话：</strong>{{ row.phone }}</p>
            <p><strong>注册时间：</strong>{{ row.createTime }}</p>
          </el-col>
          <el-col :span="12">
            <h4>扩展信息</h4>
            <p><strong>部门：</strong>{{ row.department }}</p>
            <p><strong>职位：</strong>{{ row.position }}</p>
            <p><strong>上次登录：</strong>{{ row.lastLogin }}</p>
            <p><strong>登录次数：</strong>{{ row.loginCount }}</p>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <h4>操作历史</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in row.activities"
            :key="index"
            :timestamp="activity.timestamp"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </template>
  </CommonTable>
</template>
```

### 复杂列组合示例

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 用户信息列 -->
    <template #userInfo="{ row }">
      <div style="display: flex; align-items: center;">
        <el-avatar :size="40" :src="row.avatar">{{ row.name.charAt(0) }}</el-avatar>
        <div style="margin-left: 10px;">
          <div style="font-weight: bold;">{{ row.name }}</div>
          <div style="font-size: 12px; color: #999;">{{ row.email }}</div>
        </div>
      </div>
    </template>

    <!-- 标签列 -->
    <template #tags="{ row }">
      <el-tag
        v-for="tag in row.tags"
        :key="tag"
        size="mini"
        style="margin-right: 4px;"
        :type="getTagType(tag)"
      >
        {{ tag }}
      </el-tag>
    </template>

    <!-- 操作列 -->
    <template #actions="{ row, index }">
      <el-button-group>
        <el-tooltip content="查看详情" placement="top">
          <el-button size="mini" icon="el-icon-view" @click="handleView(row)" />
        </el-tooltip>
        <el-tooltip content="编辑" placement="top">
          <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleEdit(row)" />
        </el-tooltip>
        <el-tooltip content="删除" placement="top">
          <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(row, index)" />
        </el-tooltip>
      </el-button-group>
    </template>
  </CommonTable>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        {
          prop: 'userInfo',
          label: '用户信息',
          width: 250,
          slot: 'userInfo'
        },
        {
          prop: 'tags',
          label: '标签',
          width: 200,
          slot: 'tags'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180
        },
        {
          label: '操作',
          width: 120,
          fixed: 'right',
          slot: 'actions'
        }
      ]
    }
  },
  methods: {
    getTagType(tag) {
      const typeMap = {
        'VIP': 'warning',
        '管理员': 'danger',
        '普通用户': 'info'
      }
      return typeMap[tag] || 'primary'
    }
  }
}
</script>
```



#### 4. 交互式插槽

包含用户交互的插槽：

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 可编辑单元格 -->
    <template #editableCell="{ row, column, index }">
      <div v-if="editingCell === `${index}-${column.prop}`">
        <el-input
          v-model="editValue"
          size="mini"
          @blur="handleSaveCell(row, column, index)"
          @keyup.enter.native="handleSaveCell(row, column, index)"
          @keyup.esc.native="handleCancelEdit"
          ref="editInput"
        />
      </div>
      <div v-else @dblclick="handleEditCell(row, column, index)">
        {{ row[column.prop] }}
        <i class="el-icon-edit" style="margin-left: 4px; color: #999;"></i>
      </div>
    </template>

    <!-- 星级评分 -->
    <template #rating="{ row, index }">
      <el-rate
        v-model="row.rating"
        :max="5"
        :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
        @change="handleRatingChange(row, index)"
      />
    </template>

    <!-- 数量调节器 -->
    <template #quantity="{ row, index }">
      <el-input-number
        v-model="row.quantity"
        :min="0"
        :max="999"
        size="mini"
        @change="handleQuantityChange(row, index)"
      />
    </template>
  </CommonTable>
</template>

<script>
export default {
  data() {
    return {
      editingCell: null,
      editValue: ''
    }
  },
  methods: {
    handleEditCell(row, column, index) {
      this.editingCell = `${index}-${column.prop}`
      this.editValue = row[column.prop]
      this.$nextTick(() => {
        this.$refs.editInput.focus()
      })
    },
    
    handleSaveCell(row, column, index) {
      row[column.prop] = this.editValue
      this.editingCell = null
      this.editValue = ''
      // 可以在这里调用保存接口
      this.$emit('cell-update', { row, column, index, value: this.editValue })
    },
    
    handleCancelEdit() {
      this.editingCell = null
      this.editValue = ''
    }
  }
}
</script>
```

#### 5. 富文本和媒体插槽

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 富文本内容 -->
    <template #content="{ row }">
      <div 
        v-html="row.content" 
        style="max-height: 100px; overflow-y: auto;"
        class="rich-text-content"
      />
    </template>

    <!-- 图片预览 -->
    <template #images="{ row }">
      <div style="display: flex; gap: 4px;">
        <el-image
          v-for="(img, idx) in row.images.slice(0, 3)"
          :key="idx"
          :src="img"
          :preview-src-list="row.images"
          style="width: 30px; height: 30px;"
          fit="cover"
        />
        <span v-if="row.images.length > 3" style="font-size: 12px; color: #999;">
          +{{ row.images.length - 3 }}
        </span>
      </div>
    </template>

    <!-- 视频播放 -->
    <template #video="{ row }">
      <video
        v-if="row.videoUrl"
        :src="row.videoUrl"
        style="width: 80px; height: 60px;"
        controls
        preload="metadata"
      />
      <span v-else style="color: #999;">无视频</span>
    </template>
  </CommonTable>
</template>
```

### 插槽与render函数对比

| 特性 | 插槽 (Slot) | render函数 |
|------|-------------|------------|
| **易用性** | ⭐⭐⭐⭐⭐ 模板语法，直观易懂 | ⭐⭐⭐ 需要了解JS和Vue的render函数 |
| **可读性** | ⭐⭐⭐⭐⭐ HTML模板，结构清晰 | ⭐⭐ JS代码，较为抽象 |
| **维护性** | ⭐⭐⭐⭐⭐ 易于修改和维护 | ⭐⭐⭐ 修改需要修改JS逻辑 |
| **功能性** | ⭐⭐⭐⭐ 支持大部分场景 | ⭐⭐⭐⭐⭐ 编程式，功能最强 |
| **性能** | ⭐⭐⭐⭐ 编译优化 | ⭐⭐⭐⭐ 运行时生成 |
| **学习成本** | ⭐⭐ 前端开发者都熟悉 | ⭐⭐⭐⭐ 需要学习render函数 |

**建议使用场景：**

- **优先使用插槽**：适合90%的业务场景，特别是静态内容、简单交互
- **使用render函数**：动态组件生成、复杂逻辑运算、需要编程式控制的场景

```vue
<!-- 推荐：使用插槽 -->
<template #status="{ row }">
  <el-tag :type="row.status === 1 ? 'success' : 'danger'">
    {{ row.status === 1 ? '正常' : '禁用' }}
  </el-tag>
</template>

<!-- 备选：使用render函数 -->
<script>
columns: [
  {
    prop: 'status',
    label: '状态',
    render: (h, { row }) => {
      return h('el-tag', {
        props: { type: row.status === 1 ? 'success' : 'danger' }
      }, row.status === 1 ? '正常' : '禁用')
    }
  }
]
</script>
```

---

## API 文档

### Props

#### 基础属性

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| data | 表格数据 | `Array` | — | `[]` |
| columns | 列配置 | `Array<ColumnConfig>` | — | `[]` |
| loading | 加载状态 | `Boolean` | — | `false` |

#### 功能控制

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| selection | 是否显示多选列 | `Boolean` | — | `false` |
| selectionWidth | 多选列宽度 | `String\|Number` | — | `55` |
| selectionFixed | 多选列是否固定 | `String\|Boolean` | `true/left/right` | `false` |
| reserveSelection | 数据更新时保留选项 | `Boolean` | — | `false` |
| selectable | 行的 CheckBox 是否可以勾选 | `Function` | — | `null` |
| index | 是否显示序号列 | `Boolean` | — | `false` |
| indexLabel | 序号列标题 | `String` | — | `'序号'` |
| indexWidth | 序号列宽度 | `String\|Number` | — | `50` |
| indexFixed | 序号列是否固定 | `String\|Boolean` | `true/left/right` | `false` |
| indexMethod | 自定义序号 | `Function` | — | `null` |
| expand | 是否显示展开列 | `Boolean` | — | `false` |
| expandWidth | 展开列宽度 | `String\|Number` | — | `50` |
| expandFixed | 展开列是否固定 | `String\|Boolean` | `true/left/right` | `false` |

#### 分页配置

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| pagination | 分页配置 | `Object\|Boolean` | — | `false` |

#### 工具栏配置

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| showToolbar | 是否显示工具栏 | `Boolean` | — | `true` |
| showRefresh | 是否显示刷新按钮 | `Boolean` | — | `true` |
| showColumnSetting | 是否显示列设置 | `Boolean` | — | `true` |

#### 外观样式

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| height | 表格高度 | `String\|Number` | — | `null` |
| maxHeight | 表格最大高度 | `String\|Number` | — | `null` |
| stripe | 是否为斑马纹表格 | `Boolean` | — | `true` |
| border | 是否带有纵向边框 | `Boolean` | — | `true` |
| size | 表格尺寸 | `String` | `medium/small/mini` | `'medium'` |
| fit | 列的宽度是否自撑开 | `Boolean` | — | `true` |
| showHeader | 是否显示表头 | `Boolean` | — | `true` |
| highlightCurrentRow | 是否要高亮当前行 | `Boolean` | — | `false` |

#### 其他配置

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| rowKey | 行数据的 Key | `String\|Function` | — | `null` |
| emptyText | 空数据时显示的文本内容 | `String` | — | `'暂无数据'` |
| defaultExpandAll | 是否默认展开所有行 | `Boolean` | — | `false` |
| expandRowKeys | 可以通过该属性设置 Table 目前的展开行 | `Array` | — | `[]` |
| defaultSort | 默认的排序列的 prop 和顺序 | `Object` | — | `{}` |
| tooltipEffect | tooltip effect 属性 | `String` | `dark/light` | `'dark'` |
| showSummary | 是否在表尾显示合计行 | `Boolean` | — | `false` |
| sumText | 合计行第一列的文本 | `String` | — | `'合计'` |
| summaryMethod | 自定义的合计计算方法 | `Function` | — | `null` |
| spanMethod | 合并行或列的计算方法 | `Function` | — | `null` |

### Column 配置

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|-------|
| prop | 对应列内容的字段名 | `String` | — | — |
| label | 显示的标题 | `String` | — | — |
| width | 对应列的宽度 | `String\|Number` | — | — |
| minWidth | 对应列的最小宽度 | `String\|Number` | — | — |
| fixed | 列是否固定 | `String\|Boolean` | `true/left/right` | — |
| sortable | 对应列是否可以排序 | `Boolean\|String` | `true/false/custom` | `false` |
| render | 自定义渲染函数 | `Function` | — | — |
| slot | 自定义插槽名称 | `String` | — | — |
| align | 对齐方式 | `String` | `left/center/right` | `'left'` |
| headerAlign | 表头对齐方式 | `String` | `left/center/right` | 与align相同 |
| showOverflowTooltip | 当内容过长被隐藏时显示 tooltip | `Boolean` | — | `true` |
| formatter | 用来格式化内容 | `Function` | — | — |
| filters | 数据过滤的选项 | `Array` | — | — |
| filterMethod | 数据过滤使用的方法 | `Function` | — | — |
| className | 列的 className | `String` | — | — |
| labelClassName | 当前列标题的自定义类名 | `String` | — | — |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| refresh | 点击刷新按钮时触发 | — |
| selection-change | 当选择项发生变化时会触发该事件 | `selection` |
| size-change | pageSize 改变时会触发 | `pageSize: number` |
| current-change | currentPage 改变时会触发 | `currentPage: number` |
| column-change | 列设置改变时触发 | `visibleColumns: Array` |
| select | 当用户手动勾选数据行的 Checkbox 时触发的事件 | `selection, row` |
| select-all | 当用户手动勾选全选 Checkbox 时触发的事件 | `selection` |
| cell-click | 当某个单元格被点击时会触发该事件 | `row, column, cell, event` |
| cell-dblclick | 当某个单元格被双击击时会触发该事件 | `row, column, cell, event` |
| row-click | 当某一行被点击时会触发该事件 | `row, column, event` |
| row-dblclick | 当某一行被双击时会触发该事件 | `row, column, event` |
| header-click | 当某一列的表头被点击时会触发该事件 | `column, event` |
| sort-change | 当表格的排序条件发生变化的时候会触发该事件 | `{ column, prop, order }` |
| filter-change | 当表格的筛选条件发生变化的时候会触发该事件 | `filters` |
| expand-change | 当用户对某一行展开或者关闭的时候会触发该事件 | `row, expandedRows` |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| clearSelection | 用于多选表格，清空用户的选择 | — |
| toggleRowSelection | 用于多选表格，切换某一行的选中状态 | `row, selected` |
| toggleAllSelection | 用于多选表格，切换所有行的选中状态 | — |
| setCurrentRow | 用于单选表格，设定某一行为选中行 | `row` |
| clearSort | 用于清空排序条件，数据会恢复成未排序的状态 | — |
| clearFilter | 用于清空过滤条件，数据会恢复成未过滤的状态 | `columnKey?: string` |
| doLayout | 对 Table 进行重新布局 | — |
| sort | 手动对 Table 进行排序 | `prop: string, order: string` |
| getTableRef | 获取表格实例 | — |

### Slots

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| toolbar-left | 工具栏左侧内容 | — |
| toolbar-right | 工具栏右侧内容 | — |
| expand | 展开行的内容 | `{ row, index }` |
| [columnSlot] | 自定义列内容插槽，插槽名为列配置中的 slot 属性值 | `{ row, column, index }` |

### 分页配置对象 (PaginationConfig)

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|-------|
| currentPage | 当前页数 | `Number` | `1` |
| pageSize | 每页显示条目个数 | `Number` | `10` |
| total | 总条目数 | `Number` | `0` |
| pageSizes | 每页显示个数选择器的选项设置 | `Array` | `[10, 20, 50, 100]` |
| layout | 组件布局，子组件名用逗号分隔 | `String` | `'total, sizes, prev, pager, next, jumper'` |
| small | 是否使用小型分页样式 | `Boolean` | `false` |
| background | 是否为分页按钮添加背景色 | `Boolean` | `true` |
| pagerCount | 页码按钮的数量，当总页数超过该值时会折叠 | `Number` | `7` |
| hideOnSinglePage | 只有一页时是否隐藏 | `Boolean` | `false` |

---

## 最佳实践

### 1. 性能优化

#### 虚拟滚动

对于大量数据，建议使用虚拟滚动：

```vue
<template>
  <CommonTable
    :columns="columns"
    :data="tableData"
    :height="400"
    virtual-scroll
    :virtual-row-height="50"
    :virtual-buffer="10"
  />
</template>
```

#### 列固定优化

固定列会影响性能，建议按需使用：

```javascript
// 好的做法：只固定必要的列
columns: [
  { prop: 'name', label: '姓名', fixed: 'left', width: 120 },
  { prop: 'actions', label: '操作', fixed: 'right', width: 200 },
  // 其他列不固定
]

// 避免：过多固定列
columns: [
  { prop: 'col1', fixed: 'left' },
  { prop: 'col2', fixed: 'left' },
  { prop: 'col3', fixed: 'left' }, // 过多固定列
  // ...
]
```

### 2. 响应式设计

#### 列宽自适应

```javascript
columns: [
  { prop: 'name', label: '姓名', minWidth: 100 }, // 使用 minWidth 替代固定 width
  { prop: 'description', label: '描述', minWidth: 200 },
  { prop: 'actions', label: '操作', width: 150 } // 操作列可固定宽度
]
```
## 高级功能

### 插槽参数说明

#### 列插槽参数

```typescript
interface SlotParams {
  row: Object      // 当前行数据
  column: Object   // 当前列配置
  index: Number    // 当前行索引
}
```

#### 展开行插槽参数

```typescript
interface ExpandSlotParams {
  row: Object     // 当前行数据
  index: Number   // 当前行索引
}
```

### 更多插槽应用场景

#### 1. 自定义表头插槽

虽然表格组件本身支持自定义表头，但也可以通过插槽实现更复杂的表头：

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 自定义排序列 -->
    <template #sortableHeader="{ column }">
      <span style="display: flex; align-items: center;">
        <i class="el-icon-sort" style="margin-right: 4px;"></i>
        {{ column.label }}
        <el-tooltip content="点击排序" placement="top">
          <i class="el-icon-question" style="margin-left: 4px; color: #999;"></i>
        </el-tooltip>
      </span>
    </template>
  </CommonTable>
</template>
```

#### 2. 条件渲染插槽

根据不同条件渲染不同内容：

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 权限相关操作 -->
    <template #actions="{ row, index }">
      <!-- 管理员权限 -->
      <template v-if="userRole === 'admin'">
        <el-button size="mini" @click="handleEdit(row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="handleDelete(row, index)">删除</el-button>
        <el-button size="mini" type="warning" @click="handleResetPassword(row)">重置密码</el-button>
      </template>
      
      <!-- 普通用户权限 -->
      <template v-else-if="userRole === 'user'">
        <el-button size="mini" @click="handleView(row)">查看</el-button>
        <el-button 
          v-if="row.userId === currentUserId" 
          size="mini" 
          type="primary" 
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
      </template>
      
      <!-- 游客权限 -->
      <template v-else>
        <el-button size="mini" @click="handleView(row)">查看</el-button>
      </template>
    </template>

    <!-- 状态显示 -->
    <template #status="{ row }">
      <el-tag v-if="row.status === 'active'" type="success">
        <i class="el-icon-check"></i> 正常
      </el-tag>
      <el-tag v-else-if="row.status === 'pending'" type="warning">
        <i class="el-icon-time"></i> 待审核
      </el-tag>
      <el-tag v-else-if="row.status === 'blocked'" type="danger">
        <i class="el-icon-close"></i> 已封禁
      </el-tag>
      <el-tag v-else type="info">
        <i class="el-icon-question"></i> 未知
      </el-tag>
    </template>
  </CommonTable>
</template>
```

#### 3. 嵌套组件插槽

在插槽中使用复杂的组件：

```vue
<template>
  <CommonTable :columns="columns" :data="tableData">
    <!-- 内嵌图表 -->
    <template #chart="{ row }">
      <div style="width: 100px; height: 50px;">
        <mini-chart :data="row.chartData" :options="chartOptions" />
      </div>
    </template>

    <!-- 内嵌表单 -->
    <template #editInline="{ row, index }">
      <el-form :model="row" :inline="true" size="mini">
        <el-form-item>
          <el-input 
            v-model="row.name" 
            @blur="handleSave(row, index)"
            @keyup.enter.native="handleSave(row, index)"
          />
        </el-form-item>
      </el-form>
    </template>

    <!-- 文件上传 -->
    <template #upload="{ row, index }">
      <el-upload
        :action="uploadUrl"
        :show-file-list="false"
        :before-upload="file => beforeUpload(file, row)"
        :on-success="(response) => onUploadSuccess(response, row, index)"
      >
        <el-button size="mini" icon="el-icon-upload">上传</el-button>
      </el-upload>
      <div v-if="row.fileName" style="margin-top: 4px; font-size: 12px;">
        {{ row.fileName }}
      </div>
    </template>
  </CommonTable>
</template>
```
## 源码实现

<details>
  <summary>📄 查看完整源码</summary>


```vue
<template>
  <div class="common-table">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <el-button
            v-if="showRefresh"
            type="primary"
            size="small"
            icon="el-icon-refresh"
            @click="handleRefresh"
          >
            刷新
          </el-button>
        </slot>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <el-button
            v-if="showColumnSetting"
            type="text"
            size="small"
            icon="el-icon-setting"
            @click="showColumnDialog = true"
          >
            列设置
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="table"
      v-loading="loading"
      :data="data"
      :height="height"
      :max-height="maxHeight"
      :stripe="stripe"
      :border="border"
      :size="size"
      :fit="fit"
      :show-header="showHeader"
      :highlight-current-row="highlightCurrentRow"
      :row-class-name="rowClassName"
      :row-style="rowStyle"
      :cell-class-name="cellClassName"
      :cell-style="cellStyle"
      :header-row-class-name="headerRowClassName"
      :header-row-style="headerRowStyle"
      :header-cell-class-name="headerCellClassName"
      :header-cell-style="headerCellStyle"
      :row-key="rowKey"
      :empty-text="emptyText"
      :default-expand-all="defaultExpandAll"
      :expand-row-keys="expandRowKeys"
      :default-sort="defaultSort"
      :tooltip-effect="tooltipEffect"
      :show-summary="showSummary"
      :sum-text="sumText"
      :summary-method="summaryMethod"
      :span-method="spanMethod"
      :select-on-indeterminate="selectOnIndeterminate"
      :indent="indent"
      :lazy="lazy"
      :load="load"
      :tree-props="treeProps"
      v-on="$listeners"
    >
      <!-- 多选列 -->
      <el-table-column
        v-if="selection"
        type="selection"
        :width="selectionWidth"
        :fixed="selectionFixed"
        :reserve-selection="reserveSelection"
        :selectable="selectable"
      />

      <!-- 序号列 -->
      <el-table-column
        v-if="index"
        type="index"
        :label="indexLabel"
        :width="indexWidth"
        :fixed="indexFixed"
        :index="indexMethod"
      />

      <!-- 展开列 -->
      <el-table-column
        v-if="expand"
        type="expand"
        :width="expandWidth"
        :fixed="expandFixed"
      >
        <template slot-scope="props">
          <slot name="expand" :row="props.row" :index="props.$index" />
        </template>
      </el-table-column>

      <!-- 数据列 -->
      <template v-for="(column, idx) in visibleColumns">
        <el-table-column
          :key="idx"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :render-header="column.renderHeader"
          :sortable="column.sortable"
          :sort-method="column.sortMethod"
          :sort-by="column.sortBy"
          :sort-orders="column.sortOrders"
          :resizable="column.resizable"
          :formatter="column.formatter"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
          :align="column.align || 'left'"
          :header-align="column.headerAlign || column.align || 'left'"
          :class-name="column.className"
          :label-class-name="column.labelClassName"
          :selectable="column.selectable"
          :reserve-selection="column.reserveSelection"
          :filters="column.filters"
          :filter-placement="column.filterPlacement"
          :filter-multiple="column.filterMultiple"
          :filter-method="column.filterMethod"
          :filtered-value="column.filteredValue"
        >
          <template slot-scope="scope">
            <!-- 自定义渲染函数 -->
            <span v-if="column.render">
              <render-component
                :render="column.render"
                :row="scope.row"
                :column="column"
                :index="scope.$index"
              />
            </span>
            <!-- 插槽渲染 -->
            <slot
              v-else-if="column.slot"
              :name="column.slot"
              :row="scope.row"
              :column="column"
              :index="scope.$index"
            />
            <!-- 默认渲染 -->
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <div v-if="pagination" class="table-pagination">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-sizes="pagination.pageSizes || [10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :layout="paginationLayout"
        :total="pagination.total"
        :small="pagination.small"
        :background="pagination.background !== false"
        :pager-count="pagination.pagerCount || 7"
        :hide-on-single-page="pagination.hideOnSinglePage"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
        @prev-click="$emit('prev-click', $event)"
        @next-click="$emit('next-click', $event)"
      />
    </div>

    <!-- 列设置弹窗 -->
    <el-dialog
      title="列设置"
      :visible.sync="showColumnDialog"
      width="400px"
      append-to-body
    >
      <div class="column-setting">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
        <el-divider />
        <el-checkbox-group v-model="checkedColumns" @change="handleCheckedColumnsChange">
          <draggable v-model="columnSettings" class="column-list">
            <div
              v-for="column in columnSettings"
              :key="column.prop"
              class="column-item"
            >
              <i class="el-icon-rank drag-handle" />
              <el-checkbox :label="column.prop">{{ column.label }}</el-checkbox>
            </div>
          </draggable>
        </el-checkbox-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showColumnDialog = false">取消</el-button>
        <el-button type="primary" @click="handleColumnSettingConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable'

// 渲染函数组件
const RenderComponent = {
  functional: true,
  props: {
    render: Function,
    row: Object,
    column: Object,
    index: Number
  },
  render(h, ctx) {
    const params = {
      row: ctx.props.row,
      column: ctx.props.column,
      $index: ctx.props.index
    }
    return ctx.props.render(h, params)
  }
}

export default {
  name: 'CommonTable',
  components: {
    draggable,
    RenderComponent
  },
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 列配置
    columns: {
      type: Array,
      default: () => []
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 分页配置
    pagination: {
      type: [Object, Boolean],
      default: false
    },
    // 是否显示多选列
    selection: {
      type: Boolean,
      default: false
    },
    // 多选列宽度
    selectionWidth: {
      type: [String, Number],
      default: 55
    },
    // 多选列是否固定
    selectionFixed: {
      type: [String, Boolean],
      default: false
    },
    // 数据更新时保留选项
    reserveSelection: {
      type: Boolean,
      default: false
    },
    // 仅对 type=selection 的列有效，类型为 Function，Function 的返回值用来决定这一行的 CheckBox 是否可以勾选
    selectable: {
      type: Function,
      default: null
    },
    // 是否显示序号列
    index: {
      type: Boolean,
      default: false
    },
    // 序号列标题
    indexLabel: {
      type: String,
      default: '序号'
    },
    // 序号列宽度
    indexWidth: {
      type: [String, Number],
      default: 50
    },
    // 序号列是否固定
    indexFixed: {
      type: [String, Boolean],
      default: false
    },
    // 自定义序号
    indexMethod: {
      type: Function,
      default: null
    },
    // 是否显示展开列
    expand: {
      type: Boolean,
      default: false
    },
    // 展开列宽度
    expandWidth: {
      type: [String, Number],
      default: 50
    },
    // 展开列是否固定
    expandFixed: {
      type: [String, Boolean],
      default: false
    },
    // 是否显示工具栏
    showToolbar: {
      type: Boolean,
      default: true
    },
    // 是否显示刷新按钮
    showRefresh: {
      type: Boolean,
      default: true
    },
    // 是否显示列设置
    showColumnSetting: {
      type: Boolean,
      default: true
    },
    // Table 的高度
    height: {
      type: [String, Number],
      default: null
    },
    // Table 的最大高度
    maxHeight: {
      type: [String, Number],
      default: null
    },
    // 是否为斑马纹 table
    stripe: {
      type: Boolean,
      default: true
    },
    // 是否带有纵向边框
    border: {
      type: Boolean,
      default: true
    },
    // Table 的尺寸
    size: {
      type: String,
      default: 'medium',
      validator: val => ['medium', 'small', 'mini'].includes(val)
    },
    // 列的宽度是否自撑开
    fit: {
      type: Boolean,
      default: true
    },
    // 是否显示表头
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否要高亮当前行
    highlightCurrentRow: {
      type: Boolean,
      default: false
    },
    // 行的 className 的回调方法
    rowClassName: {
      type: [String, Function],
      default: null
    },
    // 行的 style 的回调方法
    rowStyle: {
      type: [Object, Function],
      default: null
    },
    // 单元格的 className 的回调方法
    cellClassName: {
      type: [String, Function],
      default: null
    },
    // 单元格的 style 的回调方法
    cellStyle: {
      type: [Object, Function],
      default: null
    },
    // 表头行的 className 的回调方法
    headerRowClassName: {
      type: [String, Function],
      default: null
    },
    // 表头行的 style 的回调方法
    headerRowStyle: {
      type: [Object, Function],
      default: null
    },
    // 表头单元格的 className 的回调方法
    headerCellClassName: {
      type: [String, Function],
      default: null
    },
    // 表头单元格的 style 的回调方法
    headerCellStyle: {
      type: [Object, Function],
      default: null
    },
    // 行数据的 Key
    rowKey: {
      type: [String, Function],
      default: null
    },
    // 空数据时显示的文本内容
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    // 是否默认展开所有行
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    // 可以通过该属性设置 Table 目前的展开行
    expandRowKeys: {
      type: Array,
      default: () => []
    },
    // 默认的排序列的 prop 和顺序
    defaultSort: {
      type: Object,
      default: () => ({})
    },
    // tooltip effect 属性
    tooltipEffect: {
      type: String,
      default: 'dark',
      validator: val => ['dark', 'light'].includes(val)
    },
    // 是否在表尾显示合计行
    showSummary: {
      type: Boolean,
      default: false
    },
    // 合计行第一列的文本
    sumText: {
      type: String,
      default: '合计'
    },
    // 自定义的合计计算方法
    summaryMethod: {
      type: Function,
      default: null
    },
    // 合并行或列的计算方法
    spanMethod: {
      type: Function,
      default: null
    },
    // 在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为
    selectOnIndeterminate: {
      type: Boolean,
      default: true
    },
    // 展示树形数据时，树节点的缩进
    indent: {
      type: Number,
      default: 16
    },
    // 是否懒加载子节点数据
    lazy: {
      type: Boolean,
      default: false
    },
    // 加载子节点数据的函数
    load: {
      type: Function,
      default: null
    },
    // 渲染嵌套数据的配置选项
    treeProps: {
      type: Object,
      default: () => ({
        hasChildren: 'hasChildren',
        children: 'children'
      })
    }
  },
  data() {
    return {
      showColumnDialog: false,
      columnSettings: [],
      checkedColumns: [],
      checkAll: true,
      isIndeterminate: false
    }
  },
  computed: {
    // 可见的列
    visibleColumns() {
      return this.columns.filter(column => {
        return !this.columnSettings.length || this.checkedColumns.includes(column.prop)
      })
    },
    // 分页布局
    paginationLayout() {
      if (this.pagination && this.pagination.layout) {
        return this.pagination.layout
      }
      return 'total, sizes, prev, pager, next, jumper'
    }
  },
  watch: {
    columns: {
      handler(newVal) {
        this.initColumnSettings()
      },
      immediate: true
    }
  },
  methods: {
    // 初始化列设置
    initColumnSettings() {
      this.columnSettings = this.columns
        .filter(column => column.prop)
        .map(column => ({
          prop: column.prop,
          label: column.label,
          visible: true
        }))
      this.checkedColumns = this.columnSettings.map(item => item.prop)
    },
    
    // 全选/反选
    handleCheckAllChange(val) {
      this.checkedColumns = val ? this.columnSettings.map(item => item.prop) : []
      this.isIndeterminate = false
    },
    
    // 选中项改变
    handleCheckedColumnsChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.columnSettings.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.columnSettings.length
    },
    
    // 确认列设置
    handleColumnSettingConfirm() {
      this.showColumnDialog = false
      this.$emit('column-change', this.visibleColumns)
    },
    
    // 刷新数据
    handleRefresh() {
      this.$emit('refresh')
    },
    
    // 获取表格实例
    getTableRef() {
      return this.$refs.table
    },
    
    // 清空选择
    clearSelection() {
      this.$refs.table.clearSelection()
    },
    
    // 切换某一行的选中状态
    toggleRowSelection(row, selected) {
      this.$refs.table.toggleRowSelection(row, selected)
    },
    
    // 切换所有行的选中状态
    toggleAllSelection() {
      this.$refs.table.toggleAllSelection()
    },
    
    // 设定某一行为选中行
    setCurrentRow(row) {
      this.$refs.table.setCurrentRow(row)
    },
    
    // 清空排序条件
    clearSort() {
      this.$refs.table.clearSort()
    },
    
    // 清空过滤条件
    clearFilter(columnKey) {
      this.$refs.table.clearFilter(columnKey)
    },
    
    // 对 Table 进行重新布局
    doLayout() {
      this.$refs.table.doLayout()
    },
    
    // 手动对 Table 进行排序
    sort(prop, order) {
      this.$refs.table.sort(prop, order)
    }
  }
}
</script>

<style lang="scss" scoped>
.common-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 0;
    
    .toolbar-left,
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .table-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    padding: 16px 0;
  }
  
  .column-setting {
    .column-list {
      max-height: 300px;
      overflow-y: auto;
      
      .column-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        cursor: move;
        
        .drag-handle {
          margin-right: 8px;
          color: #c0c4cc;
          cursor: move;
        }
      }
    }
  }
}
</style>
```

</details>