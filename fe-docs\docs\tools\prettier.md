# Prettier 配置指南

Prettier 是一个代码格式化工具，支持多种语言，能够自动格式化代码以保持一致的代码风格。本文档提供了完整的 Prettier 配置指南。

## 安装和基础配置

### 1. 安装 Prettier

```bash
# 项目本地安装
npm install --save-dev prettier

# 全局安装
npm install -g prettier

# 格式化文件
npx prettier --write src/
```

### 2. 与 ESLint 集成

```bash
# 安装 ESLint 和 Prettier 集成插件
npm install --save-dev \
  eslint-config-prettier \
  eslint-plugin-prettier
```

## 配置文件

### .prettierrc

项目根目录创建 `.prettierrc` 文件：

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 80,
  "trailingComma": "none",
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "quoteProps": "as-needed",
  "jsxSingleQuote": true,
  "proseWrap": "preserve",
  "htmlWhitespaceSensitivity": "css",
  "vueIndentScriptAndStyle": false,
  "embeddedLanguageFormatting": "auto"
}
```

### .prettierrc.js

使用 JavaScript 配置文件：

```javascript
module.exports = {
  // 基础配置
  semi: false,                    // 不使用分号
  singleQuote: true,             // 使用单引号
  tabWidth: 2,                   // 缩进宽度
  useTabs: false,                // 使用空格缩进
  printWidth: 80,                // 每行最大字符数
  
  // 逗号配置
  trailingComma: 'none',         // 不使用尾随逗号
  
  // 括号配置
  bracketSpacing: true,          // 对象括号内空格
  bracketSameLine: false,        // 多行元素的>单独一行
  
  // 箭头函数参数括号
  arrowParens: 'avoid',          // 单参数时省略括号
  
  // 换行符
  endOfLine: 'lf',               // 使用 LF 换行符
  
  // 对象属性引号
  quoteProps: 'as-needed',       // 仅在需要时添加引号
  
  // JSX 配置
  jsxSingleQuote: true,          // JSX 中使用单引号
  
  // 文档格式化
  proseWrap: 'preserve',         // 保持原有换行
  
  // HTML 空白敏感度
  htmlWhitespaceSensitivity: 'css',
  
  // Vue 文件配置
  vueIndentScriptAndStyle: false, // 不缩进 Vue 文件的 script 和 style 标签
  
  // 嵌入式语言格式化
  embeddedLanguageFormatting: 'auto',
  
  // 文件类型特定配置
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        proseWrap: 'always',
        printWidth: 100
      }
    },
    {
      files: '*.vue',
      options: {
        parser: 'vue'
      }
    }
  ]
}
```

### package.json 配置

也可以在 `package.json` 中配置：

```json
{
  "prettier": {
    "semi": false,
    "singleQuote": true,
    "tabWidth": 2,
    "trailingComma": "none"
  }
}
```

## 忽略文件配置

### .prettierignore

创建 `.prettierignore` 文件忽略不需要格式化的文件：

```bash
# 构建输出
dist/
build/
coverage/

# 依赖
node_modules/

# 配置文件
*.config.js
.eslintrc.js

# 静态资源
public/
static/

# 模板文件
templates/

# 第三方库
src/assets/js/vendor/
src/libs/

# 特定文件类型
*.min.js
*.min.css

# 日志文件
*.log

# 临时文件
*.tmp
*.temp

# Markdown 文件（如果不想格式化）
# *.md

# 特定目录
docs/api/
```

## 配置选项详解

### 1. 基础格式化选项

```javascript
{
  // 每行最大字符数
  printWidth: 80,
  
  // 缩进配置
  tabWidth: 2,        // 缩进宽度
  useTabs: false,     // 使用空格而非制表符
  
  // 分号配置
  semi: false,        // 不使用分号
  
  // 引号配置
  singleQuote: true,  // 使用单引号
  quoteProps: 'as-needed', // 对象属性引号策略
  
  // JSX 引号
  jsxSingleQuote: true
}
```

### 2. 逗号和括号配置

```javascript
{
  // 尾随逗号
  trailingComma: 'none', // 'none' | 'es5' | 'all'
  
  // 对象括号空格
  bracketSpacing: true,  // { foo: bar } vs {foo: bar}
  
  // JSX 括号位置
  bracketSameLine: false, // 多行JSX元素的>是否与最后一行属性同行
  
  // 箭头函数参数括号
  arrowParens: 'avoid'   // 'avoid' | 'always'
}
```

### 3. 换行符配置

```javascript
{
  // 换行符类型
  endOfLine: 'lf',  // 'lf' | 'crlf' | 'cr' | 'auto'
  
  // 文档换行
  proseWrap: 'preserve' // 'always' | 'never' | 'preserve'
}
```

### 4. 语言特定配置

```javascript
{
  // HTML 空白敏感度
  htmlWhitespaceSensitivity: 'css', // 'css' | 'strict' | 'ignore'
  
  // Vue 文件配置
  vueIndentScriptAndStyle: false,
  
  // 嵌入式语言格式化
  embeddedLanguageFormatting: 'auto' // 'auto' | 'off'
}
```

## 与编辑器集成

### 1. VS Code 集成

安装 Prettier 扩展：`ext install esbenp.prettier-vscode`

在 `.vscode/settings.json` 中配置：

```json
{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  
  // 文件类型特定配置
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
```

### 2. WebStorm 集成

1. 打开 Settings → Languages & Frameworks → JavaScript → Prettier
2. 勾选 "On code reformat" 和 "On save"
3. 设置 Prettier package 路径

## 与 ESLint 集成

### 1. 配置 ESLint

在 `.eslintrc.js` 中添加：

```javascript
module.exports = {
  extends: [
    'plugin:vue/essential',
    '@vue/standard',
    'plugin:prettier/recommended' // 必须放在最后
  ],
  rules: {
    // 关闭与 Prettier 冲突的规则
    'prettier/prettier': 'error'
  }
}
```

### 2. 解决冲突

使用 `eslint-config-prettier` 自动关闭冲突规则：

```bash
# 检查冲突
npx eslint-config-prettier src/main.js

# 如果有冲突，会显示具体的规则
```

## 脚本配置

### package.json 脚本

```json
{
  "scripts": {
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "format:js": "prettier --write \"src/**/*.{js,vue}\"",
    "format:css": "prettier --write \"src/**/*.{css,scss}\"",
    "format:json": "prettier --write \"*.json\"",
    "lint:format": "eslint --fix . && prettier --write ."
  }
}
```

### Git Hooks 集成

使用 husky 和 lint-staged：

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{js,vue,css,scss,json,md}": [
      "prettier --write",
      "git add"
    ],
    "*.{js,vue}": [
      "eslint --fix",
      "git add"
    ]
  }
}
```

## 高级配置

### 1. 条件格式化

```javascript
// prettier.config.js
const isProduction = process.env.NODE_ENV === 'production'

module.exports = {
  semi: false,
  singleQuote: true,
  // 生产环境使用更严格的配置
  printWidth: isProduction ? 120 : 80,
  trailingComma: isProduction ? 'all' : 'none'
}
```

### 2. 多项目配置

```javascript
// prettier.config.js
module.exports = {
  semi: false,
  singleQuote: true,
  overrides: [
    {
      files: 'packages/admin/**/*',
      options: {
        printWidth: 120,
        tabWidth: 4
      }
    },
    {
      files: 'packages/mobile/**/*',
      options: {
        printWidth: 100,
        semi: true
      }
    }
  ]
}
```

### 3. 插件扩展

```bash
# 安装插件
npm install --save-dev \
  prettier-plugin-organize-imports \
  prettier-plugin-packagejson
```

```javascript
// prettier.config.js
module.exports = {
  plugins: [
    'prettier-plugin-organize-imports',
    'prettier-plugin-packagejson'
  ],
  semi: false,
  singleQuote: true
}
```

## 常见问题解决

### 1. 格式化冲突

```bash
# 检查 Prettier 和 ESLint 冲突
npx eslint-config-prettier src/main.js

# 手动解决冲突
{
  "rules": {
    "prettier/prettier": ["error", {
      "endOfLine": "auto"
    }]
  }
}
```

### 2. 性能优化

```javascript
// prettier.config.js
module.exports = {
  // 缓存配置
  cache: true,
  
  // 并行处理
  parallel: true,
  
  // 只格式化修改的文件
  onlyChanged: true
}
```

### 3. 特殊文件处理

```javascript
// 忽略特定代码块
// prettier-ignore
const uglyCode = {
  a:1,b:2,c:3
}

/* prettier-ignore */
const matrix = [
  1, 0, 0,
  0, 1, 0,
  0, 0, 1
]
```

## 最佳实践

### 1. 团队协作

- 统一配置文件
- 使用 Git Hooks 强制格式化
- 定期更新 Prettier 版本
- 培训团队成员

### 2. 配置策略

- 从宽松配置开始
- 逐步收紧规则
- 考虑项目特点
- 保持配置简洁

### 3. 工具链集成

- 与 ESLint 配合使用
- 集成到 CI/CD 流程
- 配置编辑器自动格式化
- 使用预提交钩子

通过合理配置 Prettier，可以自动保持代码格式的一致性，减少代码审查中关于格式的讨论，让团队专注于代码逻辑和功能实现。
