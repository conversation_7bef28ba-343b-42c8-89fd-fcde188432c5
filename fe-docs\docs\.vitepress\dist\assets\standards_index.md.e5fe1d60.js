import{_ as a,o as t,c as e,V as i}from"./chunks/framework.3d729ebc.js";const p=JSON.parse('{"title":"规范标准概述","description":"","frontmatter":{},"headers":[],"relativePath":"standards/index.md","filePath":"standards/index.md"}'),l={name:"standards/index.md"},r=i('<h1 id="规范标准概述" tabindex="-1">规范标准概述 <a class="header-anchor" href="#规范标准概述" aria-label="Permalink to &quot;规范标准概述&quot;">​</a></h1><p>规范标准是确保团队协作高效、代码质量一致的重要基础。我们制定了一系列规范，涵盖编码、Git提交、工作流程等方面，以帮助团队成员遵循统一的标准进行开发。</p><h2 id="规范体系" tabindex="-1">规范体系 <a class="header-anchor" href="#规范体系" aria-label="Permalink to &quot;规范体系&quot;">​</a></h2><p>我们的前端规范体系包括以下几个方面：</p><h3 id="编码规范" tabindex="-1">编码规范 <a class="header-anchor" href="#编码规范" aria-label="Permalink to &quot;编码规范&quot;">​</a></h3><ul><li><a href="/standards/js-standard.html">JavaScript规范</a> - JavaScript代码风格与最佳实践</li><li><a href="/standards/css-standard.html">CSS规范</a> - CSS/SCSS编写规范与命名约定</li><li><a href="/standards/html-standard.html">HTML规范</a> - HTML结构与语义化标准</li><li><a href="/standards/vue-standard.html">Vue开发规范</a> - Vue组件开发的特定规范</li></ul><h3 id="工作流规范" tabindex="-1">工作流规范 <a class="header-anchor" href="#工作流规范" aria-label="Permalink to &quot;工作流规范&quot;">​</a></h3><ul><li><a href="/standards/git-commit.html">Git提交规范</a> - 提交信息格式与分支管理规范</li><li><a href="/standards/git-workflow.html">Git工作流</a> - 分支策略与协作流程</li><li><a href="/standards/code-review.html">代码审查</a> - 代码审查流程与标准</li><li><a href="/standards/documentation.html">文档规范</a> - 技术文档编写规范</li></ul><h2 id="规范执行" tabindex="-1">规范执行 <a class="header-anchor" href="#规范执行" aria-label="Permalink to &quot;规范执行&quot;">​</a></h2><p>为确保规范的有效执行，我们采用以下措施：</p><ol><li><strong>自动化工具</strong> - 使用ESLint、Stylelint、Prettier等工具进行代码规范检查</li><li><strong>提交钩子</strong> - 使用husky和lint-staged在代码提交前进行规范检查</li><li><strong>CI/CD集成</strong> - 在持续集成流程中加入规范检查环节</li><li><strong>代码审查</strong> - 在代码审查过程中关注规范执行情况</li></ol><h2 id="规范更新" tabindex="-1">规范更新 <a class="header-anchor" href="#规范更新" aria-label="Permalink to &quot;规范更新&quot;">​</a></h2><p>我们的规范并非一成不变，会根据技术发展和项目需求进行定期更新：</p><ol><li>每季度进行规范评审</li><li>团队成员可提出规范优化建议</li><li>规范变更需经过团队讨论并达成共识</li><li>重大变更会提前通知并安排过渡期</li></ol><div class="tip custom-block"><p class="custom-block-title">提示</p><p>规范的目的是提高团队效率和代码质量，而非限制创新。在特殊情况下，可以合理地偏离规范，但需要在代码中添加相应说明。</p></div>',15),s=[r];function d(n,o,h,c,m,u){return t(),e("div",null,s)}const f=a(l,[["render",d]]);export{p as __pageData,f as default};
