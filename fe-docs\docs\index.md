---
layout: home
title: 前端技术开发文档
hero:
  name: 前端技术开发文档
  # tagline: 一站式前端开发资源中心
  image:
    src: /logo.jpeg
    alt: 前端技术文档
  actions:
    - theme: brand
      text: 快速开始
      link: /guide/
    - theme: alt
      text: 组件库
      link: /components/
    - theme: alt
      text: 规范标准
      link: /standards/

features:
  - icon: 📋
    title: 全面的开发指南
    details: 包含项目初始化、目录结构、开发流程等全面指南
  - icon: 🧩
    title: 丰富的组件库
    details: 提供基础组件、业务组件及自定义组件的详细说明与使用示例
  - icon: 🚀
    title: 最佳实践
    details: 前端性能优化、代码复用、状态管理等最佳实践指南
  - icon: 📐
    title: 规范标准
    details: 编码规范、Git工作流、代码审查、文档规范等标准化指南
  - icon: 🔧
    title: 工具配置
    details: VS Code配置、调试工具、包管理、代码质量工具等开发环境配置
  - icon: 🌏
    title: Cesium 3D地图引擎
    details: 强大的3D地球可视化引擎，支持地理空间数据展示与交互
---

<div class="main-container">
  <div class="main-content">

# 前端技术文档

欢迎使用公司前端技术文档！本文档旨在为前端开发团队提供统一的开发规范、技术指南和最佳实践，帮助团队提高开发效率，保持代码质量和一致性。

## 快速导航

- [开发指南](/guide/) - 快速入门、项目结构和开发流程
- [组件库](/components/) - 基础组件、业务组件和自定义组件
- [最佳实践](/best-practices/) - 性能优化、代码复用和状态管理
- [规范标准](/standards/) - 编码规范、Git工作流和代码审查
- [工具配置](/tools/) - VS Code配置、调试工具、代码质量工具
- [Cesium教程](/cesium/) - Cesium介绍、常用操作和实战案例

::: tip 提示
本文档持续更新中，如有问题或建议，请联系前端开发团队。
:::

</div>
</div> 