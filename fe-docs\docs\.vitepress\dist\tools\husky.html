<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title><PERSON>sky 配置指南 | 前端技术开发文档</title>
    <meta name="description" content="A VitePress site">
    <link rel="preload stylesheet" href="/assets/style.5ccb9172.css" as="style">
    <script type="module" src="/assets/app.473eac78.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.2ed14f66.woff2" as="font" type="font/woff2" crossorigin="">
  <link rel="modulepreload" href="/assets/chunks/framework.3d729ebc.js">
  <link rel="modulepreload" href="/assets/chunks/theme.533e1ce4.js">
  <link rel="modulepreload" href="/assets/tools_husky.md.586bda1d.lean.js">
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="apple-touch-icon" href="/logo.png">
  <meta name="theme-color" content="#0ea5e9">
  <script id="check-dark-light">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-21678b25 data-v-b2cf3e0b><!--[--><!--]--><!--[--><span tabindex="-1" data-v-c8616af1></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-c8616af1> Skip to content </a><!--]--><!----><header class="VPNav" data-v-b2cf3e0b data-v-7e5bc4a5><div class="VPNavBar has-sidebar" data-v-7e5bc4a5 data-v-94c81dcc><div class="container" data-v-94c81dcc><div class="title" data-v-94c81dcc><div class="VPNavBarTitle has-sidebar" data-v-94c81dcc data-v-f4ef19a3><a class="title" href="/" data-v-f4ef19a3><!--[--><!--]--><!--[--><img class="VPImage logo" src="/logo.jpeg" alt data-v-6db2186b><!--]--><!--[-->前端技术开发文档<!--]--><!--[--><!--]--></a></div></div><div class="content" data-v-94c81dcc><div class="curtain" data-v-94c81dcc></div><div class="content-body" data-v-94c81dcc><!--[--><!--]--><div class="VPNavBarSearch search" style="--vp-meta-key:&#39;Meta&#39;;" data-v-94c81dcc><!--[--><!----><div id="local-search"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search"><span class="DocSearch-Button-Container"><svg class="DocSearch-Search-Icon" width="20" height="20" viewBox="0 0 20 20" aria-label="search icon"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">搜索文档</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div><!--]--></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-94c81dcc data-v-7f418b0f><span id="main-nav-aria-label" class="visually-hidden" data-v-7f418b0f>Main Navigation</span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->首页<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/guide/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->指南<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/components/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->组件<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/best-practices/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->最佳实践<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/standards/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->规范标准<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/tools/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->工具配置<!--]--><!----></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/cesium/" tabindex="0" data-v-7f418b0f data-v-37adc828 data-v-8f4dc553><!--[-->Cesium<!--]--><!----></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-94c81dcc data-v-f6a63727><label title="toggle dark mode" data-v-f6a63727 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div><!----><div class="VPFlyout VPNavBarExtra extra" data-v-94c81dcc data-v-40855f84 data-v-764effdf><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-764effdf><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="icon" data-v-764effdf><circle cx="12" cy="12" r="2"></circle><circle cx="19" cy="12" r="2"></circle><circle cx="5" cy="12" r="2"></circle></svg></button><div class="menu" data-v-764effdf><div class="VPMenu" data-v-764effdf data-v-e7ea1737><!----><!--[--><!--[--><!----><div class="group" data-v-40855f84><div class="item appearance" data-v-40855f84><p class="label" data-v-40855f84>Appearance</p><div class="appearance-action" data-v-40855f84><label title="toggle dark mode" data-v-40855f84 data-v-a9c8afb8><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" aria-checked="false" data-v-a9c8afb8 data-v-f3c41672><span class="check" data-v-f3c41672><span class="icon" data-v-f3c41672><!--[--><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="sun" data-v-a9c8afb8><path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path></svg><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="moon" data-v-a9c8afb8><path d="M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"></path></svg><!--]--></span></span></button></label></div></div></div><!----><!--]--><!--]--></div></div></div><!--[--><!--[--><!--[--><div class="nav-color-picker" data-v-21678b25><div class="color-picker" data-v-21678b25 data-v-917787cc><button class="color-picker-trigger" title="打开色彩选择器" data-v-917787cc><div class="palette-icon" data-v-917787cc><div class="palette-circle" data-v-917787cc></div><div class="palette-colors" data-v-917787cc><div class="color-dot color-1" data-v-917787cc></div><div class="color-dot color-2" data-v-917787cc></div><div class="color-dot color-3" data-v-917787cc></div><div class="color-dot color-4" data-v-917787cc></div></div></div></button><!----><!----></div></div><!--]--><!--]--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-94c81dcc data-v-e5dd9c1c><span class="container" data-v-e5dd9c1c><span class="top" data-v-e5dd9c1c></span><span class="middle" data-v-e5dd9c1c></span><span class="bottom" data-v-e5dd9c1c></span></span></button></div></div></div></div><!----></header><div class="VPLocalNav" data-v-b2cf3e0b data-v-392e1bf8><button class="menu" aria-expanded="false" aria-controls="VPSidebarNav" data-v-392e1bf8><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="menu-icon" data-v-392e1bf8><path d="M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"></path><path d="M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"></path><path d="M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"></path><path d="M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"></path></svg><span class="menu-text" data-v-392e1bf8>Menu</span></button><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-392e1bf8 data-v-079b16a8><button data-v-079b16a8>Return to top</button><!----></div></div><aside class="VPSidebar" data-v-b2cf3e0b data-v-af16598e><div class="curtain" data-v-af16598e></div><nav class="nav" id="VPSidebarNav" aria-labelledby="sidebar-aria-label" tabindex="-1" data-v-af16598e><span class="visually-hidden" id="sidebar-aria-label" data-v-af16598e> Sidebar Navigation </span><!--[--><!--]--><!--[--><div class="group" data-v-af16598e><section class="VPSidebarItem level-0" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>开发工具配置</h2><!----></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>工具概览</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/vscode.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>VS Code配置</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/debugging.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>调试工具</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/package-manager.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>包管理工具</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><div class="group" data-v-af16598e><section class="VPSidebarItem level-0 collapsible has-active" data-v-af16598e data-v-c4656e6d><div class="item" role="button" tabindex="0" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><h2 class="text" data-v-c4656e6d>代码质量工具</h2><div class="caret" role="button" aria-label="toggle section" tabindex="0" data-v-c4656e6d><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" viewbox="0 0 24 24" class="caret-icon" data-v-c4656e6d><path d="M9,19c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l5.3-5.3L8.3,6.7c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l6,6c0.4,0.4,0.4,1,0,1.4l-6,6C9.5,18.9,9.3,19,9,19z"></path></svg></div></div><div class="items" data-v-c4656e6d><!--[--><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/eslint.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>ESLint配置</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/prettier.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Prettier配置</p><!--]--><!----></a><!----></div><!----></div><div class="VPSidebarItem level-1 is-link is-active has-active" data-v-c4656e6d data-v-c4656e6d><div class="item" data-v-c4656e6d><div class="indicator" data-v-c4656e6d></div><a class="VPLink link link" href="/tools/husky.html" data-v-c4656e6d data-v-8f4dc553><!--[--><p class="text" data-v-c4656e6d>Husky配置</p><!--]--><!----></a><!----></div><!----></div><!--]--></div></section></div><!--]--><!--[--><!--]--></nav></aside><div class="VPContent has-sidebar" id="VPContent" data-v-b2cf3e0b data-v-a494bd1d><div class="VPDoc has-sidebar has-aside" data-v-a494bd1d data-v-c4b0d3cf><!--[--><!--]--><div class="container" data-v-c4b0d3cf><div class="aside" data-v-c4b0d3cf><div class="aside-curtain" data-v-c4b0d3cf></div><div class="aside-container" data-v-c4b0d3cf><div class="aside-content" data-v-c4b0d3cf><div class="VPDocAside" data-v-c4b0d3cf data-v-3f215769><!--[--><!--]--><!--[--><!--]--><div class="VPDocAsideOutline" data-v-3f215769 data-v-ff0f39c8><div class="content" data-v-ff0f39c8><div class="outline-marker" data-v-ff0f39c8></div><div class="outline-title" data-v-ff0f39c8>On this page</div><nav aria-labelledby="doc-outline-aria-label" data-v-ff0f39c8><span class="visually-hidden" id="doc-outline-aria-label" data-v-ff0f39c8> Table of Contents for current page </span><ul class="root" data-v-ff0f39c8 data-v-9a431c33><!--[--><!--]--></ul></nav></div></div><!--[--><!--]--><div class="spacer" data-v-3f215769></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-c4b0d3cf><div class="content-container" data-v-c4b0d3cf><!--[--><!--]--><!----><main class="main" data-v-c4b0d3cf><div style="position:relative;" class="vp-doc _tools_husky" data-v-c4b0d3cf><div><h1 id="husky-配置指南" tabindex="-1">Husky 配置指南 <a class="header-anchor" href="#husky-配置指南" aria-label="Permalink to &quot;Husky 配置指南&quot;">​</a></h1><p>Husky 是一个 Git hooks 工具，可以在 Git 操作（如提交、推送）时自动执行脚本，确保代码质量和规范。本文档提供了完整的 Husky 配置指南。</p><h2 id="什么是-git-hooks" tabindex="-1">什么是 Git Hooks <a class="header-anchor" href="#什么是-git-hooks" aria-label="Permalink to &quot;什么是 Git Hooks&quot;">​</a></h2><p>Git Hooks 是 Git 在特定事件发生时自动执行的脚本，常用的 hooks 包括：</p><ul><li><code>pre-commit</code>：提交前执行</li><li><code>commit-msg</code>：提交信息验证</li><li><code>pre-push</code>：推送前执行</li><li><code>post-merge</code>：合并后执行</li></ul><h2 id="安装和基础配置" tabindex="-1">安装和基础配置 <a class="header-anchor" href="#安装和基础配置" aria-label="Permalink to &quot;安装和基础配置&quot;">​</a></h2><h3 id="_1-安装-husky" tabindex="-1">1. 安装 Husky <a class="header-anchor" href="#_1-安装-husky" aria-label="Permalink to &quot;1. 安装 Husky&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 安装 Husky</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 启用 Git hooks</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 添加到 package.json 脚本中</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">pkg</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">scripts.prepare=</span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">husky install</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h3 id="_2-husky-v7-配置" tabindex="-1">2. Husky v7+ 配置 <a class="header-anchor" href="#_2-husky-v7-配置" aria-label="Permalink to &quot;2. Husky v7+ 配置&quot;">​</a></h3><p>创建 <code>.husky</code> 目录和 hooks：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 创建 pre-commit hook</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-commit</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">npm run lint</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 创建 commit-msg hook</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/commit-msg</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">npx commitlint --edit </span><span style="color:#BABED8;font-style:italic;">$1</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 创建 pre-push hook</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">add</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-push</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">npm run test</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h3 id="_3-目录结构" tabindex="-1">3. 目录结构 <a class="header-anchor" href="#_3-目录结构" aria-label="Permalink to &quot;3. 目录结构&quot;">​</a></h3><div class="language-"><button title="Copy Code" class="copy"></button><span class="lang"></span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#babed8;">.husky/</span></span>
<span class="line"><span style="color:#babed8;">├── _/</span></span>
<span class="line"><span style="color:#babed8;">│   ├── .gitignore</span></span>
<span class="line"><span style="color:#babed8;">│   └── husky.sh</span></span>
<span class="line"><span style="color:#babed8;">├── pre-commit</span></span>
<span class="line"><span style="color:#babed8;">├── commit-msg</span></span>
<span class="line"><span style="color:#babed8;">└── pre-push</span></span></code></pre></div><h2 id="常用-hooks-配置" tabindex="-1">常用 Hooks 配置 <a class="header-anchor" href="#常用-hooks-配置" aria-label="Permalink to &quot;常用 Hooks 配置&quot;">​</a></h2><h3 id="_1-pre-commit-hook" tabindex="-1">1. pre-commit Hook <a class="header-anchor" href="#_1-pre-commit-hook" aria-label="Permalink to &quot;1. pre-commit Hook&quot;">​</a></h3><p><code>.husky/pre-commit</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行 lint-staged</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 或者直接运行命令</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># npm run lint</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># npm run format</span></span></code></pre></div><h3 id="_2-commit-msg-hook" tabindex="-1">2. commit-msg Hook <a class="header-anchor" href="#_2-commit-msg-hook" aria-label="Permalink to &quot;2. commit-msg Hook&quot;">​</a></h3><p><code>.husky/commit-msg</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 验证提交信息格式</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commitlint</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--edit</span><span style="color:#BABED8;"> </span><span style="color:#BABED8;font-style:italic;">$1</span></span></code></pre></div><h3 id="_3-pre-push-hook" tabindex="-1">3. pre-push Hook <a class="header-anchor" href="#_3-pre-push-hook" aria-label="Permalink to &quot;3. pre-push Hook&quot;">​</a></h3><p><code>.husky/pre-push</code> 文件：</p><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行测试</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行构建检查</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">build</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查分支名称</span></span>
<span class="line"><span style="color:#BABED8;">branch</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">$(</span><span style="color:#FFCB6B;">git</span><span style="color:#C3E88D;"> rev-parse --abbrev-ref HEAD</span><span style="color:#89DDFF;">)</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[[</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">master</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">||</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">main</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">]];</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">不能直接推送到主分支</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span></code></pre></div><h2 id="与-lint-staged-集成" tabindex="-1">与 lint-staged 集成 <a class="header-anchor" href="#与-lint-staged-集成" aria-label="Permalink to &quot;与 lint-staged 集成&quot;">​</a></h2><h3 id="_1-安装-lint-staged" tabindex="-1">1. 安装 lint-staged <a class="header-anchor" href="#_1-安装-lint-staged" aria-label="Permalink to &quot;1. 安装 lint-staged&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span></code></pre></div><h3 id="_2-配置-lint-staged" tabindex="-1">2. 配置 lint-staged <a class="header-anchor" href="#_2-配置-lint-staged" aria-label="Permalink to &quot;2. 配置 lint-staged&quot;">​</a></h3><p>在 <code>package.json</code> 中配置：</p><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">lint-staged</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">*.{js,vue}</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">eslint --fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">],</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">*.{css,scss}</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">stylelint --fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">],</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">*.{json,md}</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><p>或创建 <code>.lintstagedrc.js</code> 文件：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{css,scss}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">stylelint --fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{json,md}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{png,jpg,jpeg,gif,svg}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">imagemin-lint-staged</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-高级-lint-staged-配置" tabindex="-1">3. 高级 lint-staged 配置 <a class="header-anchor" href="#_3-高级-lint-staged-配置" aria-label="Permalink to &quot;3. 高级 lint-staged 配置&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// .lintstagedrc.js</span></span>
<span class="line"><span style="color:#C792EA;">const</span><span style="color:#BABED8;"> path </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#82AAFF;">require</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">path</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 相对路径配置</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span></span>
<span class="line"><span style="color:#BABED8;">    filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">map</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filename</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">filename</span><span style="color:#89DDFF;">}`</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 条件执行</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">]</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 只对 src 目录下的文件运行测试</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">srcFiles</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">filter</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">file</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">file</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">startsWith</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">src/</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    )</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">srcFiles</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">0</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#BABED8;">commands</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">npm run test:unit</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 自定义函数</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> []</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 检查组件命名</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">forEach</span><span style="color:#F07178;">(</span><span style="color:#BABED8;font-style:italic;">filename</span><span style="color:#F07178;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#C792EA;">const</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">basename</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">path</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">basename</span><span style="color:#F07178;">(</span><span style="color:#BABED8;">filename</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">.vue</span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">basename</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">!==</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">basename</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">charAt</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">0</span><span style="color:#F07178;">)</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">toUpperCase</span><span style="color:#F07178;">() </span><span style="color:#89DDFF;">+</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">basename</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">slice</span><span style="color:#F07178;">(</span><span style="color:#F78C6C;">1</span><span style="color:#F07178;">)) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;font-style:italic;">throw</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">new</span><span style="color:#F07178;"> </span><span style="color:#82AAFF;">Error</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">组件文件名应该使用 PascalCase: </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">filename</span><span style="color:#89DDFF;">}`</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#BABED8;">commands</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">push</span><span style="color:#F07178;">(</span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}`</span><span style="color:#F07178;">)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#BABED8;">commands</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="提交信息验证" tabindex="-1">提交信息验证 <a class="header-anchor" href="#提交信息验证" aria-label="Permalink to &quot;提交信息验证&quot;">​</a></h2><h3 id="_1-安装-commitlint" tabindex="-1">1. 安装 commitlint <a class="header-anchor" href="#_1-安装-commitlint" aria-label="Permalink to &quot;1. 安装 commitlint&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--save-dev</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@commitlint/cli</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">@commitlint/config-conventional</span></span></code></pre></div><h3 id="_2-配置-commitlint" tabindex="-1">2. 配置 commitlint <a class="header-anchor" href="#_2-配置-commitlint" aria-label="Permalink to &quot;2. 配置 commitlint&quot;">​</a></h3><p>创建 <code>commitlint.config.js</code> 文件：</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">extends</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@commitlint/config-conventional</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">rules</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-enum</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">feat</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 新功能</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 修复</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">docs</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 文档</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">style</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 格式</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">refactor</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 重构</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">perf</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 性能优化</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">test</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 测试</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">chore</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 构建过程或辅助工具的变动</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">revert</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 回滚</span></span>
<span class="line"><span style="color:#BABED8;">      ]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-empty</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">scope-empty</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">scope-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-empty</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-full-stop</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">never</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">.</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">header-max-length</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">72</span><span style="color:#BABED8;">]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-自定义提交信息规则" tabindex="-1">3. 自定义提交信息规则 <a class="header-anchor" href="#_3-自定义提交信息规则" aria-label="Permalink to &quot;3. 自定义提交信息规则&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// commitlint.config.js</span></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">extends</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">@commitlint/config-conventional</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#F07178;">rules</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 自定义类型</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">type-enum</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">feat</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 新功能</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">fix</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 修复bug</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">docs</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 文档更新</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">style</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 代码风格调整</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">refactor</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 重构</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">perf</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 性能优化</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">test</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 测试</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">chore</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 构建过程或辅助工具的变动</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">revert</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">   </span><span style="color:#676E95;font-style:italic;">// 回滚</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">wip</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 开发中</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">build</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">    </span><span style="color:#676E95;font-style:italic;">// 构建系统</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">ci</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// CI配置</span></span>
<span class="line"><span style="color:#BABED8;">      ]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 自定义作用域</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">scope-enum</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">components</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#676E95;font-style:italic;">// 组件</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">utils</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 工具</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">api</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 接口</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">router</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 路由</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">store</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">      </span><span style="color:#676E95;font-style:italic;">// 状态管理</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">styles</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 样式</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">config</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;">     </span><span style="color:#676E95;font-style:italic;">// 配置</span></span>
<span class="line"><span style="color:#BABED8;">        </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">deps</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">        </span><span style="color:#676E95;font-style:italic;">// 依赖</span></span>
<span class="line"><span style="color:#BABED8;">      ]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 主题长度限制</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-max-length</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">50</span><span style="color:#BABED8;">]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">// 主题格式</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">subject-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#F78C6C;">2</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">always</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      [</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">sentence-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">start-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">pascal-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">upper-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">lower-case</span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">]</span></span>
<span class="line"><span style="color:#BABED8;">    ]</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="高级配置" tabindex="-1">高级配置 <a class="header-anchor" href="#高级配置" aria-label="Permalink to &quot;高级配置&quot;">​</a></h2><h3 id="_1-条件执行" tabindex="-1">1. 条件执行 <a class="header-anchor" href="#_1-条件执行" aria-label="Permalink to &quot;1. 条件执行&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .husky/pre-commit</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查是否在 CI 环境中</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$CI</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">true</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">];</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">跳过 pre-commit hooks (CI 环境)</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">0</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查分支</span></span>
<span class="line"><span style="color:#BABED8;">branch</span><span style="color:#89DDFF;">=</span><span style="color:#89DDFF;">$(</span><span style="color:#FFCB6B;">git</span><span style="color:#C3E88D;"> rev-parse --abbrev-ref HEAD</span><span style="color:#89DDFF;">)</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">[[</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">master</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">||</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;">$branch</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">==</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">main</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">]];</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">主分支需要更严格的检查</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test:all</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint:strict</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">else</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span></code></pre></div><h3 id="_2-性能优化" tabindex="-1">2. 性能优化 <a class="header-anchor" href="#_2-性能优化" aria-label="Permalink to &quot;2. 性能优化&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// .lintstagedrc.js</span></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 并行执行</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix --cache</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write --cache</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 只检查修改的文件</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.js</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}`</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">jest --findRelatedTests </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}`</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_3-错误处理" tabindex="-1">3. 错误处理 <a class="header-anchor" href="#_3-错误处理" aria-label="Permalink to &quot;3. 错误处理&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .husky/pre-commit</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 设置错误处理</span></span>
<span class="line"><span style="color:#82AAFF;">set</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-e</span></span>
<span class="line"></span>
<span class="line"><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">运行 pre-commit 检查...</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行 lint-staged</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">❌ lint-staged 检查失败</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 运行测试</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">!</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">test:changed</span><span style="color:#89DDFF;">;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;font-style:italic;">then</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">❌ 测试失败</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#82AAFF;">exit</span><span style="color:#BABED8;"> </span><span style="color:#F78C6C;">1</span></span>
<span class="line"><span style="color:#89DDFF;font-style:italic;">fi</span></span>
<span class="line"></span>
<span class="line"><span style="color:#82AAFF;">echo</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">✅ 所有检查通过</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h2 id="团队协作配置" tabindex="-1">团队协作配置 <a class="header-anchor" href="#团队协作配置" aria-label="Permalink to &quot;团队协作配置&quot;">​</a></h2><h3 id="_1-统一配置" tabindex="-1">1. 统一配置 <a class="header-anchor" href="#_1-统一配置" aria-label="Permalink to &quot;1. 统一配置&quot;">​</a></h3><p>在 <code>package.json</code> 中添加：</p><div class="language-json"><button title="Copy Code" class="copy"></button><span class="lang">json</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">scripts</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">prepare</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">husky install</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">postinstall</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">husky install</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C792EA;">husky</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&quot;</span><span style="color:#FFCB6B;">hooks</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#F78C6C;">pre-commit</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">lint-staged</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">      </span><span style="color:#89DDFF;">&quot;</span><span style="color:#F78C6C;">commit-msg</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">commitlint -E HUSKY_GIT_PARAMS</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h3 id="_2-跳过-hooks" tabindex="-1">2. 跳过 Hooks <a class="header-anchor" href="#_2-跳过-hooks" aria-label="Permalink to &quot;2. 跳过 Hooks&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 跳过 pre-commit hook</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix: urgent fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--no-verify</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 跳过所有 hooks</span></span>
<span class="line"><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix: urgent fix</span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-n</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 环境变量跳过</span></span>
<span class="line"><span style="color:#BABED8;">HUSKY</span><span style="color:#89DDFF;">=</span><span style="color:#F78C6C;">0</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">fix: urgent fix</span><span style="color:#89DDFF;">&quot;</span></span></code></pre></div><h3 id="_3-调试-hooks" tabindex="-1">3. 调试 Hooks <a class="header-anchor" href="#_3-调试-hooks" aria-label="Permalink to &quot;3. 调试 Hooks&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 调试模式</span></span>
<span class="line"><span style="color:#BABED8;">HUSKY_DEBUG</span><span style="color:#89DDFF;">=</span><span style="color:#F78C6C;">1</span><span style="color:#BABED8;"> </span><span style="color:#FFCB6B;">git</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">commit</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-m</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">test</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 查看 hook 执行过程</span></span>
<span class="line"><span style="color:#FFCB6B;">sh</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-x</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-commit</span></span></code></pre></div><h2 id="常见问题解决" tabindex="-1">常见问题解决 <a class="header-anchor" href="#常见问题解决" aria-label="Permalink to &quot;常见问题解决&quot;">​</a></h2><h3 id="_1-hooks-不执行" tabindex="-1">1. Hooks 不执行 <a class="header-anchor" href="#_1-hooks-不执行" aria-label="Permalink to &quot;1. Hooks 不执行&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 检查 husky 是否正确安装</span></span>
<span class="line"><span style="color:#FFCB6B;">ls</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-la</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.git/hooks/</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 重新安装 husky</span></span>
<span class="line"><span style="color:#FFCB6B;">rm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">-rf</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.git/hooks</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">husky</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 检查权限</span></span>
<span class="line"><span style="color:#FFCB6B;">chmod</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">+x</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">.husky/pre-commit</span></span></code></pre></div><h3 id="_2-windows-兼容性" tabindex="-1">2. Windows 兼容性 <a class="header-anchor" href="#_2-windows-兼容性" aria-label="Permalink to &quot;2. Windows 兼容性&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># .husky/pre-commit (Windows 兼容)</span></span>
<span class="line"><span style="color:#676E95;font-style:italic;">#!/usr/bin/env sh</span></span>
<span class="line"><span style="color:#82AAFF;">.</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">&quot;$(</span><span style="color:#FFCB6B;">dirname</span><span style="color:#C3E88D;"> -- </span><span style="color:#89DDFF;">&quot;</span><span style="color:#BABED8;font-style:italic;">$0</span><span style="color:#89DDFF;">&quot;)</span><span style="color:#C3E88D;">/_/husky.sh</span><span style="color:#89DDFF;">&quot;</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 使用 npx 确保跨平台兼容</span></span>
<span class="line"><span style="color:#FFCB6B;">npx</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">lint-staged</span></span></code></pre></div><h3 id="_3-性能问题" tabindex="-1">3. 性能问题 <a class="header-anchor" href="#_3-性能问题" aria-label="Permalink to &quot;3. 性能问题&quot;">​</a></h3><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;">// 优化 lint-staged 配置</span></span>
<span class="line"><span style="color:#89DDFF;">module.exports</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">=</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 使用缓存</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.{js,vue}</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> [</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">eslint --fix --cache</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">    </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">prettier --write --cache</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#BABED8;">  ]</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#BABED8;">  </span></span>
<span class="line"><span style="color:#89DDFF;">  </span><span style="color:#676E95;font-style:italic;">// 限制文件数量</span></span>
<span class="line"><span style="color:#BABED8;">  </span><span style="color:#89DDFF;">&#39;</span><span style="color:#F07178;">*.js</span><span style="color:#89DDFF;">&#39;</span><span style="color:#89DDFF;">:</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">(</span><span style="color:#BABED8;font-style:italic;">filenames</span><span style="color:#89DDFF;">)</span><span style="color:#BABED8;"> </span><span style="color:#C792EA;">=&gt;</span><span style="color:#BABED8;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">if</span><span style="color:#F07178;"> (</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#BABED8;">length</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&gt;</span><span style="color:#F07178;"> </span><span style="color:#F78C6C;">10</span><span style="color:#F07178;">) </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;">npm run lint:all</span><span style="color:#89DDFF;">&#39;</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">`</span><span style="color:#C3E88D;">eslint --fix </span><span style="color:#89DDFF;">${</span><span style="color:#BABED8;">filenames</span><span style="color:#89DDFF;">.</span><span style="color:#82AAFF;">join</span><span style="color:#BABED8;">(</span><span style="color:#89DDFF;">&#39;</span><span style="color:#C3E88D;"> </span><span style="color:#89DDFF;">&#39;</span><span style="color:#BABED8;">)</span><span style="color:#89DDFF;">}`</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="最佳实践" tabindex="-1">最佳实践 <a class="header-anchor" href="#最佳实践" aria-label="Permalink to &quot;最佳实践&quot;">​</a></h2><h3 id="_1-渐进式采用" tabindex="-1">1. 渐进式采用 <a class="header-anchor" href="#_1-渐进式采用" aria-label="Permalink to &quot;1. 渐进式采用&quot;">​</a></h3><ul><li>从简单的 lint 检查开始</li><li>逐步添加更多检查</li><li>团队讨论和调整规则</li></ul><h3 id="_2-性能考虑" tabindex="-1">2. 性能考虑 <a class="header-anchor" href="#_2-性能考虑" aria-label="Permalink to &quot;2. 性能考虑&quot;">​</a></h3><ul><li>使用缓存加速检查</li><li>只检查修改的文件</li><li>合理设置超时时间</li></ul><h3 id="_3-团队协作" tabindex="-1">3. 团队协作 <a class="header-anchor" href="#_3-团队协作" aria-label="Permalink to &quot;3. 团队协作&quot;">​</a></h3><ul><li>统一 hooks 配置</li><li>提供跳过机制</li><li>文档化配置说明</li></ul><h3 id="_4-错误处理" tabindex="-1">4. 错误处理 <a class="header-anchor" href="#_4-错误处理" aria-label="Permalink to &quot;4. 错误处理&quot;">​</a></h3><ul><li>提供清晰的错误信息</li><li>给出修复建议</li><li>记录常见问题解决方案</li></ul><p>通过合理配置 Husky，可以在代码提交前自动执行质量检查，确保代码库的健康和团队开发规范的执行。</p></div></div></main><footer class="VPDocFooter" data-v-c4b0d3cf data-v-face870a><!--[--><!--]--><!----><div class="prev-next" data-v-face870a><div class="pager" data-v-face870a><a class="pager-link prev" href="/tools/prettier.html" data-v-face870a><span class="desc" data-v-face870a>Previous page</span><span class="title" data-v-face870a>Prettier配置</span></a></div><div class="has-prev pager" data-v-face870a><!----></div></div></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><footer class="VPFooter has-sidebar" data-v-b2cf3e0b data-v-2f86ebd2><div class="container" data-v-2f86ebd2><!----><p class="copyright" data-v-2f86ebd2>Copyright © 2025 智洋上水</p></div></footer><!--[--><!--]--></div></div>
    <script>__VP_HASH_MAP__ = JSON.parse("{\"best-practices_index.md\":\"38aee9d5\",\"components_directives_permission.md\":\"72d28b46\",\"best-practices_component-design.md\":\"d570a27f\",\"index.md\":\"3e96beb2\",\"cesium_index.md\":\"440ac056\",\"standards_code-review.md\":\"04c78cff\",\"tools_vscode.md\":\"71a1eb8e\",\"tools_husky.md\":\"586bda1d\",\"components_charts.md\":\"1b67c1bd\",\"tools_debugging.md\":\"506e7f52\",\"components_directives_table-height.md\":\"1acd3698\",\"components_business_common-dialog-box.md\":\"605964f5\",\"cesium_concepts.md\":\"608db15b\",\"best-practices_vuex-best-practices.md\":\"42c910e9\",\"cesium_operations.md\":\"4af21b3f\",\"tools_index.md\":\"6738f00c\",\"components_directives_index.md\":\"e6ec9d5d\",\"best-practices_project-structure.md\":\"1dfc19f7\",\"guide_index.md\":\"a218fcb9\",\"best-practices_performance.md\":\"495f9c35\",\"best-practices_async-data.md\":\"d6f4dbd8\",\"standards_vue-standard.md\":\"204374d5\",\"guide_project-structure.md\":\"83547606\",\"standards_css-standard.md\":\"43da9895\",\"standards_html-standard.md\":\"87257590\",\"components_directives_throttle.md\":\"b30fc5ee\",\"components_directives_loading.md\":\"0bd89f42\",\"cesium_examples_water-monitor.md\":\"432751c3\",\"standards_js-standard.md\":\"e5f97f25\",\"tools_eslint.md\":\"eea2bb46\",\"components_directives_copy.md\":\"1f572838\",\"standards_documentation.md\":\"4ebedf27\",\"components_directives_debounce.md\":\"6c3b5296\",\"standards_git-workflow.md\":\"a7004f93\",\"best-practices_modular-development.md\":\"ce72d502\",\"components_index.md\":\"06a93849\",\"components_business_dict-select.md\":\"5f3747e9\",\"components_business_custom-file-upload.md\":\"40d498f0\",\"components_business_map-visualization.md\":\"11e90cc6\",\"tools_prettier.md\":\"dc0ae721\",\"best-practices_charts.md\":\"3ff7afe8\",\"components_form.md\":\"a7aefeb8\",\"components_business_dict-tag.md\":\"f87374af\",\"components_business_input-number.md\":\"186c78fb\",\"best-practices_state-management.md\":\"6cde5f2d\",\"best-practices_api-request.md\":\"32a5c3df\",\"components_business_input-word.md\":\"28d03cd2\",\"best-practices_utils.md\":\"08da2e9d\",\"guide_development-process.md\":\"7d0d3459\",\"tools_package-manager.md\":\"7c6faea2\",\"components_screen.md\":\"a126a925\",\"components_business_file-preview.md\":\"f077827f\",\"components_directives_drag-dialog.md\":\"fc009f97\",\"cesium_basics.md\":\"18bfba98\",\"components_business.md\":\"********\",\"standards_index.md\":\"e5fe1d60\",\"standards_git-commit.md\":\"4953e1ed\",\"best-practices_error-handling.md\":\"37d5b8d1\",\"best-practices_i18n.md\":\"83c49ca7\",\"components_table.md\":\"9742235f\",\"best-practices_reuse.md\":\"d4c27921\",\"best-practices_component-communication.md\":\"da381d67\",\"best-practices_routing.md\":\"a83fde5f\",\"guide_admin-development.md\":\"b638fd3e\",\"components_business_image-upload.md\":\"f2d49e5f\"}")
__VP_SITE_DATA__ = JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"前端技术开发文档\",\"description\":\"A VitePress site\",\"base\":\"/\",\"head\":[],\"appearance\":true,\"themeConfig\":{\"logo\":\"/logo.jpeg\",\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"指南\",\"link\":\"/guide/\"},{\"text\":\"组件\",\"link\":\"/components/\"},{\"text\":\"最佳实践\",\"link\":\"/best-practices/\"},{\"text\":\"规范标准\",\"link\":\"/standards/\"},{\"text\":\"工具配置\",\"link\":\"/tools/\"},{\"text\":\"Cesium\",\"link\":\"/cesium/\"}],\"search\":{\"provider\":\"local\",\"options\":{\"translations\":{\"button\":{\"buttonText\":\"搜索文档\",\"buttonAriaLabel\":\"搜索文档\"},\"modal\":{\"noResultsText\":\"无法找到相关结果\",\"resetButtonTitle\":\"清除查询条件\",\"footer\":{\"selectText\":\"选择\",\"navigateText\":\"切换\",\"closeText\":\"关闭\"}}}}},\"sidebar\":{\"/guide/\":[{\"text\":\"开发指南\",\"items\":[{\"text\":\"快速开始\",\"link\":\"/guide/\"},{\"text\":\"项目结构\",\"link\":\"/guide/project-structure\"},{\"text\":\"开发流程\",\"link\":\"/guide/development-process\"},{\"text\":\"后台管理开发\",\"link\":\"/guide/admin-development\"}]}],\"/components/\":[{\"text\":\"组件库\",\"items\":[{\"text\":\"组件概览\",\"link\":\"/components/\"},{\"text\":\"业务组件\",\"collapsed\":false,\"items\":[{\"text\":\"业务组件总览\",\"link\":\"/components/business\"},{\"text\":\"字典标签组件\",\"link\":\"/components/business/dict-tag\"},{\"text\":\"字典选择器\",\"link\":\"/components/business/dict-select\"},{\"text\":\"自定义文件上传\",\"link\":\"/components/business/custom-file-upload\"},{\"text\":\"图片上传组件\",\"link\":\"/components/business/image-upload\"},{\"text\":\"自定义数字输入框\",\"link\":\"/components/business/input-number\"},{\"text\":\"自定义文本输入框\",\"link\":\"/components/business/input-word\"},{\"text\":\"文件预览组件\",\"link\":\"/components/business/file-preview\"},{\"text\":\"地图可视化组件\",\"link\":\"/components/business/map-visualization\"}]},{\"text\":\"表单组件\",\"link\":\"/components/form\"},{\"text\":\"表格组件\",\"link\":\"/components/table\"},{\"text\":\"图表组件\",\"link\":\"/components/charts\"},{\"text\":\"全局指令\",\"collapsed\":false,\"items\":[{\"text\":\"指令概览\",\"link\":\"/components/directives/index\"},{\"text\":\"表格高度\",\"link\":\"/components/directives/table-height\"},{\"text\":\"权限控制\",\"link\":\"/components/directives/permission\"},{\"text\":\"弹窗拖拽\",\"link\":\"/components/directives/drag-dialog\"},{\"text\":\"防抖处理\",\"link\":\"/components/directives/debounce\"},{\"text\":\"节流处理\",\"link\":\"/components/directives/throttle\"},{\"text\":\"一键复制\",\"link\":\"/components/directives/copy\"}]},{\"text\":\"大屏开发\",\"link\":\"/components/screen\"}]}],\"/best-practices/\":[{\"text\":\"最佳实践\",\"items\":[{\"text\":\"概述\",\"link\":\"/best-practices/\"},{\"text\":\"性能优化\",\"link\":\"/best-practices/performance\"},{\"text\":\"代码复用\",\"link\":\"/best-practices/reuse\"},{\"text\":\"状态管理\",\"link\":\"/best-practices/state-management\"},{\"text\":\"路由管理\",\"link\":\"/best-practices/routing\"},{\"text\":\"组件通信\",\"link\":\"/best-practices/component-communication\"},{\"text\":\"异步数据处理\",\"link\":\"/best-practices/async-data\"},{\"text\":\"模块化开发\",\"link\":\"/best-practices/modular-development\"},{\"text\":\"错误处理\",\"link\":\"/best-practices/error-handling\"},{\"text\":\"国际化实现\",\"link\":\"/best-practices/i18n\"},{\"text\":\"Vue组件设计\",\"link\":\"/best-practices/component-design\"},{\"text\":\"Vuex最佳实践\",\"link\":\"/best-practices/vuex-best-practices\"},{\"text\":\"Vue项目结构\",\"link\":\"/best-practices/project-structure\"},{\"text\":\"API请求封装\",\"link\":\"/best-practices/api-request\"},{\"text\":\"工具函数\",\"link\":\"/best-practices/utils\"}]}],\"/standards/\":[{\"text\":\"规范标准\",\"items\":[{\"text\":\"规范概述\",\"link\":\"/standards/\"},{\"text\":\"编码规范\",\"collapsed\":false,\"items\":[{\"text\":\"JavaScript规范\",\"link\":\"/standards/js-standard\"},{\"text\":\"CSS规范\",\"link\":\"/standards/css-standard\"},{\"text\":\"HTML规范\",\"link\":\"/standards/html-standard\"},{\"text\":\"Vue开发规范\",\"link\":\"/standards/vue-standard\"}]},{\"text\":\"Git提交规范\",\"link\":\"/standards/git-commit\"},{\"text\":\"Git工作流\",\"link\":\"/standards/git-workflow\"},{\"text\":\"代码审查\",\"link\":\"/standards/code-review\"},{\"text\":\"文档规范\",\"link\":\"/standards/documentation\"}]}],\"/tools/\":[{\"text\":\"开发工具配置\",\"items\":[{\"text\":\"工具概览\",\"link\":\"/tools/\"},{\"text\":\"VS Code配置\",\"link\":\"/tools/vscode\"},{\"text\":\"调试工具\",\"link\":\"/tools/debugging\"},{\"text\":\"包管理工具\",\"link\":\"/tools/package-manager\"}]},{\"text\":\"代码质量工具\",\"collapsed\":false,\"items\":[{\"text\":\"ESLint配置\",\"link\":\"/tools/eslint\"},{\"text\":\"Prettier配置\",\"link\":\"/tools/prettier\"},{\"text\":\"Husky配置\",\"link\":\"/tools/husky\"}]}],\"/cesium/\":[{\"text\":\"Cesium 3D地图引擎\",\"items\":[{\"text\":\"简介\",\"link\":\"/cesium/\"},{\"text\":\"基础配置\",\"link\":\"/cesium/basics\"},{\"text\":\"核心概念\",\"link\":\"/cesium/concepts\"},{\"text\":\"常用操作\",\"link\":\"/cesium/operations\"}]},{\"text\":\"实战案例\",\"collapsed\":false,\"items\":[{\"text\":\"智慧水务监控\",\"link\":\"/cesium/examples/water-monitor\"}]}]},\"footer\":{\"copyright\":\"Copyright © 2025 智洋上水\"}},\"locales\":{},\"scrollOffset\":90,\"cleanUrls\":false}")</script>
    
  </body>
</html>