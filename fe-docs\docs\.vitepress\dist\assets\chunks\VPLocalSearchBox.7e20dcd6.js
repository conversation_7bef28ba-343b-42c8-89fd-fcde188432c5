import{M as We,d as Xe,v as X,p as De,j as ee,q as et,w as ze,k as fe,l as de,al as tt,am as rt,o as U,D as nt,C as g,b as V,a5 as it,a6 as at,s as ot,c as K,n as Te,G as se,R as Le,F as Ne,a as te,t as re,an as st,S as lt,U as ut,ao as Oe,ap as ct,aa as ht,ag as ft,aq as dt,_ as vt}from"./framework.3d729ebc.js";import{u as pt,c as Re,a as yt,b as mt,w as gt,o as le,d as xt,e as wt,f as Ft}from"./theme.533e1ce4.js";const bt={root:()=>We(()=>import("./@localSearchIndexroot.18cc1f96.js"),[])};class Z{constructor(e,t=!0,r=[],i=5e3){this.ctx=e,this.iframes=t,this.exclude=r,this.iframesTimeout=i}static matches(e,t){const r=typeof t=="string"?[t]:t,i=e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector;if(i){let n=!1;return r.every(a=>i.call(e,a)?(n=!0,!1):!0),n}else return!1}getContexts(){let e,t=[];return typeof this.ctx>"u"||!this.ctx?e=[]:NodeList.prototype.isPrototypeOf(this.ctx)?e=Array.prototype.slice.call(this.ctx):Array.isArray(this.ctx)?e=this.ctx:typeof this.ctx=="string"?e=Array.prototype.slice.call(document.querySelectorAll(this.ctx)):e=[this.ctx],e.forEach(r=>{const i=t.filter(n=>n.contains(r)).length>0;t.indexOf(r)===-1&&!i&&t.push(r)}),t}getIframeContents(e,t,r=()=>{}){let i;try{const n=e.contentWindow;if(i=n.document,!n||!i)throw new Error("iframe inaccessible")}catch{r()}i&&t(i)}isIframeBlank(e){const t="about:blank",r=e.getAttribute("src").trim();return e.contentWindow.location.href===t&&r!==t&&r}observeIframeLoad(e,t,r){let i=!1,n=null;const a=()=>{if(!i){i=!0,clearTimeout(n);try{this.isIframeBlank(e)||(e.removeEventListener("load",a),this.getIframeContents(e,t,r))}catch{r()}}};e.addEventListener("load",a),n=setTimeout(a,this.iframesTimeout)}onIframeReady(e,t,r){try{e.contentWindow.document.readyState==="complete"?this.isIframeBlank(e)?this.observeIframeLoad(e,t,r):this.getIframeContents(e,t,r):this.observeIframeLoad(e,t,r)}catch{r()}}waitForIframes(e,t){let r=0;this.forEachIframe(e,()=>!0,i=>{r++,this.waitForIframes(i.querySelector("html"),()=>{--r||t()})},i=>{i||t()})}forEachIframe(e,t,r,i=()=>{}){let n=e.querySelectorAll("iframe"),a=n.length,s=0;n=Array.prototype.slice.call(n);const l=()=>{--a<=0&&i(s)};a||l(),n.forEach(u=>{Z.matches(u,this.exclude)?l():this.onIframeReady(u,c=>{t(u)&&(s++,r(c)),l()},l)})}createIterator(e,t,r){return document.createNodeIterator(e,t,r,!1)}createInstanceOnIframe(e){return new Z(e.querySelector("html"),this.iframes)}compareNodeIframe(e,t,r){const i=e.compareDocumentPosition(r),n=Node.DOCUMENT_POSITION_PRECEDING;if(i&n)if(t!==null){const a=t.compareDocumentPosition(r),s=Node.DOCUMENT_POSITION_FOLLOWING;if(a&s)return!0}else return!0;return!1}getIteratorNode(e){const t=e.previousNode();let r;return t===null?r=e.nextNode():r=e.nextNode()&&e.nextNode(),{prevNode:t,node:r}}checkIframeFilter(e,t,r,i){let n=!1,a=!1;return i.forEach((s,l)=>{s.val===r&&(n=l,a=s.handled)}),this.compareNodeIframe(e,t,r)?(n===!1&&!a?i.push({val:r,handled:!0}):n!==!1&&!a&&(i[n].handled=!0),!0):(n===!1&&i.push({val:r,handled:!1}),!1)}handleOpenIframes(e,t,r,i){e.forEach(n=>{n.handled||this.getIframeContents(n.val,a=>{this.createInstanceOnIframe(a).forEachNode(t,r,i)})})}iterateThroughNodes(e,t,r,i,n){const a=this.createIterator(t,e,i);let s=[],l=[],u,c,h=()=>({prevNode:c,node:u}=this.getIteratorNode(a),u);for(;h();)this.iframes&&this.forEachIframe(t,f=>this.checkIframeFilter(u,c,f,s),f=>{this.createInstanceOnIframe(f).forEachNode(e,d=>l.push(d),i)}),l.push(u);l.forEach(f=>{r(f)}),this.iframes&&this.handleOpenIframes(s,e,r,i),n()}forEachNode(e,t,r,i=()=>{}){const n=this.getContexts();let a=n.length;a||i(),n.forEach(s=>{const l=()=>{this.iterateThroughNodes(e,s,t,r,()=>{--a<=0&&i()})};this.iframes?this.waitForIframes(s,l):l()})}}let Et=class{constructor(e){this.ctx=e,this.ie=!1;const t=window.navigator.userAgent;(t.indexOf("MSIE")>-1||t.indexOf("Trident")>-1)&&(this.ie=!0)}set opt(e){this._opt=Object.assign({},{element:"",className:"",exclude:[],iframes:!1,iframesTimeout:5e3,separateWordSearch:!0,diacritics:!0,synonyms:{},accuracy:"partially",acrossElements:!1,caseSensitive:!1,ignoreJoiners:!1,ignoreGroups:0,ignorePunctuation:[],wildcards:"disabled",each:()=>{},noMatch:()=>{},filter:()=>!0,done:()=>{},debug:!1,log:window.console},e)}get opt(){return this._opt}get iterator(){return new Z(this.ctx,this.opt.iframes,this.opt.exclude,this.opt.iframesTimeout)}log(e,t="debug"){const r=this.opt.log;this.opt.debug&&typeof r=="object"&&typeof r[t]=="function"&&r[t](`mark.js: ${e}`)}escapeStr(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}createRegExp(e){return this.opt.wildcards!=="disabled"&&(e=this.setupWildcardsRegExp(e)),e=this.escapeStr(e),Object.keys(this.opt.synonyms).length&&(e=this.createSynonymsRegExp(e)),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),this.opt.diacritics&&(e=this.createDiacriticsRegExp(e)),e=this.createMergedBlanksRegExp(e),(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.createJoinersRegExp(e)),this.opt.wildcards!=="disabled"&&(e=this.createWildcardsRegExp(e)),e=this.createAccuracyRegExp(e),e}createSynonymsRegExp(e){const t=this.opt.synonyms,r=this.opt.caseSensitive?"":"i",i=this.opt.ignoreJoiners||this.opt.ignorePunctuation.length?"\0":"";for(let n in t)if(t.hasOwnProperty(n)){const a=t[n],s=this.opt.wildcards!=="disabled"?this.setupWildcardsRegExp(n):this.escapeStr(n),l=this.opt.wildcards!=="disabled"?this.setupWildcardsRegExp(a):this.escapeStr(a);s!==""&&l!==""&&(e=e.replace(new RegExp(`(${this.escapeStr(s)}|${this.escapeStr(l)})`,`gm${r}`),i+`(${this.processSynomyms(s)}|${this.processSynomyms(l)})`+i))}return e}processSynomyms(e){return(this.opt.ignoreJoiners||this.opt.ignorePunctuation.length)&&(e=this.setupIgnoreJoinersRegExp(e)),e}setupWildcardsRegExp(e){return e=e.replace(/(?:\\)*\?/g,t=>t.charAt(0)==="\\"?"?":""),e.replace(/(?:\\)*\*/g,t=>t.charAt(0)==="\\"?"*":"")}createWildcardsRegExp(e){let t=this.opt.wildcards==="withSpaces";return e.replace(/\u0001/g,t?"[\\S\\s]?":"\\S?").replace(/\u0002/g,t?"[\\S\\s]*?":"\\S*")}setupIgnoreJoinersRegExp(e){return e.replace(/[^(|)\\]/g,(t,r,i)=>{let n=i.charAt(r+1);return/[(|)\\]/.test(n)||n===""?t:t+"\0"})}createJoinersRegExp(e){let t=[];const r=this.opt.ignorePunctuation;return Array.isArray(r)&&r.length&&t.push(this.escapeStr(r.join(""))),this.opt.ignoreJoiners&&t.push("\\u00ad\\u200b\\u200c\\u200d"),t.length?e.split(/\u0000+/).join(`[${t.join("")}]*`):e}createDiacriticsRegExp(e){const t=this.opt.caseSensitive?"":"i",r=this.opt.caseSensitive?["aàáảãạăằắẳẵặâầấẩẫậäåāą","AÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćč","CÇĆČ","dđď","DĐĎ","eèéẻẽẹêềếểễệëěēę","EÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïī","IÌÍỈĨỊÎÏĪ","lł","LŁ","nñňń","NÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøō","OÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rř","RŘ","sšśșş","SŠŚȘŞ","tťțţ","TŤȚŢ","uùúủũụưừứửữựûüůū","UÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿ","YÝỲỶỸỴŸ","zžżź","ZŽŻŹ"]:["aàáảãạăằắẳẵặâầấẩẫậäåāąAÀÁẢÃẠĂẰẮẲẴẶÂẦẤẨẪẬÄÅĀĄ","cçćčCÇĆČ","dđďDĐĎ","eèéẻẽẹêềếểễệëěēęEÈÉẺẼẸÊỀẾỂỄỆËĚĒĘ","iìíỉĩịîïīIÌÍỈĨỊÎÏĪ","lłLŁ","nñňńNÑŇŃ","oòóỏõọôồốổỗộơởỡớờợöøōOÒÓỎÕỌÔỒỐỔỖỘƠỞỠỚỜỢÖØŌ","rřRŘ","sšśșşSŠŚȘŞ","tťțţTŤȚŢ","uùúủũụưừứửữựûüůūUÙÚỦŨỤƯỪỨỬỮỰÛÜŮŪ","yýỳỷỹỵÿYÝỲỶỸỴŸ","zžżźZŽŻŹ"];let i=[];return e.split("").forEach(n=>{r.every(a=>{if(a.indexOf(n)!==-1){if(i.indexOf(a)>-1)return!1;e=e.replace(new RegExp(`[${a}]`,`gm${t}`),`[${a}]`),i.push(a)}return!0})}),e}createMergedBlanksRegExp(e){return e.replace(/[\s]+/gmi,"[\\s]+")}createAccuracyRegExp(e){const t="!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~¡¿";let r=this.opt.accuracy,i=typeof r=="string"?r:r.value,n=typeof r=="string"?[]:r.limiters,a="";switch(n.forEach(s=>{a+=`|${this.escapeStr(s)}`}),i){case"partially":default:return`()(${e})`;case"complementary":return a="\\s"+(a||this.escapeStr(t)),`()([^${a}]*${e}[^${a}]*)`;case"exactly":return`(^|\\s${a})(${e})(?=$|\\s${a})`}}getSeparatedKeywords(e){let t=[];return e.forEach(r=>{this.opt.separateWordSearch?r.split(" ").forEach(i=>{i.trim()&&t.indexOf(i)===-1&&t.push(i)}):r.trim()&&t.indexOf(r)===-1&&t.push(r)}),{keywords:t.sort((r,i)=>i.length-r.length),length:t.length}}isNumeric(e){return Number(parseFloat(e))==e}checkRanges(e){if(!Array.isArray(e)||Object.prototype.toString.call(e[0])!=="[object Object]")return this.log("markRanges() will only accept an array of objects"),this.opt.noMatch(e),[];const t=[];let r=0;return e.sort((i,n)=>i.start-n.start).forEach(i=>{let{start:n,end:a,valid:s}=this.callNoMatchOnInvalidRanges(i,r);s&&(i.start=n,i.length=a-n,t.push(i),r=a)}),t}callNoMatchOnInvalidRanges(e,t){let r,i,n=!1;return e&&typeof e.start<"u"?(r=parseInt(e.start,10),i=r+parseInt(e.length,10),this.isNumeric(e.start)&&this.isNumeric(e.length)&&i-t>0&&i-r>0?n=!0:(this.log(`Ignoring invalid or overlapping range: ${JSON.stringify(e)}`),this.opt.noMatch(e))):(this.log(`Ignoring invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)),{start:r,end:i,valid:n}}checkWhitespaceRanges(e,t,r){let i,n=!0,a=r.length,s=t-a,l=parseInt(e.start,10)-s;return l=l>a?a:l,i=l+parseInt(e.length,10),i>a&&(i=a,this.log(`End range automatically set to the max value of ${a}`)),l<0||i-l<0||l>a||i>a?(n=!1,this.log(`Invalid range: ${JSON.stringify(e)}`),this.opt.noMatch(e)):r.substring(l,i).replace(/\s+/g,"")===""&&(n=!1,this.log("Skipping whitespace only range: "+JSON.stringify(e)),this.opt.noMatch(e)),{start:l,end:i,valid:n}}getTextNodes(e){let t="",r=[];this.iterator.forEachNode(NodeFilter.SHOW_TEXT,i=>{r.push({start:t.length,end:(t+=i.textContent).length,node:i})},i=>this.matchesExclude(i.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT,()=>{e({value:t,nodes:r})})}matchesExclude(e){return Z.matches(e,this.opt.exclude.concat(["script","style","title","head","html"]))}wrapRangeInTextNode(e,t,r){const i=this.opt.element?this.opt.element:"mark",n=e.splitText(t),a=n.splitText(r-t);let s=document.createElement(i);return s.setAttribute("data-markjs","true"),this.opt.className&&s.setAttribute("class",this.opt.className),s.textContent=n.textContent,n.parentNode.replaceChild(s,n),a}wrapRangeInMappedTextNode(e,t,r,i,n){e.nodes.every((a,s)=>{const l=e.nodes[s+1];if(typeof l>"u"||l.start>t){if(!i(a.node))return!1;const u=t-a.start,c=(r>a.end?a.end:r)-a.start,h=e.value.substr(0,a.start),f=e.value.substr(c+a.start);if(a.node=this.wrapRangeInTextNode(a.node,u,c),e.value=h+f,e.nodes.forEach((d,v)=>{v>=s&&(e.nodes[v].start>0&&v!==s&&(e.nodes[v].start-=c),e.nodes[v].end-=c)}),r-=c,n(a.node.previousSibling,a.start),r>a.end)t=a.end;else return!1}return!0})}wrapMatches(e,t,r,i,n){const a=t===0?0:t+1;this.getTextNodes(s=>{s.nodes.forEach(l=>{l=l.node;let u;for(;(u=e.exec(l.textContent))!==null&&u[a]!=="";){if(!r(u[a],l))continue;let c=u.index;if(a!==0)for(let h=1;h<a;h++)c+=u[h].length;l=this.wrapRangeInTextNode(l,c,c+u[a].length),i(l.previousSibling),e.lastIndex=0}}),n()})}wrapMatchesAcrossElements(e,t,r,i,n){const a=t===0?0:t+1;this.getTextNodes(s=>{let l;for(;(l=e.exec(s.value))!==null&&l[a]!=="";){let u=l.index;if(a!==0)for(let h=1;h<a;h++)u+=l[h].length;const c=u+l[a].length;this.wrapRangeInMappedTextNode(s,u,c,h=>r(l[a],h),(h,f)=>{e.lastIndex=f,i(h)})}n()})}wrapRangeFromIndex(e,t,r,i){this.getTextNodes(n=>{const a=n.value.length;e.forEach((s,l)=>{let{start:u,end:c,valid:h}=this.checkWhitespaceRanges(s,a,n.value);h&&this.wrapRangeInMappedTextNode(n,u,c,f=>t(f,s,n.value.substring(u,c),l),f=>{r(f,s)})}),i()})}unwrapMatches(e){const t=e.parentNode;let r=document.createDocumentFragment();for(;e.firstChild;)r.appendChild(e.removeChild(e.firstChild));t.replaceChild(r,e),this.ie?this.normalizeTextNode(t):t.normalize()}normalizeTextNode(e){if(e){if(e.nodeType===3)for(;e.nextSibling&&e.nextSibling.nodeType===3;)e.nodeValue+=e.nextSibling.nodeValue,e.parentNode.removeChild(e.nextSibling);else this.normalizeTextNode(e.firstChild);this.normalizeTextNode(e.nextSibling)}}markRegExp(e,t){this.opt=t,this.log(`Searching with expression "${e}"`);let r=0,i="wrapMatches";const n=a=>{r++,this.opt.each(a)};this.opt.acrossElements&&(i="wrapMatchesAcrossElements"),this[i](e,this.opt.ignoreGroups,(a,s)=>this.opt.filter(s,a,r),n,()=>{r===0&&this.opt.noMatch(e),this.opt.done(r)})}mark(e,t){this.opt=t;let r=0,i="wrapMatches";const{keywords:n,length:a}=this.getSeparatedKeywords(typeof e=="string"?[e]:e),s=this.opt.caseSensitive?"":"i",l=u=>{let c=new RegExp(this.createRegExp(u),`gm${s}`),h=0;this.log(`Searching with expression "${c}"`),this[i](c,1,(f,d)=>this.opt.filter(d,u,r,h),f=>{h++,r++,this.opt.each(f)},()=>{h===0&&this.opt.noMatch(u),n[a-1]===u?this.opt.done(r):l(n[n.indexOf(u)+1])})};this.opt.acrossElements&&(i="wrapMatchesAcrossElements"),a===0?this.opt.done(r):l(n[0])}markRanges(e,t){this.opt=t;let r=0,i=this.checkRanges(e);i&&i.length?(this.log("Starting to mark with the following ranges: "+JSON.stringify(i)),this.wrapRangeFromIndex(i,(n,a,s,l)=>this.opt.filter(n,a,s,l),(n,a)=>{r++,this.opt.each(n,a)},()=>{this.opt.done(r)})):this.opt.done(r)}unmark(e){this.opt=e;let t=this.opt.element?this.opt.element:"*";t+="[data-markjs]",this.opt.className&&(t+=`.${this.opt.className}`),this.log(`Removal selector "${t}"`),this.iterator.forEachNode(NodeFilter.SHOW_ELEMENT,r=>{this.unwrapMatches(r)},r=>{const i=Z.matches(r,t),n=this.matchesExclude(r);return!i||n?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT},this.opt.done)}};function St(o){const e=new Et(o);return this.mark=(t,r)=>(e.mark(t,r),this),this.markRegExp=(t,r)=>(e.markRegExp(t,r),this),this.markRanges=(t,r)=>(e.markRanges(t,r),this),this.unmark=t=>(e.unmark(t),this),this}var N=function(){return N=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++){t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},N.apply(this,arguments)};function _t(o,e,t,r){function i(n){return n instanceof t?n:new t(function(a){a(n)})}return new(t||(t=Promise))(function(n,a){function s(c){try{u(r.next(c))}catch(h){a(h)}}function l(c){try{u(r.throw(c))}catch(h){a(h)}}function u(c){c.done?n(c.value):i(c.value).then(s,l)}u((r=r.apply(o,e||[])).next())})}function At(o,e){var t={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},r,i,n,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(u){return function(c){return l([u,c])}}function l(u){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,u[0]&&(t=0)),t;)try{if(r=1,i&&(n=u[0]&2?i.return:u[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,u[1])).done)return n;switch(i=0,n&&(u=[u[0]&2,n.value]),u[0]){case 0:case 1:n=u;break;case 4:return t.label++,{value:u[1],done:!1};case 5:t.label++,i=u[1],u=[0];continue;case 7:u=t.ops.pop(),t.trys.pop();continue;default:if(n=t.trys,!(n=n.length>0&&n[n.length-1])&&(u[0]===6||u[0]===2)){t=0;continue}if(u[0]===3&&(!n||u[1]>n[0]&&u[1]<n[3])){t.label=u[1];break}if(u[0]===6&&t.label<n[1]){t.label=n[1],n=u;break}if(n&&t.label<n[2]){t.label=n[2],t.ops.push(u);break}n[2]&&t.ops.pop(),t.trys.pop();continue}u=e.call(o,t)}catch(c){u=[6,c],i=0}finally{r=n=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function S(o){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&o[e],r=0;if(t)return t.call(o);if(o&&typeof o.length=="number")return{next:function(){return o&&r>=o.length&&(o=void 0),{value:o&&o[r++],done:!o}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function R(o,e){var t=typeof Symbol=="function"&&o[Symbol.iterator];if(!t)return o;var r=t.call(o),i,n=[],a;try{for(;(e===void 0||e-- >0)&&!(i=r.next()).done;)n.push(i.value)}catch(s){a={error:s}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(a)throw a.error}}return n}var Ct="ENTRIES",Je="KEYS",je="VALUES",B="",ve=function(){function o(e,t){var r=e._tree,i=Array.from(r.keys());this.set=e,this._type=t,this._path=i.length>0?[{node:r,keys:i}]:[]}return o.prototype.next=function(){var e=this.dive();return this.backtrack(),e},o.prototype.dive=function(){if(this._path.length===0)return{done:!0,value:void 0};var e=Y(this._path),t=e.node,r=e.keys;if(Y(r)===B)return{done:!1,value:this.result()};var i=t.get(Y(r));return this._path.push({node:i,keys:Array.from(i.keys())}),this.dive()},o.prototype.backtrack=function(){if(this._path.length!==0){var e=Y(this._path).keys;e.pop(),!(e.length>0)&&(this._path.pop(),this.backtrack())}},o.prototype.key=function(){return this.set._prefix+this._path.map(function(e){var t=e.keys;return Y(t)}).filter(function(e){return e!==B}).join("")},o.prototype.value=function(){return Y(this._path).node.get(B)},o.prototype.result=function(){switch(this._type){case je:return this.value();case Je:return this.key();default:return[this.key(),this.value()]}},o.prototype[Symbol.iterator]=function(){return this},o}(),Y=function(o){return o[o.length-1]},kt=function(o,e,t){var r=new Map;if(e===void 0)return r;for(var i=e.length+1,n=i+t,a=new Uint8Array(n*i).fill(t+1),s=0;s<i;++s)a[s]=s;for(var l=1;l<n;++l)a[l*i]=l;return Ue(o,e,t,r,a,1,i,""),r},Ue=function(o,e,t,r,i,n,a,s){var l,u,c=n*a;try{e:for(var h=S(o.keys()),f=h.next();!f.done;f=h.next()){var d=f.value;if(d===B){var v=i[c-1];v<=t&&r.set(s,[o.get(d),v])}else{for(var w=n,p=0;p<d.length;++p,++w){for(var F=d[p],b=a*w,x=b-a,M=i[b],D=Math.max(0,w-t-1),I=Math.min(a-1,w+t),E=D;E<I;++E){var A=F!==e[E],z=i[x+E]+ +A,L=i[x+E+1]+1,_=i[b+E]+1,C=i[b+E+1]=Math.min(z,L,_);C<M&&(M=C)}if(M>t)continue e}Ue(o.get(d),e,t,r,i,w,a,s+d)}}}catch(O){l={error:O}}finally{try{f&&!f.done&&(u=h.return)&&u.call(h)}finally{if(l)throw l.error}}},pe=function(){function o(e,t){e===void 0&&(e=new Map),t===void 0&&(t=""),this._size=void 0,this._tree=e,this._prefix=t}return o.prototype.atPrefix=function(e){var t,r;if(!e.startsWith(this._prefix))throw new Error("Mismatched prefix");var i=R(ce(this._tree,e.slice(this._prefix.length)),2),n=i[0],a=i[1];if(n===void 0){var s=R(Ee(a),2),l=s[0],u=s[1];try{for(var c=S(l.keys()),h=c.next();!h.done;h=c.next()){var f=h.value;if(f!==B&&f.startsWith(u)){var d=new Map;return d.set(f.slice(u.length),l.get(f)),new o(d,e)}}}catch(v){t={error:v}}finally{try{h&&!h.done&&(r=c.return)&&r.call(c)}finally{if(t)throw t.error}}}return new o(n,e)},o.prototype.clear=function(){this._size=void 0,this._tree.clear()},o.prototype.delete=function(e){return this._size=void 0,It(this._tree,e)},o.prototype.entries=function(){return new ve(this,Ct)},o.prototype.forEach=function(e){var t,r;try{for(var i=S(this),n=i.next();!n.done;n=i.next()){var a=R(n.value,2),s=a[0],l=a[1];e(s,l,this)}}catch(u){t={error:u}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},o.prototype.fuzzyGet=function(e,t){return kt(this._tree,e,t)},o.prototype.get=function(e){var t=we(this._tree,e);return t!==void 0?t.get(B):void 0},o.prototype.has=function(e){var t=we(this._tree,e);return t!==void 0&&t.has(B)},o.prototype.keys=function(){return new ve(this,Je)},o.prototype.set=function(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;var r=ye(this._tree,e);return r.set(B,t),this},Object.defineProperty(o.prototype,"size",{get:function(){if(this._size)return this._size;this._size=0;for(var e=this.entries();!e.next().done;)this._size+=1;return this._size},enumerable:!1,configurable:!0}),o.prototype.update=function(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;var r=ye(this._tree,e);return r.set(B,t(r.get(B))),this},o.prototype.fetch=function(e,t){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;var r=ye(this._tree,e),i=r.get(B);return i===void 0&&r.set(B,i=t()),i},o.prototype.values=function(){return new ve(this,je)},o.prototype[Symbol.iterator]=function(){return this.entries()},o.from=function(e){var t,r,i=new o;try{for(var n=S(e),a=n.next();!a.done;a=n.next()){var s=R(a.value,2),l=s[0],u=s[1];i.set(l,u)}}catch(c){t={error:c}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return i},o.fromObject=function(e){return o.from(Object.entries(e))},o}(),ce=function(o,e,t){var r,i;if(t===void 0&&(t=[]),e.length===0||o==null)return[o,t];try{for(var n=S(o.keys()),a=n.next();!a.done;a=n.next()){var s=a.value;if(s!==B&&e.startsWith(s))return t.push([o,s]),ce(o.get(s),e.slice(s.length),t)}}catch(l){r={error:l}}finally{try{a&&!a.done&&(i=n.return)&&i.call(n)}finally{if(r)throw r.error}}return t.push([o,e]),ce(void 0,"",t)},we=function(o,e){var t,r;if(e.length===0||o==null)return o;try{for(var i=S(o.keys()),n=i.next();!n.done;n=i.next()){var a=n.value;if(a!==B&&e.startsWith(a))return we(o.get(a),e.slice(a.length))}}catch(s){t={error:s}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},ye=function(o,e){var t,r,i=e.length;e:for(var n=0;o&&n<i;){try{for(var a=(t=void 0,S(o.keys())),s=a.next();!s.done;s=a.next()){var l=s.value;if(l!==B&&e[n]===l[0]){for(var u=Math.min(i-n,l.length),c=1;c<u&&e[n+c]===l[c];)++c;var h=o.get(l);if(c===l.length)o=h;else{var f=new Map;f.set(l.slice(c),h),o.set(e.slice(n,n+c),f),o.delete(l),o=f}n+=c;continue e}}}catch(v){t={error:v}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}var d=new Map;return o.set(e.slice(n),d),d}return o},It=function(o,e){var t=R(ce(o,e),2),r=t[0],i=t[1];if(r!==void 0){if(r.delete(B),r.size===0)He(i);else if(r.size===1){var n=R(r.entries().next().value,2),a=n[0],s=n[1];Ke(i,a,s)}}},He=function(o){if(o.length!==0){var e=R(Ee(o),2),t=e[0],r=e[1];if(t.delete(r),t.size===0)He(o.slice(0,-1));else if(t.size===1){var i=R(t.entries().next().value,2),n=i[0],a=i[1];n!==B&&Ke(o.slice(0,-1),n,a)}}},Ke=function(o,e,t){if(o.length!==0){var r=R(Ee(o),2),i=r[0],n=r[1];i.set(n+e,t),i.delete(n)}},Ee=function(o){return o[o.length-1]},ne,Se="or",Qe="and",Mt="and_not",Dt=function(){function o(e){if((e==null?void 0:e.fields)==null)throw new Error('MiniSearch: option "fields" must be provided');var t=e.autoVacuum==null||e.autoVacuum===!0?xe:e.autoVacuum;this._options=N(N(N({},ge),e),{autoVacuum:t,searchOptions:N(N({},Ve),e.searchOptions||{}),autoSuggestOptions:N(N({},Ot),e.autoSuggestOptions||{})}),this._index=new pe,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=be,this.addFields(this._options.fields)}return o.prototype.add=function(e){var t,r,i,n,a,s,l=this._options,u=l.extractField,c=l.tokenize,h=l.processTerm,f=l.fields,d=l.idField,v=u(e,d);if(v==null)throw new Error('MiniSearch: document does not have ID field "'.concat(d,'"'));if(this._idToShortId.has(v))throw new Error("MiniSearch: duplicate ID ".concat(v));var w=this.addDocumentId(v);this.saveStoredFields(w,e);try{for(var p=S(f),F=p.next();!F.done;F=p.next()){var b=F.value,x=u(e,b);if(x!=null){var M=c(x.toString(),b),D=this._fieldIds[b],I=new Set(M).size;this.addFieldLength(w,D,this._documentCount-1,I);try{for(var E=(i=void 0,S(M)),A=E.next();!A.done;A=E.next()){var z=A.value,L=h(z,b);if(Array.isArray(L))try{for(var _=(a=void 0,S(L)),C=_.next();!C.done;C=_.next()){var O=C.value;this.addTerm(D,w,O)}}catch(T){a={error:T}}finally{try{C&&!C.done&&(s=_.return)&&s.call(_)}finally{if(a)throw a.error}}else L&&this.addTerm(D,w,L)}}catch(T){i={error:T}}finally{try{A&&!A.done&&(n=E.return)&&n.call(E)}finally{if(i)throw i.error}}}}}catch(T){t={error:T}}finally{try{F&&!F.done&&(r=p.return)&&r.call(p)}finally{if(t)throw t.error}}},o.prototype.addAll=function(e){var t,r;try{for(var i=S(e),n=i.next();!n.done;n=i.next()){var a=n.value;this.add(a)}}catch(s){t={error:s}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},o.prototype.addAllAsync=function(e,t){var r=this;t===void 0&&(t={});var i=t.chunkSize,n=i===void 0?10:i,a={chunk:[],promise:Promise.resolve()},s=e.reduce(function(c,h,f){var d=c.chunk,v=c.promise;return d.push(h),(f+1)%n===0?{chunk:[],promise:v.then(function(){return new Promise(function(w){return setTimeout(w,0)})}).then(function(){return r.addAll(d)})}:{chunk:d,promise:v}},a),l=s.chunk,u=s.promise;return u.then(function(){return r.addAll(l)})},o.prototype.remove=function(e){var t,r,i,n,a,s,l=this._options,u=l.tokenize,c=l.processTerm,h=l.extractField,f=l.fields,d=l.idField,v=h(e,d);if(v==null)throw new Error('MiniSearch: document does not have ID field "'.concat(d,'"'));var w=this._idToShortId.get(v);if(w==null)throw new Error("MiniSearch: cannot remove document with ID ".concat(v,": it is not in the index"));try{for(var p=S(f),F=p.next();!F.done;F=p.next()){var b=F.value,x=h(e,b);if(x!=null){var M=u(x.toString(),b),D=this._fieldIds[b],I=new Set(M).size;this.removeFieldLength(w,D,this._documentCount,I);try{for(var E=(i=void 0,S(M)),A=E.next();!A.done;A=E.next()){var z=A.value,L=c(z,b);if(Array.isArray(L))try{for(var _=(a=void 0,S(L)),C=_.next();!C.done;C=_.next()){var O=C.value;this.removeTerm(D,w,O)}}catch(T){a={error:T}}finally{try{C&&!C.done&&(s=_.return)&&s.call(_)}finally{if(a)throw a.error}}else L&&this.removeTerm(D,w,L)}}catch(T){i={error:T}}finally{try{A&&!A.done&&(n=E.return)&&n.call(E)}finally{if(i)throw i.error}}}}}catch(T){t={error:T}}finally{try{F&&!F.done&&(r=p.return)&&r.call(p)}finally{if(t)throw t.error}}this._storedFields.delete(w),this._documentIds.delete(w),this._idToShortId.delete(v),this._fieldLength.delete(w),this._documentCount-=1},o.prototype.removeAll=function(e){var t,r;if(e)try{for(var i=S(e),n=i.next();!n.done;n=i.next()){var a=n.value;this.remove(a)}}catch(s){t={error:s}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new pe,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}},o.prototype.discard=function(e){var t=this,r=this._idToShortId.get(e);if(r==null)throw new Error("MiniSearch: cannot discard document with ID ".concat(e,": it is not in the index"));this._idToShortId.delete(e),this._documentIds.delete(r),this._storedFields.delete(r),(this._fieldLength.get(r)||[]).forEach(function(i,n){t.removeFieldLength(r,n,t._documentCount,i)}),this._fieldLength.delete(r),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()},o.prototype.maybeAutoVacuum=function(){if(this._options.autoVacuum!==!1){var e=this._options.autoVacuum,t=e.minDirtFactor,r=e.minDirtCount,i=e.batchSize,n=e.batchWait;this.conditionalVacuum({batchSize:i,batchWait:n},{minDirtCount:r,minDirtFactor:t})}},o.prototype.discardAll=function(e){var t,r,i=this._options.autoVacuum;try{this._options.autoVacuum=!1;try{for(var n=S(e),a=n.next();!a.done;a=n.next()){var s=a.value;this.discard(s)}}catch(l){t={error:l}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}}finally{this._options.autoVacuum=i}this.maybeAutoVacuum()},o.prototype.replace=function(e){var t=this._options,r=t.idField,i=t.extractField,n=i(e,r);this.discard(n),this.add(e)},o.prototype.vacuum=function(e){return e===void 0&&(e={}),this.conditionalVacuum(e)},o.prototype.conditionalVacuum=function(e,t){var r=this;return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&t,this._enqueuedVacuum!=null?this._enqueuedVacuum:(this._enqueuedVacuum=this._currentVacuum.then(function(){var i=r._enqueuedVacuumConditions;return r._enqueuedVacuumConditions=be,r.performVacuuming(e,i)}),this._enqueuedVacuum)):this.vacuumConditionsMet(t)===!1?Promise.resolve():(this._currentVacuum=this.performVacuuming(e),this._currentVacuum)},o.prototype.performVacuuming=function(e,t){return _t(this,void 0,void 0,function(){var r,i,n,a,s,l,u,c,h,f,d,v,w,p,F,b,x,M,D,I,E,A,z,L,_;return At(this,function(C){switch(C.label){case 0:if(r=this._dirtCount,!this.vacuumConditionsMet(t))return[3,10];i=e.batchSize||Fe.batchSize,n=e.batchWait||Fe.batchWait,a=1,C.label=1;case 1:C.trys.push([1,7,8,9]),s=S(this._index),l=s.next(),C.label=2;case 2:if(l.done)return[3,6];u=R(l.value,2),c=u[0],h=u[1];try{for(f=(A=void 0,S(h)),d=f.next();!d.done;d=f.next()){v=R(d.value,2),w=v[0],p=v[1];try{for(F=(L=void 0,S(p)),b=F.next();!b.done;b=F.next())x=R(b.value,1),M=x[0],!this._documentIds.has(M)&&(p.size<=1?h.delete(w):p.delete(M))}catch(O){L={error:O}}finally{try{b&&!b.done&&(_=F.return)&&_.call(F)}finally{if(L)throw L.error}}}}catch(O){A={error:O}}finally{try{d&&!d.done&&(z=f.return)&&z.call(f)}finally{if(A)throw A.error}}return this._index.get(c).size===0&&this._index.delete(c),a%i!==0?[3,4]:[4,new Promise(function(O){return setTimeout(O,n)})];case 3:C.sent(),C.label=4;case 4:a+=1,C.label=5;case 5:return l=s.next(),[3,2];case 6:return[3,9];case 7:return D=C.sent(),I={error:D},[3,9];case 8:try{l&&!l.done&&(E=s.return)&&E.call(s)}finally{if(I)throw I.error}return[7];case 9:this._dirtCount-=r,C.label=10;case 10:return[4,null];case 11:return C.sent(),this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null,[2]}})})},o.prototype.vacuumConditionsMet=function(e){if(e==null)return!0;var t=e.minDirtCount,r=e.minDirtFactor;return t=t||xe.minDirtCount,r=r||xe.minDirtFactor,this.dirtCount>=t&&this.dirtFactor>=r},Object.defineProperty(o.prototype,"isVacuuming",{get:function(){return this._currentVacuum!=null},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"dirtCount",{get:function(){return this._dirtCount},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"dirtFactor",{get:function(){return this._dirtCount/(1+this._documentCount+this._dirtCount)},enumerable:!1,configurable:!0}),o.prototype.has=function(e){return this._idToShortId.has(e)},o.prototype.getStoredFields=function(e){var t=this._idToShortId.get(e);if(t!=null)return this._storedFields.get(t)},o.prototype.search=function(e,t){var r,i;t===void 0&&(t={});var n=this.executeQuery(e,t),a=[];try{for(var s=S(n),l=s.next();!l.done;l=s.next()){var u=R(l.value,2),c=u[0],h=u[1],f=h.score,d=h.terms,v=h.match,w=d.length||1,p={id:this._documentIds.get(c),score:f*w,terms:Object.keys(v),queryTerms:d,match:v};Object.assign(p,this._storedFields.get(c)),(t.filter==null||t.filter(p))&&a.push(p)}}catch(F){r={error:F}}finally{try{l&&!l.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}return e===o.wildcard&&t.boostDocument==null&&this._options.searchOptions.boostDocument==null||a.sort(Pe),a},o.prototype.autoSuggest=function(e,t){var r,i,n,a;t===void 0&&(t={}),t=N(N({},this._options.autoSuggestOptions),t);var s=new Map;try{for(var l=S(this.search(e,t)),u=l.next();!u.done;u=l.next()){var c=u.value,h=c.score,f=c.terms,d=f.join(" "),v=s.get(d);v!=null?(v.score+=h,v.count+=1):s.set(d,{score:h,terms:f,count:1})}}catch(D){r={error:D}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(r)throw r.error}}var w=[];try{for(var p=S(s),F=p.next();!F.done;F=p.next()){var b=R(F.value,2),v=b[0],x=b[1],h=x.score,f=x.terms,M=x.count;w.push({suggestion:v,terms:f,score:h/M})}}catch(D){n={error:D}}finally{try{F&&!F.done&&(a=p.return)&&a.call(p)}finally{if(n)throw n.error}}return w.sort(Pe),w},Object.defineProperty(o.prototype,"documentCount",{get:function(){return this._documentCount},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"termCount",{get:function(){return this._index.size},enumerable:!1,configurable:!0}),o.loadJSON=function(e,t){if(t==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(e),t)},o.getDefault=function(e){if(ge.hasOwnProperty(e))return me(ge,e);throw new Error('MiniSearch: unknown option "'.concat(e,'"'))},o.loadJS=function(e,t){var r,i,n,a,s,l,u=e.index,c=e.documentCount,h=e.nextId,f=e.documentIds,d=e.fieldIds,v=e.fieldLength,w=e.averageFieldLength,p=e.storedFields,F=e.dirtCount,b=e.serializationVersion;if(b!==1&&b!==2)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");var x=new o(t);x._documentCount=c,x._nextId=h,x._documentIds=ue(f),x._idToShortId=new Map,x._fieldIds=d,x._fieldLength=ue(v),x._avgFieldLength=w,x._storedFields=ue(p),x._dirtCount=F||0,x._index=new pe;try{for(var M=S(x._documentIds),D=M.next();!D.done;D=M.next()){var I=R(D.value,2),E=I[0],A=I[1];x._idToShortId.set(A,E)}}catch(P){r={error:P}}finally{try{D&&!D.done&&(i=M.return)&&i.call(M)}finally{if(r)throw r.error}}try{for(var z=S(u),L=z.next();!L.done;L=z.next()){var _=R(L.value,2),C=_[0],O=_[1],T=new Map;try{for(var y=(s=void 0,S(Object.keys(O))),m=y.next();!m.done;m=y.next()){var k=m.value,J=O[k];b===1&&(J=J.ds),T.set(parseInt(k,10),ue(J))}}catch(P){s={error:P}}finally{try{m&&!m.done&&(l=y.return)&&l.call(y)}finally{if(s)throw s.error}}x._index.set(C,T)}}catch(P){n={error:P}}finally{try{L&&!L.done&&(a=z.return)&&a.call(z)}finally{if(n)throw n.error}}return x},o.prototype.executeQuery=function(e,t){var r=this;if(t===void 0&&(t={}),e===o.wildcard)return this.executeWildcardQuery(t);if(typeof e!="string"){var i=N(N(N({},t),e),{queries:void 0}),n=e.queries.map(function(p){return r.executeQuery(p,i)});return this.combineResults(n,i.combineWith)}var a=this._options,s=a.tokenize,l=a.processTerm,u=a.searchOptions,c=N(N({tokenize:s,processTerm:l},u),t),h=c.tokenize,f=c.processTerm,d=h(e).flatMap(function(p){return f(p)}).filter(function(p){return!!p}),v=d.map(Nt(c)),w=v.map(function(p){return r.executeQuerySpec(p,c)});return this.combineResults(w,c.combineWith)},o.prototype.executeQuerySpec=function(e,t){var r,i,n,a,s=N(N({},this._options.searchOptions),t),l=(s.fields||this._options.fields).reduce(function(k,J){var P;return N(N({},k),(P={},P[J]=me(s.boost,J)||1,P))},{}),u=s.boostDocument,c=s.weights,h=s.maxFuzzy,f=s.bm25,d=N(N({},Ve.weights),c),v=d.fuzzy,w=d.prefix,p=this._index.get(e.term),F=this.termResults(e.term,e.term,1,p,l,u,f),b,x;if(e.prefix&&(b=this._index.atPrefix(e.term)),e.fuzzy){var M=e.fuzzy===!0?.2:e.fuzzy,D=M<1?Math.min(h,Math.round(e.term.length*M)):M;D&&(x=this._index.fuzzyGet(e.term,D))}if(b)try{for(var I=S(b),E=I.next();!E.done;E=I.next()){var A=R(E.value,2),z=A[0],L=A[1],_=z.length-e.term.length;if(_){x==null||x.delete(z);var C=w*z.length/(z.length+.3*_);this.termResults(e.term,z,C,L,l,u,f,F)}}}catch(k){r={error:k}}finally{try{E&&!E.done&&(i=I.return)&&i.call(I)}finally{if(r)throw r.error}}if(x)try{for(var O=S(x.keys()),T=O.next();!T.done;T=O.next()){var z=T.value,y=R(x.get(z),2),m=y[0],_=y[1];if(_){var C=v*z.length/(z.length+_);this.termResults(e.term,z,C,m,l,u,f,F)}}}catch(k){n={error:k}}finally{try{T&&!T.done&&(a=O.return)&&a.call(O)}finally{if(n)throw n.error}}return F},o.prototype.executeWildcardQuery=function(e){var t,r,i=new Map,n=N(N({},this._options.searchOptions),e);try{for(var a=S(this._documentIds),s=a.next();!s.done;s=a.next()){var l=R(s.value,2),u=l[0],c=l[1],h=n.boostDocument?n.boostDocument(c,"",this._storedFields.get(u)):1;i.set(u,{score:h,terms:[],match:{}})}}catch(f){t={error:f}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return i},o.prototype.combineResults=function(e,t){if(t===void 0&&(t=Se),e.length===0)return new Map;var r=t.toLowerCase();return e.reduce(zt[r])||new Map},o.prototype.toJSON=function(){var e,t,r,i,n=[];try{for(var a=S(this._index),s=a.next();!s.done;s=a.next()){var l=R(s.value,2),u=l[0],c=l[1],h={};try{for(var f=(r=void 0,S(c)),d=f.next();!d.done;d=f.next()){var v=R(d.value,2),w=v[0],p=v[1];h[w]=Object.fromEntries(p)}}catch(F){r={error:F}}finally{try{d&&!d.done&&(i=f.return)&&i.call(f)}finally{if(r)throw r.error}}n.push([u,h])}}catch(F){e={error:F}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:n,serializationVersion:2}},o.prototype.termResults=function(e,t,r,i,n,a,s,l){var u,c,h,f,d;if(l===void 0&&(l=new Map),i==null)return l;try{for(var v=S(Object.keys(n)),w=v.next();!w.done;w=v.next()){var p=w.value,F=n[p],b=this._fieldIds[p],x=i.get(b);if(x!=null){var M=x.size,D=this._avgFieldLength[b];try{for(var I=(h=void 0,S(x.keys())),E=I.next();!E.done;E=I.next()){var A=E.value;if(!this._documentIds.has(A)){this.removeTerm(b,A,t),M-=1;continue}var z=a?a(this._documentIds.get(A),t,this._storedFields.get(A)):1;if(z){var L=x.get(A),_=this._fieldLength.get(A)[b],C=Lt(L,M,this._documentCount,_,D,s),O=r*F*z*C,T=l.get(A);if(T){T.score+=O,Rt(T.terms,e);var y=me(T.match,t);y?y.push(p):T.match[t]=[p]}else l.set(A,{score:O,terms:[e],match:(d={},d[t]=[p],d)})}}}catch(m){h={error:m}}finally{try{E&&!E.done&&(f=I.return)&&f.call(I)}finally{if(h)throw h.error}}}}}catch(m){u={error:m}}finally{try{w&&!w.done&&(c=v.return)&&c.call(v)}finally{if(u)throw u.error}}return l},o.prototype.addTerm=function(e,t,r){var i=this._index.fetch(r,$e),n=i.get(e);if(n==null)n=new Map,n.set(t,1),i.set(e,n);else{var a=n.get(t);n.set(t,(a||0)+1)}},o.prototype.removeTerm=function(e,t,r){if(!this._index.has(r)){this.warnDocumentChanged(t,e,r);return}var i=this._index.fetch(r,$e),n=i.get(e);n==null||n.get(t)==null?this.warnDocumentChanged(t,e,r):n.get(t)<=1?n.size<=1?i.delete(e):n.delete(t):n.set(t,n.get(t)-1),this._index.get(r).size===0&&this._index.delete(r)},o.prototype.warnDocumentChanged=function(e,t,r){var i,n;try{for(var a=S(Object.keys(this._fieldIds)),s=a.next();!s.done;s=a.next()){var l=s.value;if(this._fieldIds[l]===t){this._options.logger("warn","MiniSearch: document with ID ".concat(this._documentIds.get(e),' has changed before removal: term "').concat(r,'" was not present in field "').concat(l,'". Removing a document after it has changed can corrupt the index!'),"version_conflict");return}}}catch(u){i={error:u}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(i)throw i.error}}},o.prototype.addDocumentId=function(e){var t=this._nextId;return this._idToShortId.set(e,t),this._documentIds.set(t,e),this._documentCount+=1,this._nextId+=1,t},o.prototype.addFields=function(e){for(var t=0;t<e.length;t++)this._fieldIds[e[t]]=t},o.prototype.addFieldLength=function(e,t,r,i){var n=this._fieldLength.get(e);n==null&&this._fieldLength.set(e,n=[]),n[t]=i;var a=this._avgFieldLength[t]||0,s=a*r+i;this._avgFieldLength[t]=s/(r+1)},o.prototype.removeFieldLength=function(e,t,r,i){if(r===1){this._avgFieldLength[t]=0;return}var n=this._avgFieldLength[t]*r-i;this._avgFieldLength[t]=n/(r-1)},o.prototype.saveStoredFields=function(e,t){var r,i,n=this._options,a=n.storeFields,s=n.extractField;if(!(a==null||a.length===0)){var l=this._storedFields.get(e);l==null&&this._storedFields.set(e,l={});try{for(var u=S(a),c=u.next();!c.done;c=u.next()){var h=c.value,f=s(t,h);f!==void 0&&(l[h]=f)}}catch(d){r={error:d}}finally{try{c&&!c.done&&(i=u.return)&&i.call(u)}finally{if(r)throw r.error}}}},o.wildcard=Symbol("*"),o}(),me=function(o,e){return Object.prototype.hasOwnProperty.call(o,e)?o[e]:void 0},zt=(ne={},ne[Se]=function(o,e){var t,r;try{for(var i=S(e.keys()),n=i.next();!n.done;n=i.next()){var a=n.value,s=o.get(a);if(s==null)o.set(a,e.get(a));else{var l=e.get(a),u=l.score,c=l.terms,h=l.match;s.score=s.score+u,s.match=Object.assign(s.match,h),Be(s.terms,c)}}}catch(f){t={error:f}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return o},ne[Qe]=function(o,e){var t,r,i=new Map;try{for(var n=S(e.keys()),a=n.next();!a.done;a=n.next()){var s=a.value,l=o.get(s);if(l!=null){var u=e.get(s),c=u.score,h=u.terms,f=u.match;Be(l.terms,h),i.set(s,{score:l.score+c,terms:l.terms,match:Object.assign(l.match,f)})}}}catch(d){t={error:d}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return i},ne[Mt]=function(o,e){var t,r;try{for(var i=S(e.keys()),n=i.next();!n.done;n=i.next()){var a=n.value;o.delete(a)}}catch(s){t={error:s}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return o},ne),Tt={k:1.2,b:.7,d:.5},Lt=function(o,e,t,r,i,n){var a=n.k,s=n.b,l=n.d,u=Math.log(1+(t-e+.5)/(e+.5));return u*(l+o*(a+1)/(o+a*(1-s+s*r/i)))},Nt=function(o){return function(e,t,r){var i=typeof o.fuzzy=="function"?o.fuzzy(e,t,r):o.fuzzy||!1,n=typeof o.prefix=="function"?o.prefix(e,t,r):o.prefix===!0;return{term:e,fuzzy:i,prefix:n}}},ge={idField:"id",extractField:function(o,e){return o[e]},tokenize:function(o){return o.split(Vt)},processTerm:function(o){return o.toLowerCase()},fields:void 0,searchOptions:void 0,storeFields:[],logger:function(o,e){typeof(console==null?void 0:console[o])=="function"&&console[o](e)},autoVacuum:!0},Ve={combineWith:Se,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:Tt},Ot={combineWith:Qe,prefix:function(o,e,t){return e===t.length-1}},Fe={batchSize:1e3,batchWait:10},be={minDirtFactor:.1,minDirtCount:20},xe=N(N({},Fe),be),Rt=function(o,e){o.includes(e)||o.push(e)},Be=function(o,e){var t,r;try{for(var i=S(e),n=i.next();!n.done;n=i.next()){var a=n.value;o.includes(a)||o.push(a)}}catch(s){t={error:s}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},Pe=function(o,e){var t=o.score,r=e.score;return r-t},$e=function(){return new Map},ue=function(o){var e,t,r=new Map;try{for(var i=S(Object.keys(o)),n=i.next();!n.done;n=i.next()){var a=n.value;r.set(parseInt(a,10),o[a])}}catch(s){e={error:s}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}return r},Vt=/[\n\r -#%-*,-/:;?@[-\]_{}\u00A0\u00A1\u00A7\u00AB\u00B6\u00B7\u00BB\u00BF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u1680\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2000-\u200A\u2010-\u2029\u202F-\u2043\u2045-\u2051\u2053-\u205F\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u3000-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]+/u;const W=o=>(lt("data-v-31fa5fb1"),o=o(),ut(),o),Bt={class:"shell"},Pt=W(()=>g("svg",{class:"search-icon",width:"18",height:"18",viewBox:"0 0 24 24","aria-hidden":"true"},[g("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[g("circle",{cx:"11",cy:"11",r:"8"}),g("path",{d:"m21 21l-4.35-4.35"})])],-1)),$t={class:"search-actions before"},Wt=["title"],Jt=W(()=>g("svg",{width:"18",height:"18",viewBox:"0 0 24 24","aria-hidden":"true"},[g("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 12H5m7 7l-7-7l7-7"})],-1)),jt=[Jt],Ut=["placeholder"],Ht={class:"search-actions"},Kt=["title"],Qt=W(()=>g("svg",{width:"18",height:"18",viewBox:"0 0 24 24","aria-hidden":"true"},[g("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 14h7v7H3zM3 3h7v7H3zm11 1h7m-7 5h7m-7 6h7m-7 5h7"})],-1)),Gt=[Qt],qt=["title"],Yt=W(()=>g("svg",{width:"18",height:"18",viewBox:"0 0 24 24","aria-hidden":"true"},[g("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 5H9l-7 7l7 7h11a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2Zm-2 4l-6 6m0-6l6 6"})],-1)),Zt=[Yt],Xt=["href","aria-label","onMouseenter"],er={class:"titles"},tr=W(()=>g("span",{class:"title-icon"},"#",-1)),rr=["innerHTML"],nr=W(()=>g("svg",{width:"18",height:"18",viewBox:"0 0 24 24"},[g("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"m9 18l6-6l-6-6"})],-1)),ir={class:"title main"},ar=["innerHTML"],or={key:0,class:"excerpt-wrapper"},sr={key:0,class:"excerpt"},lr=["innerHTML"],ur=W(()=>g("div",{class:"excerpt-gradient-bottom"},null,-1)),cr=W(()=>g("div",{class:"excerpt-gradient-top"},null,-1)),hr={key:0,class:"no-results"},fr={class:"search-keyboard-shortcuts"},dr=["aria-label"],vr=W(()=>g("svg",{width:"14",height:"14",viewBox:"0 0 24 24"},[g("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 19V5m-7 7l7-7l7 7"})],-1)),pr=[vr],yr=["aria-label"],mr=W(()=>g("svg",{width:"14",height:"14",viewBox:"0 0 24 24"},[g("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v14m7-7l-7 7l-7-7"})],-1)),gr=[mr],xr=["aria-label"],wr=W(()=>g("svg",{width:"14",height:"14",viewBox:"0 0 24 24"},[g("g",{fill:"none",stroke:"currentcolor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},[g("path",{d:"m9 10l-5 5l5 5"}),g("path",{d:"M20 4v7a4 4 0 0 1-4 4H4"})])],-1)),Fr=[wr],br=["aria-label"],Er=Xe({__name:"VPLocalSearchBox",props:{placeholder:{}},emits:["close"],setup(o,{emit:e}){var T;const t=X(),r=X(),i=X(),n=X(bt),a=pt(),{localeIndex:s,theme:l}=a,u=Re(async()=>{var y,m,k;return Oe(Dt.loadJSON((k=await((m=(y=n.value)[s.value])==null?void 0:m.call(y)))==null?void 0:k.default,{fields:["title","titles","text"],storeFields:["title","titles"],searchOptions:{fuzzy:.2,prefix:!0,boost:{title:4,text:2,titles:1}}}))}),h=De(()=>{var y,m;return((y=l.value.search)==null?void 0:y.provider)==="local"&&((m=l.value.search.options)==null?void 0:m.disableQueryPersistence)===!0}).value?ee(""):yt("vitepress:local-search-filter",""),f=mt("vitepress:local-search-detailed-list",!1),d=De(()=>{var y,m;return((y=l.value.search)==null?void 0:y.provider)==="local"&&((m=l.value.search.options)==null?void 0:m.disableDetailedView)===!0});et(()=>{d.value&&(f.value=!1)});const v=X([]),w=/<h(\d*).*?>.*?<a.*? href="#(.*?)".*?>.*?<\/a><\/h\1>/gi,p=ee(!1);ze(h,()=>{p.value=!1});const F=Re(async()=>{if(r.value)return Oe(new St(r.value))},null);gt(()=>[u.value,h.value,f.value],async([y,m,k],J,P)=>{var Ae,Ce,ke,Ie;let G=!1;if(P(()=>{G=!0}),!y)return;v.value=y.search(m).slice(0,16),p.value=!0;const Ge=k?await Promise.all(v.value.map($=>b($.id))):[];if(G)return;const he=new Map;for(const{id:$,mod:Q}of Ge){const j=Q.default??Q;if(j!=null&&j.render){const H=ct(j);H.config.warnHandler=()=>{},H.provide(ht,a);const ie=document.createElement("div");H.mount(ie);const q=ie.innerHTML.split(w);H.unmount(),q.shift();const Me=$.slice(0,$.indexOf("#"));let ae=he.get(Me);ae||(ae=new Map,he.set(Me,ae));for(let oe=0;oe<q.length;oe+=3){const Ye=q[oe+1],Ze=q[oe+2];ae.set(Ye,Ze)}}if(G)return}const _e=new Set;if(v.value=v.value.map($=>{const[Q,j]=$.id.split("#"),H=he.get(Q),ie=(H==null?void 0:H.get(j))??"";for(const q in $.match)_e.add(q);return{...$,text:ie}}),await de(),G)return;await new Promise($=>{var Q;(Q=F.value)==null||Q.unmark({done:()=>{var j;(j=F.value)==null||j.markRegExp(O(_e),{done:$})}})});const qe=((Ae=t.value)==null?void 0:Ae.querySelectorAll(".result .excerpt"))??[];for(const $ of qe)(Ce=$.querySelector('mark[data-markjs="true"]'))==null||Ce.scrollIntoView({block:"center"});(Ie=(ke=r.value)==null?void 0:ke.firstElementChild)==null||Ie.scrollIntoView({block:"start"})},{debounce:200,immediate:!0});async function b(y){const m=ft(dt(y.slice(0,y.indexOf("#"))));try{return{id:y,mod:await We(()=>import(m),[])}}catch(k){return console.error(k),{id:y,mod:{}}}}const x=ee();function M(){var y,m;(y=x.value)==null||y.focus(),(m=x.value)==null||m.select()}fe(()=>{M()});function D(y){y.pointerType==="mouse"&&M()}const I=ee(0),E=ee(!1);ze(v,()=>{I.value=0,A()});function A(){de(()=>{const y=document.querySelector(".result.selected");y&&y.scrollIntoView({block:"nearest"})})}le("ArrowUp",y=>{y.preventDefault(),I.value--,I.value<0&&(I.value=v.value.length-1),E.value=!0,A()}),le("ArrowDown",y=>{y.preventDefault(),I.value++,I.value>=v.value.length&&(I.value=0),E.value=!0,A()});const z=tt();le("Enter",()=>{const y=v.value[I.value];y&&(z.go(y.id),e("close"))}),le("Escape",()=>{e("close")});const L={modal:{displayDetails:"Display detailed list",resetButtonTitle:"Reset search",backButtonTitle:"Close search",noResultsText:"No results for",footer:{selectText:"to select",selectKeyAriaLabel:"enter",navigateText:"to navigate",navigateUpKeyAriaLabel:"up arrow",navigateDownKeyAriaLabel:"down arrow",closeText:"to close",closeKeyAriaLabel:"escape"}}},_=xt((T=l.value.search)==null?void 0:T.options,L);fe(()=>{window.history.pushState(null,"",null)}),wt("popstate",y=>{y.preventDefault(),e("close")});const C=Ft(i);fe(()=>{i.value=document.body,de(()=>{C.value=!0})}),rt(()=>{C.value=!1});function O(y){return new RegExp([...y].sort((m,k)=>k.length-m.length).map(m=>`(${m.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")})`).join("|"),"gi")}return(y,m)=>(U(),nt(st,{to:"body"},[g("div",{ref_key:"el",ref:t,class:"VPLocalSearchBox","aria-modal":"true"},[g("div",{class:"backdrop",onClick:m[0]||(m[0]=k=>y.$emit("close"))}),g("div",Bt,[g("div",{class:"search-bar",onPointerup:m[5]||(m[5]=k=>D(k))},[Pt,g("div",$t,[g("button",{class:"back-button",title:V(_)("modal.backButtonTitle"),onClick:m[1]||(m[1]=k=>y.$emit("close"))},jt,8,Wt)]),it(g("input",{ref_key:"searchInput",ref:x,"onUpdate:modelValue":m[2]||(m[2]=k=>ot(h)?h.value=k:null),placeholder:y.placeholder,class:"search-input"},null,8,Ut),[[at,V(h)]]),g("div",Ht,[d.value?se("",!0):(U(),K("button",{key:0,class:Te(["toggle-layout-button",{"detailed-list":V(f)}]),title:V(_)("modal.displayDetails"),onClick:m[3]||(m[3]=k=>f.value=!V(f))},Gt,10,Kt)),g("button",{class:"clear-button",title:V(_)("modal.resetButtonTitle"),onClick:m[4]||(m[4]=k=>h.value="")},Zt,8,qt)])],32),g("div",{ref_key:"resultsEl",ref:r,class:"results",onMousemove:m[7]||(m[7]=k=>E.value=!1)},[(U(!0),K(Ne,null,Le(v.value,(k,J)=>(U(),K("a",{key:k.id,href:k.id,class:Te(["result",{selected:I.value===J}]),"aria-label":[...k.titles,k.title].join(" > "),onMouseenter:P=>!E.value&&(I.value=J),onClick:m[6]||(m[6]=P=>y.$emit("close"))},[g("div",null,[g("div",er,[tr,(U(!0),K(Ne,null,Le(k.titles,(P,G)=>(U(),K("span",{key:G,class:"title"},[g("span",{class:"text",innerHTML:P},null,8,rr),nr]))),128)),g("span",ir,[g("span",{class:"text",innerHTML:k.title},null,8,ar)])]),V(f)?(U(),K("div",or,[k.text?(U(),K("div",sr,[g("div",{class:"vp-doc",innerHTML:k.text},null,8,lr)])):se("",!0),ur,cr])):se("",!0)])],42,Xt))),128)),V(h)&&!v.value.length&&p.value?(U(),K("div",hr,[te(re(V(_)("modal.noResultsText"))+' "',1),g("strong",null,re(V(h)),1),te('" ')])):se("",!0)],544),g("div",fr,[g("span",null,[g("kbd",{"aria-label":V(_)("modal.footer.navigateUpKeyAriaLabel")},pr,8,dr),g("kbd",{"aria-label":V(_)("modal.footer.navigateDownKeyAriaLabel")},gr,8,yr),te(" "+re(V(_)("modal.footer.navigateText")),1)]),g("span",null,[g("kbd",{"aria-label":V(_)("modal.footer.selectKeyAriaLabel")},Fr,8,xr),te(" "+re(V(_)("modal.footer.selectText")),1)]),g("span",null,[g("kbd",{"aria-label":V(_)("modal.footer.closeKeyAriaLabel")},"esc",8,br),te(" "+re(V(_)("modal.footer.closeText")),1)])])])],512)]))}});const Cr=vt(Er,[["__scopeId","data-v-31fa5fb1"]]);export{Cr as default};
