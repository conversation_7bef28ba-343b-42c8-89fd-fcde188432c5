import{_ as s,o as n,c as a,V as l}from"./chunks/framework.3d729ebc.js";const A=JSON.parse('{"title":"Vue项目结构最佳实践","description":"","frontmatter":{},"headers":[],"relativePath":"best-practices/project-structure.md","filePath":"best-practices/project-structure.md"}'),p={name:"best-practices/project-structure.md"},o=l("",50),e=[o];function t(c,r,y,F,D,i){return n(),a("div",null,e)}const E=s(p,[["render",t]]);export{A as __pageData,E as default};
