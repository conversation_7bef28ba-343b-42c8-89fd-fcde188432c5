import{_ as s,o as a,c as n,V as l}from"./chunks/framework.3d729ebc.js";const u=JSON.parse('{"title":"开发指南","description":"","frontmatter":{},"headers":[],"relativePath":"guide/index.md","filePath":"guide/index.md"}'),t={name:"guide/index.md"},e=l(`<h1 id="开发指南" tabindex="-1">开发指南 <a class="header-anchor" href="#开发指南" aria-label="Permalink to &quot;开发指南&quot;">​</a></h1><p>欢迎使用公司前端开发指南！本指南旨在帮助开发人员快速了解我们的前端技术栈、开发流程和项目结构，以便能够更快地融入团队并开始工作。</p><h2 id="技术栈" tabindex="-1">技术栈 <a class="header-anchor" href="#技术栈" aria-label="Permalink to &quot;技术栈&quot;">​</a></h2><p>我们的前端项目主要基于以下技术栈：</p><ul><li><strong>框架</strong>：<strong>Vue 2.x</strong></li><li><strong>构建工具</strong>：<strong>Webpack</strong></li><li><strong>UI组件库</strong>：<strong>Element UI</strong></li><li><strong>状态管理</strong>：<strong>Vuex</strong></li><li><strong>路由管理</strong>：<strong>Vue Router</strong></li><li><strong>HTTP请求</strong>：<strong>Axios</strong></li><li><strong>代码规范</strong>：<strong>ESLint + Prettier</strong></li><li><strong>CSS预处理器</strong>：<strong>SCSS</strong></li><li><strong>图表库</strong>：<strong>ECharts</strong></li></ul><h2 id="快速开始" tabindex="-1">快速开始 <a class="header-anchor" href="#快速开始" aria-label="Permalink to &quot;快速开始&quot;">​</a></h2><h3 id="环境准备" tabindex="-1">环境准备 <a class="header-anchor" href="#环境准备" aria-label="Permalink to &quot;环境准备&quot;">​</a></h3><p>开发前，请确保您的计算机已安装以下软件：</p><ol><li><strong>Node.js</strong>：推荐使用 node16版本</li><li><strong>npm</strong> 或 <strong>yarn</strong>：包管理工具</li><li><strong>Git</strong>：版本控制工具</li></ol><h3 id="克隆项目" tabindex="-1">克隆项目 <a class="header-anchor" href="#克隆项目" aria-label="Permalink to &quot;克隆项目&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 公司内网  </span></span>
<span class="line"><span style="color:#FFCB6B;">http://192.168.9.5:3000/</span></span></code></pre></div><h3 id="安装依赖" tabindex="-1">安装依赖 <a class="header-anchor" href="#安装依赖" aria-label="Permalink to &quot;安装依赖&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 使用npm 内部源</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;">  </span><span style="color:#C3E88D;">--registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">http://172.20.2.11:8081/repository/npm/</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 或使用yarn</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 如果有依赖冲突可使用下方指令，强行安装，不建议使用</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">install</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--legacy-peer-deps</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">--registry</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">http://172.20.2.11:8081/repository/npm/</span></span></code></pre></div><h3 id="启动开发服务器" tabindex="-1">启动开发服务器 <a class="header-anchor" href="#启动开发服务器" aria-label="Permalink to &quot;启动开发服务器&quot;">​</a></h3><div class="language-bash"><button title="Copy Code" class="copy"></button><span class="lang">bash</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#676E95;font-style:italic;"># 使用npm</span></span>
<span class="line"><span style="color:#FFCB6B;">npm</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">run</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">dev</span></span>
<span class="line"></span>
<span class="line"><span style="color:#676E95;font-style:italic;"># 或使用yarn</span></span>
<span class="line"><span style="color:#FFCB6B;">yarn</span><span style="color:#BABED8;"> </span><span style="color:#C3E88D;">dev</span></span></code></pre></div><h2 id="项目结构" tabindex="-1">项目结构 <a class="header-anchor" href="#项目结构" aria-label="Permalink to &quot;项目结构&quot;">​</a></h2><p>请查看<a href="/guide/project-structure.html">项目结构</a>页面了解详细的项目结构说明。</p><h2 id="开发流程" tabindex="-1">开发流程 <a class="header-anchor" href="#开发流程" aria-label="Permalink to &quot;开发流程&quot;">​</a></h2><p>请查看<a href="/guide/development-process.html">开发流程</a>页面了解我们的开发流程、分支管理策略和代码提交规范。</p>`,19),o=[e];function r(p,i,c,h,d,g){return a(),n("div",null,o)}const m=s(t,[["render",r]]);export{u as __pageData,m as default};
