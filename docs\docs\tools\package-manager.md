# 包管理工具指南

本文档介绍前端项目中包管理工具的使用规范，包括npm、yarn的配置和最佳实践。

## 包管理工具对比

### npm vs yarn vs pnpm

| 特性 | npm | yarn | pnpm |
|------|-----|------|------|
| 安装速度 | 中等 | 快 | 最快 |
| 磁盘空间 | 大 | 大 | 小 |
| 离线安装 | 支持 | 支持 | 支持 |
| 工作区支持 | 支持 | 支持 | 支持 |
| 锁文件 | package-lock.json | yarn.lock | pnpm-lock.yaml |

## npm 使用指南

### 基础配置

#### 1. 设置镜像源

```bash
# 查看当前源
npm config get registry

# 设置淘宝镜像
npm config set registry https://registry.npmmirror.com

# 设置公司内部源
npm config set registry http://172.20.2.11:8081/repository/npm/

# 恢复官方源
npm config set registry https://registry.npmjs.org
```

#### 2. 配置文件 .npmrc

项目根目录创建 `.npmrc` 文件：

```bash
# 镜像源配置
registry=http://172.20.2.11:8081/repository/npm/

# 安装配置
save-exact=true
package-lock=true
shrinkwrap=false

# 缓存配置
cache=/path/to/npm-cache

# 代理配置（如需要）
proxy=http://proxy.company.com:8080
https-proxy=http://proxy.company.com:8080

# 私有包配置
@company:registry=http://npm.company.internal
//npm.company.internal/:_authToken=your-auth-token
```

### 常用命令

#### 1. 包安装

```bash
# 安装所有依赖
npm install

# 安装生产依赖
npm install package-name
npm install package-name@version

# 安装开发依赖
npm install package-name --save-dev
npm install package-name -D

# 全局安装
npm install -g package-name

# 安装指定版本
npm install package-name@1.2.3

# 从特定源安装
npm install package-name --registry https://registry.npmmirror.com
```

#### 2. 包管理

```bash
# 查看已安装包
npm list
npm list --depth=0  # 只显示顶级包
npm list -g --depth=0  # 全局包

# 查看包信息
npm info package-name
npm view package-name versions --json

# 更新包
npm update
npm update package-name

# 卸载包
npm uninstall package-name
npm uninstall package-name --save-dev
```

#### 3. 脚本执行

```bash
# 运行脚本
npm run script-name
npm run dev
npm run build

# 查看可用脚本
npm run

# 传递参数
npm run build -- --mode production
```

### package.json 配置

```json
{
  "name": "project-name",
  "version": "1.0.0",
  "description": "项目描述",
  "main": "index.js",
  "scripts": {
    "dev": "vue-cli-service serve",
    "build": "vue-cli-service build",
    "lint": "vue-cli-service lint",
    "test": "jest",
    "precommit": "lint-staged"
  },
  "dependencies": {
    "vue": "^2.6.14",
    "vue-router": "^3.5.1",
    "vuex": "^3.6.2"
  },
  "devDependencies": {
    "@vue/cli-service": "^4.5.0",
    "eslint": "^7.32.0",
    "prettier": "^2.3.2"
  },
  "engines": {
    "node": ">=14.0.0",
    "npm": ">=6.0.0"
  },
  "browserslist": [
    "> 1%",
    "last 2 versions",
    "not dead"
  ]
}
```

## yarn 使用指南

### 安装和配置

```bash
# 安装yarn
npm install -g yarn

# 查看版本
yarn --version

# 设置镜像源
yarn config set registry https://registry.npmmirror.com

# 查看配置
yarn config list
```

### 常用命令

```bash
# 安装依赖
yarn install
yarn  # 简写

# 添加依赖
yarn add package-name
yarn add package-name@version
yarn add package-name --dev

# 升级依赖
yarn upgrade
yarn upgrade package-name

# 移除依赖
yarn remove package-name

# 运行脚本
yarn run script-name
yarn dev  # 可省略run

# 查看依赖树
yarn list
yarn list --depth=0
```

### yarn.lock 文件

yarn.lock 文件锁定依赖版本，确保团队成员安装相同版本的依赖：

```yaml
# yarn.lock 示例
"@babel/core@^7.12.3":
  version "7.15.5"
  resolved "https://registry.yarnpkg.com/@babel/core/-/core-7.15.5.tgz"
  integrity sha512-pYgXxiwAgQpgM1bNkZsDEq85f0ggXMA5L7c+o3tskGMh2BunCI9QUwB9Z4jpvXUOuMdyGKiGKQiRe11VS6Jzvg==
```

## 私有包管理

### 1. 发布私有包

```bash
# 登录私有仓库
npm login --registry=http://npm.company.internal

# 发布包
npm publish --registry=http://npm.company.internal

# 发布带标签的版本
npm publish --tag beta
```

### 2. 使用私有包

```bash
# 安装私有包
npm install @company/package-name --registry=http://npm.company.internal

# 在package.json中配置
{
  "dependencies": {
    "@company/ui-components": "^1.0.0"
  }
}
```

### 3. .npmrc 配置私有源

```bash
# 全局配置
@company:registry=http://npm.company.internal
//npm.company.internal/:_authToken=${NPM_TOKEN}

# 项目配置
registry=https://registry.npmjs.org
@company:registry=http://npm.company.internal
```

## 依赖管理最佳实践

### 1. 版本管理

```json
{
  "dependencies": {
    "vue": "2.6.14",        // 精确版本
    "axios": "^0.21.1",     // 兼容版本
    "lodash": "~4.17.21"    // 补丁版本
  }
}
```

版本号说明：
- `1.2.3`：精确版本
- `^1.2.3`：兼容版本（1.x.x）
- `~1.2.3`：补丁版本（1.2.x）
- `>=1.2.3`：大于等于版本

### 2. 依赖分类

```json
{
  "dependencies": {
    // 生产环境依赖
    "vue": "^2.6.14",
    "vue-router": "^3.5.1"
  },
  "devDependencies": {
    // 开发环境依赖
    "webpack": "^5.0.0",
    "eslint": "^7.32.0"
  },
  "peerDependencies": {
    // 同伴依赖
    "vue": "^2.6.0"
  },
  "optionalDependencies": {
    // 可选依赖
    "fsevents": "^2.3.2"
  }
}
```

### 3. 安全检查

```bash
# 检查安全漏洞
npm audit

# 自动修复
npm audit fix

# 强制修复
npm audit fix --force

# 查看详细信息
npm audit --json
```

### 4. 清理和优化

```bash
# 清理缓存
npm cache clean --force

# 清理node_modules
rm -rf node_modules package-lock.json
npm install

# 检查过期包
npm outdated

# 查看包大小
npm ls --depth=0 --json | jq '.dependencies | to_entries | map({name: .key, version: .value.version})'
```

## 工作区管理

### 1. npm workspaces

```json
{
  "name": "monorepo",
  "workspaces": [
    "packages/*",
    "apps/*"
  ]
}
```

```bash
# 安装所有工作区依赖
npm install

# 在特定工作区运行命令
npm run build --workspace=package-a

# 在所有工作区运行命令
npm run test --workspaces
```

### 2. yarn workspaces

```json
{
  "name": "monorepo",
  "private": true,
  "workspaces": {
    "packages": [
      "packages/*"
    ]
  }
}
```

```bash
# 添加依赖到特定工作区
yarn workspace package-a add lodash

# 运行工作区命令
yarn workspace package-a run build
```

## 常见问题解决

### 1. 依赖冲突

```bash
# 查看依赖树
npm ls package-name

# 强制解析
npm install --legacy-peer-deps

# 使用resolutions（yarn）
{
  "resolutions": {
    "package-name": "1.2.3"
  }
}
```

### 2. 网络问题

```bash
# 增加超时时间
npm config set timeout 60000

# 使用代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# 跳过SSL验证（不推荐）
npm config set strict-ssl false
```

### 3. 权限问题

```bash
# 修改npm全局目录
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'

# 添加到PATH
export PATH=~/.npm-global/bin:$PATH
```

## 团队协作规范

### 1. 锁文件管理

- 始终提交锁文件（package-lock.json、yarn.lock）
- 不要手动修改锁文件
- 团队使用相同的包管理工具

### 2. 依赖更新策略

- 定期更新依赖包
- 重大版本更新需要团队讨论
- 使用自动化工具检查过期依赖

### 3. 私有包发布流程

1. 版本号遵循语义化版本规范
2. 编写详细的CHANGELOG
3. 进行充分的测试
4. 使用CI/CD自动发布

通过规范的包管理，可以确保项目依赖的稳定性和团队开发的一致性。
